const { Sequelize } = require('sequelize');

// Load database config
const dbConfig = require('./config/database.js');

// Create Sequelize instance
const sequelize = new Sequelize(dbConfig.development);

async function queryAccounts() {
  try {
    console.log('🔍 Querying accounts from PostgreSQL...\n');

    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful\n');

    // Query all accounts
    const accounts = await sequelize.query(`
      SELECT id, email, full_name, contact_number, is_verified, created_at
      FROM client_accounts
      ORDER BY created_at DESC;
    `, { type: Sequelize.QueryTypes.SELECT });

    console.log('👥 All registered accounts:');
    console.log('==========================');

    if (accounts.length === 0) {
      console.log('No accounts found in the database.');
    } else {
      accounts.forEach((account, index) => {
        console.log(`${index + 1}. ID: ${account.id}`);
        console.log(`   Email: ${account.email}`);
        console.log(`   Full Name: ${account.full_name || 'N/A'}`);
        console.log(`   Contact: ${account.contact_number || 'N/A'}`);
        console.log(`   Verified: ${account.is_verified ? 'Yes' : 'No'}`);
        console.log(`   Created: ${new Date(account.created_at).toLocaleString()}`);
        console.log('');
      });
    }

    console.log(`Total accounts: ${accounts.length}`);

  } catch (error) {
    console.error('❌ Query failed:', error.message);
  } finally {
    await sequelize.close();
  }
}

// Run the query
queryAccounts();
