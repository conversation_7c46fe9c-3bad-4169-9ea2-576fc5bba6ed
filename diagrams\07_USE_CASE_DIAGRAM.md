# Diagram 7: Use Case Diagram

## Overview
This use case diagram shows all user interactions with the Smart Governance User Module system, including authentication, profile management, business operations, application workflows, document management, notifications, and feedback submission.

---

## Actors
- **User (Business Owner):** Primary actor who interacts with the system
- **Email System:** External system for sending OTP emails
- **File System:** External system for storing uploaded documents

---

## Mermaid Use Case Diagram

```mermaid
graph TB
    subgraph "Smart Governance User Module System"
        subgraph "Authentication"
            UC1[Register Account]
            UC2[Verify Email with OTP]
            UC3[Login to System]
            UC4[Logout from System]
        end
        
        subgraph "Profile Management"
            UC5[View Profile]
            UC6[Update Profile Information]
            UC7[Change Password]
        end
        
        subgraph "Business Profile Management"
            UC8[Create Business Profile]
            UC9[View Business Profiles]
            UC10[Edit Business Profile]
        end
        
        subgraph "Permit Application"
            UC11[Create New Application]
            UC12[Create Renewal Application]
            UC13[View Application Status]
            UC14[Submit Application for Review]
        end
        
        subgraph "Document Management"
            UC15[Upload Document]
            UC16[View Uploaded Documents]
            UC17[Delete Document]
        end
        
        subgraph "Notifications"
            UC18[View Notifications]
            UC19[Mark Notification as Read]
            UC20[Mark All Notifications as Read]
            UC21[Filter Notifications]
        end
        
        subgraph "Feedback System"
            UC22[Submit Feedback]
            UC23[View Feedback Statistics]
            UC24[View My Feedback History]
        end
    end
    
    %% External Systems
    EmailSys[Email System<br/>Nodemailer]
    FileSys[File System<br/>Local Storage]
    
    %% Actor
    User((User<br/>Business Owner))
    
    %% Authentication Relationships
    User -->|performs| UC1
    User -->|performs| UC2
    User -->|performs| UC3
    User -->|performs| UC4
    
    UC1 -.->|includes| EmailSys
    UC2 -.->|uses| EmailSys
    
    %% Profile Management Relationships
    User -->|performs| UC5
    User -->|performs| UC6
    User -->|performs| UC7
    
    %% Business Profile Relationships
    User -->|performs| UC8
    User -->|performs| UC9
    User -->|performs| UC10
    
    %% Application Relationships
    User -->|performs| UC11
    User -->|performs| UC12
    User -->|performs| UC13
    User -->|performs| UC14
    
    UC11 -.->|requires| UC8
    UC12 -.->|requires| UC8
    UC14 -.->|requires| UC15
    
    %% Document Relationships
    User -->|performs| UC15
    User -->|performs| UC16
    User -->|performs| UC17
    
    UC15 -.->|uses| FileSys
    UC17 -.->|uses| FileSys
    
    %% Notification Relationships
    User -->|performs| UC18
    User -->|performs| UC19
    User -->|performs| UC20
    User -->|performs| UC21
    
    %% Feedback Relationships
    User -->|performs| UC22
    User -->|performs| UC23
    User -->|performs| UC24
    
    %% Styling
    classDef actor fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef usecase fill:#10b981,stroke:#059669,color:#fff
    classDef external fill:#f59e0b,stroke:#d97706,color:#fff
    
    class User actor
    class UC1,UC2,UC3,UC4,UC5,UC6,UC7,UC8,UC9,UC10,UC11,UC12,UC13,UC14,UC15,UC16,UC17,UC18,UC19,UC20,UC21,UC22,UC23,UC24 usecase
    class EmailSys,FileSys external
```

---

## Use Case Descriptions

### Authentication Use Cases

#### UC1: Register Account
- **Actor:** User
- **Precondition:** None
- **Description:** User creates a new account with email, password, and full name
- **Postcondition:** Account created, OTP sent to email
- **Includes:** Send OTP Email

#### UC2: Verify Email with OTP
- **Actor:** User
- **Precondition:** User has registered and received OTP
- **Description:** User enters 6-digit OTP to verify email address
- **Postcondition:** Email verified, account activated

#### UC3: Login to System
- **Actor:** User
- **Precondition:** User has verified account
- **Description:** User logs in with email and password
- **Postcondition:** JWT token generated, user authenticated

#### UC4: Logout from System
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User logs out of the system
- **Postcondition:** JWT token removed, session ended

---

### Profile Management Use Cases

#### UC5: View Profile
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User views their profile information
- **Postcondition:** Profile data displayed

#### UC6: Update Profile Information
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User updates full name and contact number
- **Postcondition:** Profile updated in database

#### UC7: Change Password
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User changes password with current password verification
- **Postcondition:** Password updated, notification sent

---

### Business Profile Management Use Cases

#### UC8: Create Business Profile
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User creates a business profile with name, type, address, contact, TIN
- **Postcondition:** Business profile created

#### UC9: View Business Profiles
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User views all their business profiles
- **Postcondition:** List of business profiles displayed

#### UC10: Edit Business Profile
- **Actor:** User
- **Precondition:** User owns the business profile
- **Description:** User updates business profile information
- **Postcondition:** Business profile updated

---

### Permit Application Use Cases

#### UC11: Create New Application
- **Actor:** User
- **Precondition:** User has at least one business profile
- **Description:** User creates a new permit application
- **Postcondition:** Application created in draft status
- **Extends:** Requires business profile (UC8)

#### UC12: Create Renewal Application
- **Actor:** User
- **Precondition:** User has at least one business profile
- **Description:** User creates a renewal permit application
- **Postcondition:** Renewal application created in draft status
- **Extends:** Requires business profile (UC8)

#### UC13: View Application Status
- **Actor:** User
- **Precondition:** User has created applications
- **Description:** User views status of all applications
- **Postcondition:** Application list with statuses displayed

#### UC14: Submit Application for Review
- **Actor:** User
- **Precondition:** Application is in draft status, documents uploaded
- **Description:** User submits application for admin review
- **Postcondition:** Application status changed to 'submitted', notification sent
- **Extends:** Requires document upload (UC15)

---

### Document Management Use Cases

#### UC15: Upload Document
- **Actor:** User
- **Precondition:** Application is in draft status
- **Description:** User uploads required documents (PDF, JPG, PNG, max 5MB)
- **Postcondition:** Document saved to file system and database
- **Uses:** File System

#### UC16: View Uploaded Documents
- **Actor:** User
- **Precondition:** User has uploaded documents
- **Description:** User views all documents for an application
- **Postcondition:** Document list displayed

#### UC17: Delete Document
- **Actor:** User
- **Precondition:** Application is in draft status, user owns document
- **Description:** User deletes an uploaded document
- **Postcondition:** Document removed from file system and database
- **Uses:** File System

---

### Notification Use Cases

#### UC18: View Notifications
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User views all notifications
- **Postcondition:** Notification list displayed

#### UC19: Mark Notification as Read
- **Actor:** User
- **Precondition:** User has unread notifications
- **Description:** User marks a single notification as read
- **Postcondition:** Notification marked as read, unread count updated

#### UC20: Mark All Notifications as Read
- **Actor:** User
- **Precondition:** User has unread notifications
- **Description:** User marks all notifications as read
- **Postcondition:** All notifications marked as read

#### UC21: Filter Notifications
- **Actor:** User
- **Precondition:** User has notifications
- **Description:** User filters notifications by read/unread status
- **Postcondition:** Filtered notification list displayed

---

### Feedback System Use Cases

#### UC22: Submit Feedback
- **Actor:** User
- **Precondition:** User is logged in
- **Description:** User submits feedback with star rating (1-5) and optional comments
- **Postcondition:** Feedback saved to database

#### UC23: View Feedback Statistics
- **Actor:** User
- **Precondition:** Feedback exists in system
- **Description:** User views average rating and rating distribution
- **Postcondition:** Statistics displayed

#### UC24: View My Feedback History
- **Actor:** User
- **Precondition:** User has submitted feedback
- **Description:** User views their own feedback submissions
- **Postcondition:** Feedback history displayed

---

## Use Case Relationships

### Include Relationships
- **UC1 (Register)** includes **Send OTP Email**
- **UC2 (Verify OTP)** uses **Email System**

### Extend Relationships
- **UC11 (New Application)** requires **UC8 (Create Business Profile)**
- **UC12 (Renewal Application)** requires **UC8 (Create Business Profile)**
- **UC14 (Submit Application)** requires **UC15 (Upload Document)**

### Dependency Relationships
- **UC15 (Upload Document)** uses **File System**
- **UC17 (Delete Document)** uses **File System**

---

## External Systems

### Email System (Nodemailer)
- **Purpose:** Send OTP emails for registration and password reset
- **Current Mode:** Console.log (demo)
- **Production:** SMTP configuration required

### File System (Local Storage)
- **Purpose:** Store uploaded documents
- **Location:** `uploads/documents/`
- **File Types:** PDF, JPG, PNG
- **Max Size:** 5MB per file

---

## User Permissions

### All Users Can:
- ✅ Register and verify email
- ✅ Login and logout
- ✅ Manage their own profile
- ✅ Create and manage business profiles
- ✅ Create and submit applications
- ✅ Upload and delete documents (draft only)
- ✅ View notifications
- ✅ Submit feedback

### Users Cannot:
- ❌ Access other users' data
- ❌ Modify submitted applications
- ❌ Delete documents from submitted applications
- ❌ Review or approve applications (admin feature)
- ❌ Access admin dashboard (admin feature)

---

**Diagram Status:** ✅ Complete  
**Total Use Cases:** 24  
**Actors:** 1 (User)  
**External Systems:** 2 (Email, File System)  
**Last Updated:** November 20, 2025

