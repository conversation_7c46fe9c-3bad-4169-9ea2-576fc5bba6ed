-- PostgreSQL Sample Users Seed Script
-- BSCS Mini-Thesis Project - Holy Trinity College PCMRC
-- Created: November 19, 2025
-- Purpose: Seed sample government users for testing Smart Governance Portal
-- Security: Uses bcrypt-hashed passwords, realistic government email domains
-- Standards: PostgreSQL 16+ syntax with UUID generation and proper timestamps

-- Enable required extensions (should already be enabled from schema)
SELECT 'Seeding sample users for Smart Governance Portal...' as status;

-- Insert sample verified users for testing (government roles)
INSERT INTO client_accounts (id, email, password_hash, full_name, contact_number, is_verified, metadata, created_at, updated_at)
VALUES
(
    uuid_generate_v4(),
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- Password: "AdminPass123!"
    'System Administrator',
    '+************',
    TRUE,
    '{"department": "IT", "position": "System Administrator", "clearance_level": "admin", "employee_id": "ADM001"}'::jsonb,
    '2025-11-19 08:00:00+00',
    '2025-11-19 08:00:00+00'
),
(
    uuid_generate_v4(),
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- Password: "AdminPass123!"
    'Business Permit Clerk',
    '+************',
    TRUE,
    '{"department": "BPLO", "position": "Permit Processing Clerk", "clearance_level": "standard", "employee_id": "CLK001"}'::jsonb,
    '2025-11-19 08:15:00+00',
    '2025-11-19 08:15:00+00'
),
(
    uuid_generate_v4(),
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- Password: "AdminPass123!"
    'Permit Inspector',
    '+************',
    TRUE,
    '{"department": "BPLO", "position": "Field Inspector", "clearance_level": "inspector", "employee_id": "INS001"}'::jsonb,
    '2025-11-19 08:30:00+00',
    '2025-11-19 08:30:00+00'
);

-- Insert sample unverified user (for testing OTP registration flow)
INSERT INTO client_accounts (id, email, password_hash, full_name, contact_number, is_verified, metadata, created_at, updated_at)
VALUES
(
    uuid_generate_v4(),
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- Password: "AdminPass123!"
    'New Applicant',
    '+************',
    FALSE,
    '{"department": "Applicant", "position": "Business Owner", "clearance_level": "pending", "application_type": "new_business"}'::jsonb,
    '2025-11-19 09:00:00+00',
    '2025-11-19 09:00:00+00'
);

-- Insert sample OTP for the unverified user (for testing OTP verification)
INSERT INTO otp_verification (id, client_id, otp_code, expires_at, is_used, purpose, attempts, created_at, updated_at)
SELECT
    uuid_generate_v4(),
    c.id,
    '123456',
    CURRENT_TIMESTAMP + INTERVAL '5 minutes',
    FALSE,
    'Registration'::otp_purpose,
    0,
    '2025-11-19 09:00:00+00',
    '2025-11-19 09:00:00+00'
FROM client_accounts c
WHERE c.email = '<EMAIL>';

-- Verification queries
SELECT 'Sample users created successfully!' as status;
SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_verified THEN 1 END) as verified_users,
    COUNT(CASE WHEN NOT is_verified THEN 1 END) as unverified_users
FROM client_accounts;

SELECT
    COUNT(*) as active_otps,
    COUNT(CASE WHEN purpose = 'Registration' THEN 1 END) as registration_otps
FROM otp_verification
WHERE is_used = FALSE AND expires_at > CURRENT_TIMESTAMP;

-- Display sample user credentials for testing
SELECT
    '🎯 SAMPLE USER CREDENTIALS (for testing only):' as info,
    '📧 <EMAIL> | 🔑 AdminPass123!' as admin_credentials,
    '📧 <EMAIL> | 🔑 AdminPass123!' as clerk_credentials,
    '📧 <EMAIL> | 🔑 AdminPass123!' as inspector_credentials,
    '📧 <EMAIL> | 🔑 AdminPass123! | 📱 OTP: 123456' as new_user_credentials;

-- Show user details with metadata
SELECT
    email,
    full_name,
    is_verified,
    metadata->>'department' as department,
    metadata->>'position' as position,
    metadata->>'clearance_level' as clearance_level
FROM client_accounts
ORDER BY created_at;

-- Show OTP details
SELECT
    c.email,
    ov.otp_code,
    ov.expires_at,
    ov.purpose,
    ov.attempts
FROM otp_verification ov
JOIN client_accounts c ON ov.client_id = c.id
WHERE ov.is_used = FALSE AND ov.expires_at > CURRENT_TIMESTAMP;

-- Academic documentation note
SELECT 'PostgreSQL seed data created for BSCS thesis testing.' as academic_note;
SELECT 'All sample users use bcrypt-hashed passwords for security.' as security_note;
SELECT 'Government email domains (@gensantos.gov.ph) used for realism.' as domain_note;
