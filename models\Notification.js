// Sequelize Model for Notification - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define Notification model for user notifications
// Security: UUID primary keys, ENUM validation, read status tracking
// Standards: Sequelize v6+ conventions with PostgreSQL data types

const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const Notification = sequelize.define('Notification', {
    // Primary key: UUID v4 for secure identifiers
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique notification identifier (UUID v4)',
    },
    // Foreign key to client_accounts table
    client_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'client_accounts',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Reference to user account',
    },
    // Notification title
    title: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: {
          args: [1, 255],
          msg: 'Title must be between 1 and 255 characters',
        },
        notEmpty: {
          msg: 'Title is required',
        },
      },
      comment: 'Notification title',
    },
    // Notification message
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Message is required',
        },
      },
      comment: 'Notification message content',
    },
    // Notification type with ENUM
    type: {
      type: DataTypes.ENUM('info', 'success', 'warning', 'error'),
      allowNull: false,
      defaultValue: 'info',
      validate: {
        isIn: {
          args: [['info', 'success', 'warning', 'error']],
          msg: 'Type must be one of: info, success, warning, error',
        },
      },
      comment: 'Notification type for UI styling',
    },
    // Read status flag
    is_read: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether notification has been read',
    },
    // Additional metadata for flexible extensions
    metadata: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
      comment: 'JSONB field for flexible metadata (action URL, related entity IDs, etc.)',
    },
  }, {
    // Table configuration
    tableName: 'notifications',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Foreign key index for performance
      {
        fields: ['client_id'],
        name: 'idx_notifications_client_id',
        using: 'btree',
      },
      // Read status filtering index
      {
        fields: ['is_read'],
        name: 'idx_notifications_is_read',
        using: 'btree',
      },
      // Composite index for unread notifications query
      {
        fields: ['client_id', 'is_read'],
        name: 'idx_notifications_client_is_read',
        using: 'btree',
      },
      // Creation date index for sorting
      {
        fields: ['created_at'],
        name: 'idx_notifications_created_at',
        using: 'btree',
      },
      // GIN index for JSONB metadata queries
      {
        fields: ['metadata'],
        name: 'idx_notifications_metadata',
        using: 'gin',
      },
    ],
  });

  // Instance method to mark as read
  Notification.prototype.markAsRead = async function() {
    if (this.is_read) {
      return this; // Already read
    }
    return await this.update({ is_read: true });
  };

  // Instance method to mark as unread
  Notification.prototype.markAsUnread = async function() {
    if (!this.is_read) {
      return this; // Already unread
    }
    return await this.update({ is_read: false });
  };

  // Static method to find unread notifications for a client
  Notification.findUnreadByClient = async function(clientId) {
    return await this.findAll({
      where: {
        client_id: clientId,
        is_read: false,
      },
      order: [['created_at', 'DESC']],
    });
  };

  // Static method to find all notifications for a client
  Notification.findByClient = async function(clientId, limit = 50) {
    return await this.findAll({
      where: { client_id: clientId },
      order: [['created_at', 'DESC']],
      limit: limit,
    });
  };

  // Static method to count unread notifications
  Notification.countUnread = async function(clientId) {
    return await this.count({
      where: {
        client_id: clientId,
        is_read: false,
      },
    });
  };

  // Static method to mark all as read for a client
  Notification.markAllAsRead = async function(clientId) {
    return await this.update(
      { is_read: true },
      {
        where: {
          client_id: clientId,
          is_read: false,
        },
      }
    );
  };

  // Static method to create notification
  Notification.createNotification = async function(clientId, title, message, type = 'info', metadata = {}) {
    return await this.create({
      client_id: clientId,
      title: title,
      message: message,
      type: type,
      metadata: metadata,
    });
  };

  return Notification;
};

