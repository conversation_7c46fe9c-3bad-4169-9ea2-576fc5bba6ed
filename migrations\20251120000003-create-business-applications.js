// Migration: Create business_applications table - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Create business_applications table with UUID primary key and ENUM types

'use strict';

export default {
  up: async (queryInterface, Sequelize) => {
    // Create ENUM types
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_business_applications_application_type AS ENUM ('new', 'renewal');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_business_applications_status AS ENUM ('draft', 'submitted', 'under_review', 'approved', 'rejected');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryInterface.createTable('business_applications', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        comment: 'Unique application identifier (UUID v4)',
      },
      business_profile_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'business_profiles',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Reference to business profile',
      },
      application_type: {
        type: Sequelize.ENUM('new', 'renewal'),
        allowNull: false,
        comment: 'Type of application (new permit or renewal)',
      },
      status: {
        type: Sequelize.ENUM('draft', 'submitted', 'under_review', 'approved', 'rejected'),
        allowNull: false,
        defaultValue: 'draft',
        comment: 'Current status of the application',
      },
      submitted_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Timestamp when application was submitted',
      },
      reviewed_at: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Timestamp when application was reviewed',
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Admin notes or rejection reasons',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'JSONB field for flexible metadata',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('business_applications', ['business_profile_id'], {
      name: 'idx_business_applications_business_profile_id',
      using: 'btree',
    });

    await queryInterface.addIndex('business_applications', ['status'], {
      name: 'idx_business_applications_status',
      using: 'btree',
    });

    await queryInterface.addIndex('business_applications', ['business_profile_id', 'status'], {
      name: 'idx_business_applications_profile_status',
      using: 'btree',
    });

    await queryInterface.addIndex('business_applications', ['submitted_at'], {
      name: 'idx_business_applications_submitted_at',
      using: 'btree',
    });

    await queryInterface.addIndex('business_applications', ['metadata'], {
      name: 'idx_business_applications_metadata',
      using: 'gin',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('business_applications');
    
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_business_applications_application_type;
    `);

    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_business_applications_status;
    `);
  },
};

