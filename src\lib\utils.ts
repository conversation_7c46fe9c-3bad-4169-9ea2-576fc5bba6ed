import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export interface PasswordStrength {
  score: number
  label: string
  color: string
  percentage: number
}

export function calculatePasswordStrength(password: string): PasswordStrength {
  let score = 0
  
  if (!password) {
    return { score: 0, label: 'Too weak', color: 'bg-gray-300', percentage: 0 }
  }

  if (password.length >= 8) score += 1
  if (password.length >= 12) score += 1
  if (/[a-z]/.test(password)) score += 1
  if (/[A-Z]/.test(password)) score += 1
  if (/[0-9]/.test(password)) score += 1
  if (/[^a-zA-Z0-9]/.test(password)) score += 1

  const percentage = (score / 6) * 100

  if (score <= 2) {
    return { score, label: 'Weak', color: 'bg-red-500', percentage }
  } else if (score <= 4) {
    return { score, label: 'Medium', color: 'bg-yellow-500', percentage }
  } else {
    return { score, label: 'Strong', color: 'bg-green-500', percentage }
  }
}

export function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

