import React, { useState, useEffect } from 'react'
import { useAuth } from '@/context/AuthContext'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import {
  LogOut, User, Building2, FileText, Bell, MessageSquare,
  ArrowRight, CheckCircle
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { notificationService } from '@/services/notificationService'

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    fetchUnreadCount()
  }, [])

  const fetchUnreadCount = async () => {
    try {
      const response = await notificationService.getUnreadCount()
      setUnreadCount(response.unread_count)
    } catch (error) {
      console.error('Failed to fetch unread count:', error)
    }
  }

  const handleLogout = () => {
    logout()
    navigate('/login', { replace: true })
  }

  const menuItems = [
    {
      title: 'Profile Settings',
      description: 'Manage your account information',
      icon: User,
      path: '/profile',
      color: 'from-blue-500 to-blue-600',
    },
    {
      title: 'Business Profiles',
      description: 'Manage your business information',
      icon: Building2,
      path: '/business-profile',
      color: 'from-purple-500 to-purple-600',
    },
    {
      title: 'New Application',
      description: 'Apply for a business permit',
      icon: FileText,
      path: '/applications/new',
      color: 'from-green-500 to-green-600',
    },
    {
      title: 'Application Status',
      description: 'Track your permit applications',
      icon: CheckCircle,
      path: '/applications',
      color: 'from-orange-500 to-orange-600',
    },
    {
      title: 'Notifications',
      description: `${unreadCount > 0 ? `${unreadCount} unread notification${unreadCount > 1 ? 's' : ''}` : 'View all notifications'}`,
      icon: Bell,
      path: '/notifications',
      color: 'from-red-500 to-red-600',
      badge: unreadCount > 0 ? unreadCount : undefined,
    },
    {
      title: 'Feedback',
      description: 'Share your experience with us',
      icon: MessageSquare,
      path: '/feedback',
      color: 'from-pink-500 to-pink-600',
    },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation Bar */}
      <nav className="bg-white/80 backdrop-blur-sm shadow-md sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                General Santos City
              </h1>
              <p className="text-xs text-gray-600">Smart Governance Portal</p>
            </div>
            <div className="flex items-center gap-3">
              <Button onClick={() => navigate('/notifications')} variant="ghost" size="sm" className="relative" aria-label="Notifications">
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {unreadCount}
                  </span>
                )}
              </Button>
              <Button onClick={handleLogout} variant="outline" size="sm">
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="shadow-xl border-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
            <CardContent className="p-8">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 rounded-full">
                  <User className="h-8 w-8" />
                </div>
                <div>
                  <h2 className="text-3xl font-bold">Welcome back, {user?.fullName}!</h2>
                  <p className="text-blue-100 mt-1">
                    Business Permit Processing & Renewal System
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="p-4 bg-white/10 backdrop-blur-sm rounded-lg">
                  <p className="text-sm text-blue-100 mb-1">Email Address</p>
                  <p className="text-lg font-medium">{user?.email}</p>
                </div>
                <div className="p-4 bg-white/10 backdrop-blur-sm rounded-lg">
                  <p className="text-sm text-blue-100 mb-1">Account Status</p>
                  <p className="text-lg font-medium flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    Verified
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Actions */}
        <div className="mb-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {menuItems.map((item, index) => (
              <motion.div
                key={item.path}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card
                  className="shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-all cursor-pointer group"
                  onClick={() => navigate(item.path)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className={`p-3 rounded-lg bg-gradient-to-r ${item.color}`}>
                        <item.icon className="h-6 w-6 text-white" />
                      </div>
                      {item.badge && (
                        <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">{item.title}</h4>
                    <p className="text-sm text-gray-600 mb-4">{item.description}</p>
                    <div className="flex items-center text-sm font-medium text-blue-600 group-hover:text-blue-700">
                      Go to {item.title}
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* System Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>System Information</CardTitle>
              <CardDescription>
                Smart Governance Through a Web-Based Business Permit Processing and Renewal System
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Features</h4>
                  <ul className="space-y-1 text-gray-700">
                    <li>✅ Business Profile Management</li>
                    <li>✅ Online Permit Application</li>
                    <li>✅ Document Upload System</li>
                    <li>✅ Real-time Status Tracking</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Security</h4>
                  <ul className="space-y-1 text-gray-700">
                    <li>✅ JWT Authentication</li>
                    <li>✅ OTP Email Verification</li>
                    <li>✅ Secure File Upload</li>
                    <li>✅ Data Encryption</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </main>
    </div>
  )
}

export default Dashboard

