// Feedback Controller - Feedback Submission Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Handle feedback submission operations (GET, POST)

const { FeedbackSubmission, BusinessApplication, BusinessProfile } = require('../models');

/**
 * Submit feedback
 * @route POST /api/feedback
 * @access Private
 */
const submitFeedback = async (req, res) => {
  try {
    const { application_id, rating, comments } = req.body;

    // Validation
    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        success: false,
        message: 'Rating must be between 1 and 5.',
      });
    }

    // If application_id is provided, verify it belongs to user
    if (application_id) {
      const application = await BusinessApplication.findOne({
        where: { id: application_id },
        include: [
          {
            model: BusinessProfile,
            as: 'businessProfile',
            where: { client_id: req.user.userId },
          },
        ],
      });

      if (!application) {
        return res.status(404).json({
          success: false,
          message: 'Application not found.',
        });
      }
    }

    // Create feedback
    const feedback = await FeedbackSubmission.create({
      client_id: req.user.userId,
      application_id: application_id || null,
      rating: parseInt(rating),
      comments: comments?.trim() || null,
      submitted_at: new Date(),
      metadata: {},
    });

    res.status(201).json({
      success: true,
      message: 'Feedback submitted successfully. Thank you for your feedback!',
      data: feedback,
    });
  } catch (error) {
    console.error('Submit feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit feedback.',
    });
  }
};

/**
 * Get feedback submissions for authenticated user
 * @route GET /api/feedback
 * @access Private
 */
const getFeedback = async (req, res) => {
  try {
    const feedback = await FeedbackSubmission.findAll({
      where: { client_id: req.user.userId },
      include: [
        {
          model: BusinessApplication,
          as: 'application',
          attributes: ['id', 'application_type', 'status'],
          required: false,
        },
      ],
      order: [['submitted_at', 'DESC']],
    });

    res.status(200).json({
      success: true,
      data: feedback,
    });
  } catch (error) {
    console.error('Get feedback error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve feedback.',
    });
  }
};

/**
 * Get feedback statistics (for display purposes)
 * @route GET /api/feedback/stats
 * @access Private
 */
const getFeedbackStats = async (req, res) => {
  try {
    const averageRating = await FeedbackSubmission.getAverageRating();
    const distribution = await FeedbackSubmission.getRatingDistribution();

    res.status(200).json({
      success: true,
      data: {
        average_rating: averageRating.average,
        total_submissions: averageRating.count,
        rating_distribution: distribution,
      },
    });
  } catch (error) {
    console.error('Get feedback stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve feedback statistics.',
    });
  }
};

module.exports = {
  submitFeedback,
  getFeedback,
  getFeedbackStats,
};

