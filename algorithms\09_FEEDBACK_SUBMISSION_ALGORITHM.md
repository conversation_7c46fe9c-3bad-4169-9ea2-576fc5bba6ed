# Algorithm 9: Feedback Submission and Statistics

## Overview
This algorithm handles user feedback submission with star ratings (1-5), calculates feedback statistics, and generates rating distribution charts for the Smart Governance system.

---

## Algorithm Complexity
- **Time Complexity:** O(1) for submission, O(n) for statistics where n = total feedback count
- **Space Complexity:** O(1) for submission, O(1) for statistics (fixed 5 rating levels)
- **Database Operations:** 1 INSERT for submission, 2-3 SELECT for statistics

---

## Input Parameters
```typescript
interface FeedbackSubmissionInput {
  client_account_id: UUID;       // From JWT token
  application_id?: UUID;         // Optional: link to specific application
  rating: number;                // Required: 1-5 stars
  comments?: string;             // Optional: max 1000 characters
}
```

## Output
```typescript
interface FeedbackSubmissionOutput {
  success: boolean;
  message: string;
  feedback?: {
    id: UUID;
    rating: number;
    comments: string;
    created_at: Date;
  };
}

interface FeedbackStatsOutput {
  success: boolean;
  stats: {
    average_rating: number;
    total_submissions: number;
    distribution: {
      1: number;
      2: number;
      3: number;
      4: number;
      5: number;
    };
  };
}
```

---

## Pseudocode

```
ALGORITHM SubmitFeedback(client_account_id, application_id, rating, comments)
BEGIN
  // Step 1: Validate Rating
  IF rating is empty OR rating < 1 OR rating > 5 THEN
    RETURN error("Rating must be between 1 and 5")
  END IF
  
  IF NOT is_integer(rating) THEN
    RETURN error("Rating must be a whole number")
  END IF
  
  // Step 2: Validate Comments Length (if provided)
  IF comments is NOT empty AND comments.length > 1000 THEN
    RETURN error("Comments must not exceed 1000 characters")
  END IF
  
  // Step 3: Validate Application ID (if provided)
  IF application_id is NOT empty THEN
    application ← DATABASE.query(
      "SELECT a.*, bp.client_account_id
       FROM business_applications a
       JOIN business_profiles bp ON a.business_profile_id = bp.id
       WHERE a.id = ?",
      application_id
    )
    
    IF application NOT EXISTS THEN
      RETURN error(404, "Application not found")
    END IF
    
    IF application.client_account_id ≠ client_account_id THEN
      RETURN error(403, "Access denied")
    END IF
    
    IF application.status NOT IN ['submitted', 'under_review', 'approved', 'rejected'] THEN
      RETURN error("Can only provide feedback for submitted applications")
    END IF
  END IF
  
  // Step 4: Generate UUID
  feedback_id ← UUID.generate_v4()
  
  // Step 5: Insert Feedback
  feedback ← DATABASE.insert("feedback_submissions", {
    id: feedback_id,
    client_account_id: client_account_id,
    application_id: application_id OR NULL,
    rating: rating,
    comments: comments OR NULL,
    metadata: {},
    created_at: CURRENT_TIMESTAMP,
    updated_at: CURRENT_TIMESTAMP
  })
  
  // Step 6: Return Success Response
  RETURN success({
    message: "Thank you for your feedback!",
    feedback: feedback
  })
END

ALGORITHM GetFeedbackStatistics()
BEGIN
  // Step 1: Calculate Average Rating
  avg_result ← DATABASE.query(
    "SELECT AVG(rating) as average_rating FROM feedback_submissions"
  )
  
  average_rating ← ROUND(avg_result.average_rating, 2)
  
  // Step 2: Count Total Submissions
  total_count ← DATABASE.count(
    "SELECT COUNT(*) FROM feedback_submissions"
  )
  
  // Step 3: Get Rating Distribution
  distribution ← {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
  
  FOR rating_value FROM 1 TO 5 DO
    count ← DATABASE.count(
      "SELECT COUNT(*) FROM feedback_submissions WHERE rating = ?",
      rating_value
    )
    distribution[rating_value] ← count
  END FOR
  
  // Step 4: Calculate Percentages
  FOR rating_value FROM 1 TO 5 DO
    IF total_count > 0 THEN
      percentage ← (distribution[rating_value] / total_count) * 100
      distribution[rating_value + '_percentage'] ← ROUND(percentage, 1)
    ELSE
      distribution[rating_value + '_percentage'] ← 0
    END IF
  END FOR
  
  // Step 5: Return Statistics
  RETURN success({
    stats: {
      average_rating: average_rating,
      total_submissions: total_count,
      distribution: distribution
    }
  })
END

ALGORITHM GetUserFeedback(client_account_id)
BEGIN
  // Step 1: Query User's Feedback
  feedback_list ← DATABASE.query(
    "SELECT f.*, a.application_type, bp.business_name
     FROM feedback_submissions f
     LEFT JOIN business_applications a ON f.application_id = a.id
     LEFT JOIN business_profiles bp ON a.business_profile_id = bp.id
     WHERE f.client_account_id = ?
     ORDER BY f.created_at DESC",
    client_account_id
  )
  
  // Step 2: Return Feedback List
  RETURN success({
    feedback: feedback_list
  })
END
```

---

## Flowchart (Submit Feedback)

```mermaid
flowchart TD
    Start([Start: Submit Feedback]) --> Auth{User<br/>Authenticated?}
    Auth -->|No| ErrorAuth[Return Error 401]
    Auth -->|Yes| Input[Receive: rating,<br/>comments, application_id]
    Input --> ValidateRating{Rating<br/>1-5?}
    ValidateRating -->|No| ErrorRating[Return Error:<br/>Invalid rating]
    ValidateRating -->|Yes| ValidateComments{Comments<br/>≤ 1000 chars?}
    ValidateComments -->|No| ErrorComments[Return Error:<br/>Comments too long]
    ValidateComments -->|Yes| HasAppID{Application ID<br/>Provided?}
    HasAppID -->|Yes| FindApp[Query: Find application<br/>with ownership check]
    FindApp --> AppExists{Application<br/>Found?}
    AppExists -->|No| ErrorNotFound[Return Error 404]
    AppExists -->|Yes| CheckOwner{User Owns<br/>Application?}
    CheckOwner -->|No| ErrorAccess[Return Error 403]
    CheckOwner -->|Yes| CheckStatus{Application<br/>Submitted?}
    CheckStatus -->|No| ErrorStatus[Return Error:<br/>Not submitted yet]
    CheckStatus -->|Yes| GenerateUUID
    HasAppID -->|No| GenerateUUID[Generate UUID<br/>for feedback_id]
    GenerateUUID --> InsertFeedback[Insert into<br/>feedback_submissions table]
    InsertFeedback --> Success[Return Success:<br/>Thank you for feedback]
    Success --> End([End])
    ErrorAuth --> End
    ErrorRating --> End
    ErrorComments --> End
    ErrorNotFound --> End
    ErrorAccess --> End
    ErrorStatus --> End
```

---

## Flowchart (Get Statistics)

```mermaid
flowchart TD
    Start([Start: Get Statistics]) --> CalcAvg[Query: Calculate<br/>AVG rating]
    CalcAvg --> CountTotal[Query: Count total<br/>submissions]
    CountTotal --> InitDist[Initialize distribution:<br/>1:0, 2:0, 3:0, 4:0, 5:0]
    InitDist --> Loop{For each<br/>rating 1-5}
    Loop -->|Yes| CountRating[Query: Count submissions<br/>for this rating]
    CountRating --> CalcPercent[Calculate percentage<br/>of total]
    CalcPercent --> Loop
    Loop -->|No| Success[Return Success:<br/>Statistics object]
    Success --> End([End])
```

---

## Rating Scale

| Stars | Label | Description |
|-------|-------|-------------|
| ⭐ | Very Poor | Extremely dissatisfied |
| ⭐⭐ | Poor | Dissatisfied |
| ⭐⭐⭐ | Average | Neutral experience |
| ⭐⭐⭐⭐ | Good | Satisfied |
| ⭐⭐⭐⭐⭐ | Excellent | Very satisfied |

---

## Statistics Calculations

### Average Rating
```
average_rating = SUM(all ratings) / COUNT(all submissions)
```

### Rating Distribution
```
For each rating level (1-5):
  count = COUNT(submissions WHERE rating = level)
  percentage = (count / total_submissions) * 100
```

### Example Output
```json
{
  "average_rating": 4.2,
  "total_submissions": 150,
  "distribution": {
    "1": 5,
    "1_percentage": 3.3,
    "2": 10,
    "2_percentage": 6.7,
    "3": 25,
    "3_percentage": 16.7,
    "4": 60,
    "4_percentage": 40.0,
    "5": 50,
    "5_percentage": 33.3
  }
}
```

---

## Database Tables Involved

### feedback_submissions
- **Operations:** INSERT, SELECT, COUNT, AVG
- **Fields:** id, client_account_id, application_id, rating, comments, metadata, created_at
- **Indexes:** btree on client_account_id, btree on application_id, btree on rating

### business_applications
- **Operations:** SELECT (for validation)
- **Fields:** id, business_profile_id, status

### business_profiles
- **Operations:** SELECT (for ownership verification)
- **Fields:** id, client_account_id, business_name

---

## Frontend Display

### Star Rating Component
```typescript
const StarRating = ({ rating, onChange }) => {
  const [hover, setHover] = useState(0);
  
  return (
    <div>
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={star <= (hover || rating) ? 'fill-yellow-400' : ''}
          onClick={() => onChange(star)}
          onMouseEnter={() => setHover(star)}
          onMouseLeave={() => setHover(0)}
        />
      ))}
    </div>
  );
};
```

### Distribution Chart
```typescript
{Object.entries(distribution).map(([rating, count]) => (
  <div key={rating}>
    <span>{rating} ⭐</span>
    <div className="progress-bar">
      <div style={{ width: `${distribution[rating + '_percentage']}%` }} />
    </div>
    <span>{count} ({distribution[rating + '_percentage']}%)</span>
  </div>
))}
```

---

## Implementation Files

- **Controller:** `controllers/feedbackController.js`
- **Model:** `models/FeedbackSubmission.js`
- **Routes:** `routes/feedback.js`
- **Frontend:** `src/pages/FeedbackPage.tsx`
- **Service:** `src/services/feedbackService.ts`

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

