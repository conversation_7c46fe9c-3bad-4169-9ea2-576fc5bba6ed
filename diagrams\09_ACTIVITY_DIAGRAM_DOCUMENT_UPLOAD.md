# Diagram 9: Activity Diagram - Document Upload

## Overview
This activity diagram shows the detailed document upload validation flow with decision points for file type, file size, ownership verification, and application status checks, including error handling and file cleanup.

---

## Process Flow
1. User selects file
2. Validate file type (PDF, JPG, PNG)
3. Validate file size (max 5MB)
4. Verify application ownership
5. Check application status (draft only)
6. Save file to disk
7. Create database record
8. Return success or error

---

## Mermaid Activity Diagram

```mermaid
flowchart TD
    Start([User Initiates Document Upload]) --> ViewApp[View Application Details<br/>Status: draft]
    
    ViewApp --> SelectDocType[Select Document Type<br/>DTI, Barangay Clearance,<br/>Fire Safety, etc.]
    
    SelectDocType --> ClickUpload[Click Upload Button]
    
    ClickUpload --> OpenDialog[Open File Selection Dialog]
    
    OpenDialog --> UserSelect{User Selects<br/>File?}
    
    UserSelect -->|Cancel| End1([End: Upload Cancelled])
    
    UserSelect -->|Yes| FileSelected[File Selected from Device]
    
    FileSelected --> ClientValidate[Client-side Validation]
    
    %% Client-side File Type Check
    ClientValidate --> CheckTypeClient{File Type<br/>Valid?}
    
    CheckTypeClient -->|No| ErrorTypeClient[Display Error:<br/>Only PDF, JPG, PNG allowed]
    ErrorTypeClient --> OpenDialog
    
    CheckTypeClient -->|Yes| CheckSizeClient{File Size<br/>≤ 5MB?}
    
    CheckSizeClient -->|No| ErrorSizeClient[Display Error:<br/>File size must not exceed 5MB]
    ErrorSizeClient --> OpenDialog
    
    CheckSizeClient -->|Yes| PrepareUpload[Prepare FormData:<br/>application_id, document_type, file]
    
    PrepareUpload --> SendRequest[Send POST Request<br/>to /api/documents/upload<br/>with JWT token]
    
    SendRequest --> ServerReceive[Server Receives Request]
    
    %% Multer Middleware Processing
    ServerReceive --> MulterProcess[Multer Middleware<br/>Processes multipart/form-data]
    
    MulterProcess --> CheckTypeServer{MIME Type<br/>Valid?}
    
    CheckTypeServer -->|No| ErrorTypeServer[Return 400 Error:<br/>Invalid file type]
    ErrorTypeServer --> DisplayError1[Display Error to User]
    DisplayError1 --> End2([End: Upload Failed])
    
    CheckTypeServer -->|Yes| CheckSizeServer{File Size<br/>≤ 5MB?}
    
    CheckSizeServer -->|No| ErrorSizeServer[Return 400 Error:<br/>File too large]
    ErrorSizeServer --> DisplayError2[Display Error to User]
    DisplayError2 --> End3([End: Upload Failed])
    
    CheckSizeServer -->|Yes| GenerateFilename[Generate Unique Filename:<br/>timestamp-uuid-original_name]
    
    GenerateFilename --> SaveToDisk[Save File to Disk:<br/>uploads/documents/]
    
    SaveToDisk --> FileOnDisk[File Saved Successfully]
    
    %% Controller Processing
    FileOnDisk --> ControllerProcess[Controller Processes Request]
    
    ControllerProcess --> CheckFileExists{File in<br/>Request?}
    
    CheckFileExists -->|No| ErrorNoFile[Return 400 Error:<br/>No file uploaded]
    ErrorNoFile --> DisplayError3[Display Error to User]
    DisplayError3 --> End4([End: Upload Failed])
    
    CheckFileExists -->|Yes| FindApp[Find Application by ID<br/>with Business Profile JOIN]
    
    FindApp --> AppExists{Application<br/>Found?}
    
    AppExists -->|No| CleanupNotFound[Delete File from Disk<br/>Cleanup]
    CleanupNotFound --> ErrorNotFound[Return 404 Error:<br/>Application not found]
    ErrorNotFound --> DisplayError4[Display Error to User]
    DisplayError4 --> End5([End: Upload Failed])
    
    AppExists -->|Yes| CheckOwnership{User Owns<br/>Application?}
    
    CheckOwnership -->|No| CleanupAccess[Delete File from Disk<br/>Cleanup]
    CleanupAccess --> ErrorAccess[Return 403 Error:<br/>Access denied]
    ErrorAccess --> DisplayError5[Display Error to User]
    DisplayError5 --> End6([End: Upload Failed])
    
    CheckOwnership -->|Yes| CheckStatus{Application<br/>Status = draft?}
    
    CheckStatus -->|No| CleanupStatus[Delete File from Disk<br/>Cleanup]
    CleanupStatus --> ErrorStatus[Return 400 Error:<br/>Cannot upload to submitted applications]
    ErrorStatus --> DisplayError6[Display Error to User]
    DisplayError6 --> End7([End: Upload Failed])
    
    CheckStatus -->|Yes| CreateRecord[Create Document Record<br/>in Database]
    
    CreateRecord --> DBInsert[INSERT INTO document_uploads<br/>id, application_id, document_type,<br/>file_path, file_name, file_size,<br/>mime_type, created_at, updated_at]
    
    DBInsert --> DBSuccess{Database<br/>Insert OK?}
    
    DBSuccess -->|No| CleanupDB[Delete File from Disk<br/>Cleanup]
    CleanupDB --> ErrorDB[Return 500 Error:<br/>Database error]
    ErrorDB --> DisplayError7[Display Error to User]
    DisplayError7 --> End8([End: Upload Failed])
    
    DBSuccess -->|Yes| ReturnSuccess[Return 201 Created:<br/>Document object]
    
    ReturnSuccess --> DisplaySuccess[Display Success Message:<br/>Document uploaded successfully]
    
    DisplaySuccess --> RefreshList[Refresh Document List<br/>on UI]
    
    RefreshList --> MoreUploads{Upload More<br/>Documents?}
    
    MoreUploads -->|Yes| SelectDocType
    MoreUploads -->|No| ReviewDocs[Review All Documents]
    
    ReviewDocs --> ReadySubmit{Ready to<br/>Submit Application?}
    
    ReadySubmit -->|Yes| SubmitApp[Submit Application<br/>for Review]
    SubmitApp --> End9([End: Application Submitted])
    
    ReadySubmit -->|No| SaveDraft[Save as Draft]
    SaveDraft --> End10([End: Draft Saved])
    
    %% Styling
    classDef userAction fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef systemAction fill:#10b981,stroke:#059669,color:#fff
    classDef validation fill:#8b5cf6,stroke:#7c3aed,color:#fff
    classDef error fill:#ef4444,stroke:#dc2626,color:#fff
    classDef cleanup fill:#f59e0b,stroke:#d97706,color:#fff
    
    class ViewApp,SelectDocType,ClickUpload,OpenDialog,FileSelected,ReviewDocs,SubmitApp,SaveDraft userAction
    class ClientValidate,PrepareUpload,SendRequest,ServerReceive,MulterProcess,GenerateFilename,SaveToDisk,FileOnDisk,ControllerProcess,FindApp,CreateRecord,DBInsert,ReturnSuccess,DisplaySuccess,RefreshList systemAction
    class CheckTypeClient,CheckSizeClient,CheckTypeServer,CheckSizeServer,CheckFileExists,AppExists,CheckOwnership,CheckStatus,DBSuccess,UserSelect,MoreUploads,ReadySubmit validation
    class ErrorTypeClient,ErrorSizeClient,ErrorTypeServer,ErrorSizeServer,ErrorNoFile,ErrorNotFound,ErrorAccess,ErrorStatus,ErrorDB,DisplayError1,DisplayError2,DisplayError3,DisplayError4,DisplayError5,DisplayError6,DisplayError7 error
    class CleanupNotFound,CleanupAccess,CleanupStatus,CleanupDB cleanup
```

---

## Validation Checkpoints

### 1. Client-Side Validation (Frontend)
**Purpose:** Provide immediate feedback to user

#### File Type Check
```javascript
const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
if (!allowedTypes.includes(file.type)) {
  return error("Only PDF, JPG, and PNG files are allowed");
}
```

#### File Size Check
```javascript
const maxSize = 5 * 1024 * 1024; // 5MB
if (file.size > maxSize) {
  return error("File size must not exceed 5MB");
}
```

---

### 2. Server-Side Validation (Multer Middleware)
**Purpose:** Security and data integrity

#### MIME Type Validation
```javascript
const fileFilter = (req, file, cb) => {
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type'), false);
  }
};
```

#### File Size Limit
```javascript
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 }
});
```

---

### 3. Business Logic Validation (Controller)
**Purpose:** Enforce business rules

#### Application Ownership
```javascript
if (application.BusinessProfile.client_account_id !== req.user.id) {
  // Delete uploaded file
  fs.unlinkSync(req.file.path);
  return res.status(403).json({ message: "Access denied" });
}
```

#### Application Status
```javascript
if (application.status !== 'draft') {
  // Delete uploaded file
  fs.unlinkSync(req.file.path);
  return res.status(400).json({ 
    message: "Cannot upload documents to submitted applications" 
  });
}
```

---

## File Cleanup on Error

### Why Cleanup is Necessary
When Multer saves a file to disk before validation, if any subsequent validation fails, the file must be deleted to prevent orphaned files.

### Cleanup Scenarios
1. **Application not found** → Delete file, return 404
2. **Access denied** → Delete file, return 403
3. **Application not draft** → Delete file, return 400
4. **Database error** → Delete file, return 500

### Cleanup Implementation
```javascript
try {
  // Validation logic
  if (error) {
    fs.unlinkSync(req.file.path);  // Delete file
    return res.status(400).json({ message: error.message });
  }
} catch (error) {
  if (req.file) {
    fs.unlinkSync(req.file.path);  // Cleanup on exception
  }
  return res.status(500).json({ message: "Upload failed" });
}
```

---

## Document Types

### Required Documents (Examples)
1. **DTI Registration** - Department of Trade and Industry certificate
2. **Barangay Clearance** - Clearance from local barangay
3. **Fire Safety Certificate** - Fire safety inspection certificate
4. **Sanitary Permit** - Health and sanitation permit
5. **Environmental Clearance** - Environmental compliance certificate
6. **Building Permit** - Building permit (if applicable)
7. **Business Plan** - Business plan document
8. **Financial Statements** - Financial documents
9. **Other** - Other supporting documents

---

## Error Messages

| Error Type | HTTP Status | Message | User Action |
|------------|-------------|---------|-------------|
| Invalid file type (client) | - | "Only PDF, JPG, and PNG files are allowed" | Select different file |
| File too large (client) | - | "File size must not exceed 5MB" | Compress or select smaller file |
| Invalid MIME type (server) | 400 | "Invalid file type. Only PDF, JPG, and PNG allowed." | Select valid file |
| File too large (server) | 400 | "File size exceeds 5MB limit" | Select smaller file |
| No file uploaded | 400 | "No file uploaded" | Select a file |
| Application not found | 404 | "Application not found" | Check application ID |
| Access denied | 403 | "You don't have permission to upload to this application" | Verify ownership |
| Application not draft | 400 | "Cannot upload documents to submitted applications" | Create new application |
| Database error | 500 | "Failed to upload document. Please try again." | Retry upload |

---

## Database Operations

### Create Document Record
```sql
INSERT INTO document_uploads (
  id, application_id, document_type, file_path, file_name, 
  file_size, mime_type, metadata, created_at, updated_at
) VALUES (
  uuid_generate_v4(), ?, ?, ?, ?, ?, ?, '{}', NOW(), NOW()
);
```

### Find Application with Ownership
```sql
SELECT a.*, bp.client_account_id
FROM business_applications a
JOIN business_profiles bp ON a.business_profile_id = bp.id
WHERE a.id = ?;
```

---

## Performance Considerations

- **Client-side validation:** < 10ms (instant feedback)
- **File upload:** 50-500ms (depends on file size and network)
- **Multer processing:** 10-50ms
- **Disk write:** 20-100ms
- **Database INSERT:** 10-30ms
- **Total time:** 90-680ms (for 2MB file)

---

**Diagram Status:** ✅ Complete  
**Total Steps:** 40+  
**Decision Points:** 12  
**Error Paths:** 7  
**Cleanup Points:** 4  
**Last Updated:** November 20, 2025

