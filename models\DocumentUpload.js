// Sequelize Model for Document Upload - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define DocumentUpload model for file attachments
// Security: UUID primary keys, file size validation, MIME type checking
// Standards: Sequelize v6+ conventions with PostgreSQL data types

module.exports = (sequelize, DataTypes) => {
  const DocumentUpload = sequelize.define('DocumentUpload', {
    // Primary key: UUID v4 for secure identifiers
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique document identifier (UUID v4)',
    },
    // Foreign key to business_applications table
    application_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'business_applications',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Reference to business application',
    },
    // Document type/category
    document_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Document type is required',
        },
      },
      comment: 'Type of document (e.g., DTI Permit, Barangay Clearance, etc.)',
    },
    // File path on server
    file_path: {
      type: DataTypes.STRING(500),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'File path is required',
        },
      },
      comment: 'Server file path',
    },
    // Original file name
    file_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'File name is required',
        },
      },
      comment: 'Original file name',
    },
    // MIME type for validation
    mime_type: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        isIn: {
          args: [['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']],
          msg: 'Only PDF, JPG, JPEG, and PNG files are allowed',
        },
      },
      comment: 'MIME type of the file',
    },
    // File size in bytes with 5MB limit
    file_size: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: {
          args: [1],
          msg: 'File size must be greater than 0',
        },
        max: {
          args: [5242880], // 5MB in bytes
          msg: 'File size must not exceed 5MB',
        },
      },
      comment: 'File size in bytes (max 5MB)',
    },
    // Upload timestamp
    uploaded_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Timestamp when file was uploaded',
    },
    // Additional metadata for flexible extensions
    metadata: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
      comment: 'JSONB field for flexible metadata (upload IP, user agent, etc.)',
    },
  }, {
    // Table configuration
    tableName: 'document_uploads',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Foreign key index for performance
      {
        fields: ['application_id'],
        name: 'idx_document_uploads_application_id',
        using: 'btree',
      },
      // Document type filtering index
      {
        fields: ['document_type'],
        name: 'idx_document_uploads_document_type',
        using: 'btree',
      },
      // Upload date index for reporting
      {
        fields: ['uploaded_at'],
        name: 'idx_document_uploads_uploaded_at',
        using: 'btree',
      },
      // GIN index for JSONB metadata queries
      {
        fields: ['metadata'],
        name: 'idx_document_uploads_metadata',
        using: 'gin',
      },
    ],
  });

  // Instance method to get file size in MB
  DocumentUpload.prototype.getFileSizeMB = function() {
    return (this.file_size / 1048576).toFixed(2);
  };

  // Instance method to check if file is an image
  DocumentUpload.prototype.isImage = function() {
    return this.mime_type.startsWith('image/');
  };

  // Instance method to check if file is a PDF
  DocumentUpload.prototype.isPDF = function() {
    return this.mime_type === 'application/pdf';
  };

  // Static method to find documents by application
  DocumentUpload.findByApplication = async function(applicationId) {
    return await this.findAll({
      where: { application_id: applicationId },
      order: [['uploaded_at', 'DESC']],
    });
  };

  // Static method to find documents by type
  DocumentUpload.findByType = async function(documentType) {
    return await this.findAll({
      where: { document_type: documentType },
      order: [['uploaded_at', 'DESC']],
    });
  };

  // Static method to calculate total storage used
  DocumentUpload.getTotalStorageUsed = async function() {
    const result = await this.sum('file_size');
    return result || 0;
  };

  return DocumentUpload;
};

