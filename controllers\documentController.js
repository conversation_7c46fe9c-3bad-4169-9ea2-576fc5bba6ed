// Document Controller - Document Upload Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Handle document upload operations

const { DocumentUpload, BusinessApplication, BusinessProfile } = require('../models');
const { deleteFile } = require('../middleware/upload');
const path = require('path');

/**
 * Upload document for application
 * @route POST /api/documents/upload
 * @access Private
 */
const uploadDocument = async (req, res) => {
  try {
    const { application_id, document_type } = req.body;

    // Validation
    if (!application_id) {
      if (req.file) deleteFile(req.file.path);
      return res.status(400).json({
        success: false,
        message: 'Application ID is required.',
      });
    }

    if (!document_type) {
      if (req.file) deleteFile(req.file.path);
      return res.status(400).json({
        success: false,
        message: 'Document type is required.',
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file uploaded.',
      });
    }

    // Verify application belongs to user
    const application = await BusinessApplication.findOne({
      where: { id: application_id },
      include: [
        {
          model: BusinessProfile,
          as: 'businessProfile',
          where: { client_id: req.user.userId },
        },
      ],
    });

    if (!application) {
      deleteFile(req.file.path);
      return res.status(404).json({
        success: false,
        message: 'Application not found.',
      });
    }

    // Check if application is still in draft status
    if (application.status !== 'draft') {
      deleteFile(req.file.path);
      return res.status(400).json({
        success: false,
        message: 'Documents can only be uploaded to draft applications.',
      });
    }

    // Create document record
    const document = await DocumentUpload.create({
      application_id: application_id,
      document_type: document_type,
      file_path: req.file.path,
      file_name: req.file.originalname,
      mime_type: req.file.mimetype,
      file_size: req.file.size,
      uploaded_at: new Date(),
      metadata: {
        uploaded_by: req.user.userId,
        original_name: req.file.originalname,
      },
    });

    res.status(201).json({
      success: true,
      message: 'Document uploaded successfully.',
      data: {
        id: document.id,
        document_type: document.document_type,
        file_name: document.file_name,
        file_size: document.file_size,
        mime_type: document.mime_type,
        uploaded_at: document.uploaded_at,
      },
    });
  } catch (error) {
    console.error('Upload document error:', error);
    if (req.file) deleteFile(req.file.path);
    res.status(500).json({
      success: false,
      message: 'Failed to upload document.',
    });
  }
};

/**
 * Get documents for application
 * @route GET /api/documents/application/:applicationId
 * @access Private
 */
const getDocumentsByApplication = async (req, res) => {
  try {
    const { applicationId } = req.params;

    // Verify application belongs to user
    const application = await BusinessApplication.findOne({
      where: { id: applicationId },
      include: [
        {
          model: BusinessProfile,
          as: 'businessProfile',
          where: { client_id: req.user.userId },
        },
      ],
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found.',
      });
    }

    const documents = await DocumentUpload.findAll({
      where: { application_id: applicationId },
      order: [['uploaded_at', 'DESC']],
    });

    res.status(200).json({
      success: true,
      data: documents,
    });
  } catch (error) {
    console.error('Get documents error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve documents.',
    });
  }
};

/**
 * Delete document
 * @route DELETE /api/documents/:id
 * @access Private
 */
const deleteDocument = async (req, res) => {
  try {
    const { id } = req.params;

    const document = await DocumentUpload.findOne({
      where: { id: id },
      include: [
        {
          model: BusinessApplication,
          as: 'application',
          include: [
            {
              model: BusinessProfile,
              as: 'businessProfile',
              where: { client_id: req.user.userId },
            },
          ],
        },
      ],
    });

    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found.',
      });
    }

    // Check if application is still in draft
    if (document.application.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Documents can only be deleted from draft applications.',
      });
    }

    // Delete file from filesystem
    deleteFile(document.file_path);

    // Delete database record
    await document.destroy();

    res.status(200).json({
      success: true,
      message: 'Document deleted successfully.',
    });
  } catch (error) {
    console.error('Delete document error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete document.',
    });
  }
};

module.exports = {
  uploadDocument,
  getDocumentsByApplication,
  deleteDocument,
};

