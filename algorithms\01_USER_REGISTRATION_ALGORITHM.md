# Algorithm 1: User Registration with OTP Verification

## Overview
This algorithm handles the complete user registration process including email validation, password hashing, OTP generation, and email verification for the Smart Governance Business Permit Processing System.

---

## Algorithm Complexity
- **Time Complexity:** O(1) - Constant time for database operations
- **Space Complexity:** O(1) - Fixed size data structures
- **Database Operations:** 2 INSERT queries (client_accounts, otp_verification)

---

## Input Parameters
```typescript
interface RegistrationInput {
  email: string;           // User's email address (must be unique)
  password: string;        // Plain text password (min 8 characters)
  full_name: string;       // User's full name
  contact_number: string;  // Phone number (optional)
}
```

## Output
```typescript
interface RegistrationOutput {
  success: boolean;
  message: string;
  client_account_id?: UUID;
  otp_sent?: boolean;
}
```

---

## Pseudocode

```
ALGORITHM UserRegistration(email, password, full_name, contact_number)
BEGIN
  // Step 1: Input Validation
  IF email is empty OR NOT valid_email_format(email) THEN
    RETURN error("Invalid email format")
  END IF
  
  IF password.length < 8 THEN
    RETURN error("Password must be at least 8 characters")
  END IF
  
  // Step 2: Check for Existing User
  existing_user ← DATABASE.query("SELECT * FROM client_accounts WHERE email = ?", email)
  
  IF existing_user EXISTS THEN
    RETURN error("Email already registered")
  END IF
  
  // Step 3: Hash Password
  salt ← bcrypt.genSalt(10)
  password_hash ← bcrypt.hash(password, salt)
  
  // Step 4: Create User Account
  client_account_id ← UUID.generate_v4()
  
  DATABASE.insert("client_accounts", {
    id: client_account_id,
    email: email,
    password_hash: password_hash,
    full_name: full_name,
    contact_number: contact_number,
    is_verified: FALSE,
    created_at: CURRENT_TIMESTAMP
  })
  
  // Step 5: Generate OTP
  otp_code ← RANDOM.generate_6_digit_number()
  otp_id ← UUID.generate_v4()
  expires_at ← CURRENT_TIMESTAMP + 10 MINUTES
  
  DATABASE.insert("otp_verification", {
    id: otp_id,
    client_account_id: client_account_id,
    otp_code: otp_code,
    purpose: 'registration',
    expires_at: expires_at,
    created_at: CURRENT_TIMESTAMP
  })
  
  // Step 6: Send OTP Email
  email_sent ← SEND_EMAIL({
    to: email,
    subject: "Verify Your Email - Smart Governance",
    body: "Your OTP code is: " + otp_code + ". Valid for 10 minutes."
  })
  
  // Step 7: Return Success Response
  RETURN success({
    message: "Registration successful. Please verify your email.",
    client_account_id: client_account_id,
    otp_sent: email_sent
  })
END
```

---

## Flowchart

```mermaid
flowchart TD
    Start([Start: User Registration]) --> Input[Receive: email, password, full_name, contact_number]
    Input --> ValidateEmail{Valid Email<br/>Format?}
    ValidateEmail -->|No| ErrorEmail[Return Error:<br/>Invalid email format]
    ValidateEmail -->|Yes| ValidatePassword{Password<br/>Length >= 8?}
    ValidatePassword -->|No| ErrorPassword[Return Error:<br/>Password too short]
    ValidatePassword -->|Yes| CheckExisting[Query Database:<br/>Check if email exists]
    CheckExisting --> UserExists{Email<br/>Already<br/>Registered?}
    UserExists -->|Yes| ErrorExists[Return Error:<br/>Email already registered]
    UserExists -->|No| HashPassword[Hash Password:<br/>bcrypt with 10 rounds]
    HashPassword --> GenerateUUID[Generate UUID<br/>for client_account_id]
    GenerateUUID --> InsertUser[Insert into<br/>client_accounts table]
    InsertUser --> GenerateOTP[Generate 6-digit<br/>OTP code]
    GenerateOTP --> SetExpiry[Set OTP expiry:<br/>Current time + 10 minutes]
    SetExpiry --> InsertOTP[Insert into<br/>otp_verification table]
    InsertOTP --> SendEmail[Send OTP via Email]
    SendEmail --> EmailSent{Email<br/>Sent<br/>Successfully?}
    EmailSent -->|Yes| SuccessResponse[Return Success:<br/>Registration complete,<br/>verify email]
    EmailSent -->|No| WarningResponse[Return Warning:<br/>Registered but email failed]
    SuccessResponse --> End([End])
    WarningResponse --> End
    ErrorEmail --> End
    ErrorPassword --> End
    ErrorExists --> End
```

---

## Step-by-Step Explanation

### Step 1: Input Validation
- Validate email format using regex pattern
- Check password length (minimum 8 characters)
- Ensure required fields are not empty

### Step 2: Check for Existing User
- Query `client_accounts` table for existing email
- Prevent duplicate registrations
- Return error if email already exists

### Step 3: Hash Password
- Use bcrypt with 10 salt rounds
- Never store plain text passwords
- Generates unique hash even for same password

### Step 4: Create User Account
- Generate UUID v4 for primary key
- Insert record into `client_accounts` table
- Set `is_verified` to FALSE initially

### Step 5: Generate OTP
- Generate random 6-digit number (100000-999999)
- Create OTP record with 10-minute expiry
- Link OTP to user account via foreign key

### Step 6: Send OTP Email
- Use Nodemailer to send email
- Include OTP code and expiry information
- In development: console.log for demo

### Step 7: Return Success Response
- Return success message
- Include client_account_id for next step
- Indicate if email was sent successfully

---

## Security Considerations

1. **Password Hashing:** bcrypt with 10 rounds prevents rainbow table attacks
2. **OTP Expiry:** 10-minute window limits brute force attempts
3. **Email Validation:** Prevents invalid email addresses
4. **Unique Constraint:** Database enforces email uniqueness
5. **No Password Exposure:** Password never logged or returned in response

---

## Error Handling

| Error Type | HTTP Status | Message |
|------------|-------------|---------|
| Invalid Email | 400 | "Invalid email format" |
| Short Password | 400 | "Password must be at least 8 characters" |
| Duplicate Email | 409 | "Email already registered" |
| Database Error | 500 | "Registration failed. Please try again." |
| Email Send Failure | 201 | "Registered but verification email failed" |

---

## Database Tables Involved

### client_accounts
- **Operation:** INSERT
- **Fields:** id, email, password_hash, full_name, contact_number, is_verified

### otp_verification
- **Operation:** INSERT
- **Fields:** id, client_account_id, otp_code, purpose, expires_at

---

## Implementation Files

- **Controller:** `controllers/authController.js` - `register()` function
- **Model:** `models/ClientAccount.js`, `models/OtpVerification.js`
- **Route:** `routes/auth.js` - `POST /api/auth/register`
- **Frontend:** `src/pages/RegisterPage.tsx`

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

