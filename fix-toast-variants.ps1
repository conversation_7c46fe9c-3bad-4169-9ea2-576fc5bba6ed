# PowerShell script to fix toast variant types
# Replace 'error' with 'destructive' and 'info' with 'default'

$files = @(
    "src/pages/ApplicationStatusPage.tsx",
    "src/pages/BusinessProfilePage.tsx",
    "src/pages/DocumentUploadPage.tsx",
    "src/pages/FeedbackPage.tsx",
    "src/pages/NotificationsPage.tsx",
    "src/pages/ProfilePage.tsx"
)

foreach ($file in $files) {
    Write-Host "Processing $file..."
    $content = Get-Content $file -Raw
    $content = $content -replace "variant: 'error'", "variant: 'destructive'"
    $content = $content -replace "variant: 'info'", "variant: 'default'"
    Set-Content -Path $file -Value $content -NoNewline
    Write-Host "✅ Fixed $file"
}

Write-Host "`n✅ All toast variants fixed!"

