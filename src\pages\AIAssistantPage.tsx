import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Textarea } from '@/components/ui/Textarea';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/hooks/useToast';
import apiClient from '@/services/apiClient';
import BackButton from '@/components/ui/BackButton';

interface AIResponse {
  response: string;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

const AIAssistantPage: React.FC = () => {
  const [message, setMessage] = useState('');
  const [context, setContext] = useState('general');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<AIResponse | null>(null);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a message.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await apiClient.post('/ai/chat', {
        message: message.trim(),
        context,
      });

      setResponse(result.data.data);
      toast({
        title: 'Success',
        description: 'AI response generated successfully.',
      });
    } catch (error: any) {
      console.error('AI chat error:', error);
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to get AI response.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const contextOptions = [
    { value: 'general', label: 'General', description: 'General assistance' },
    { value: 'application', label: 'Application', description: 'Help with business applications' },
    { value: 'documents', label: 'Documents', description: 'Document preparation guidance' },
    { value: 'feedback', label: 'Feedback', description: 'Feedback and suggestions' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 p-4">
      <div className="max-w-4xl mx-auto">
        <BackButton />

        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            AI Assistant
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Get intelligent assistance with your business applications and processes.
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Input Section */}
          <Card>
            <CardHeader>
              <CardTitle>Ask the AI</CardTitle>
              <CardDescription>
                Describe what you need help with, and our AI assistant will provide guidance.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Context
                  </label>
                  <select
                    value={context}
                    onChange={(e) => setContext(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  >
                    {contextOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label} - {option.description}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Your Message
                  </label>
                  <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Ask me anything about business applications, documents, or processes..."
                    rows={6}
                    className="resize-none"
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? 'Generating Response...' : 'Ask AI'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Response Section */}
          <Card>
            <CardHeader>
              <CardTitle>AI Response</CardTitle>
              <CardDescription>
                {response ? 'Here\'s the AI\'s response to your query.' : 'Submit a message to get started.'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {response ? (
                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <p className="text-gray-800 dark:text-gray-200 whitespace-pre-wrap">
                      {response.response}
                    </p>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">
                      Model: {response.model}
                    </Badge>
                    <Badge variant="secondary">
                      Tokens: {response.usage.total_tokens}
                    </Badge>
                    <Badge variant="secondary">
                      Prompt: {response.usage.prompt_tokens}
                    </Badge>
                    <Badge variant="secondary">
                      Completion: {response.usage.completion_tokens}
                    </Badge>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <div className="text-4xl mb-4">🤖</div>
                  <p>No response yet. Ask the AI something!</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common questions you might have
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2">
              <Button
                variant="outline"
                onClick={() => setMessage('What documents do I need to submit for a business license application?')}
                className="text-left justify-start h-auto p-3"
              >
                📄 What documents do I need?
              </Button>
              <Button
                variant="outline"
                onClick={() => setMessage('How long does the application review process typically take?')}
                className="text-left justify-start h-auto p-3"
              >
                ⏱️ How long does review take?
              </Button>
              <Button
                variant="outline"
                onClick={() => setMessage('What are the common reasons applications get rejected?')}
                className="text-left justify-start h-auto p-3"
              >
                ❌ Common rejection reasons
              </Button>
              <Button
                variant="outline"
                onClick={() => setMessage('How can I improve my application\'s chances of approval?')}
                className="text-left justify-start h-auto p-3"
              >
                ✅ Improve approval chances
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AIAssistantPage;
