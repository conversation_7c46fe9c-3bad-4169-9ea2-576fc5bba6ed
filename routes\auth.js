// Authentication Routes for Smart Governance System
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 19, 2025
// Purpose: REST API endpoints for user authentication
// Security: Input validation, rate limiting, JWT authentication
// Standards: Express.js routing best practices

import express from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { body, validationResult } from 'express-validator';

const router = express.Router();

// Generate JWT token
const generateToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      fullName: user.full_name,
    },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '24h' }
  );
};

// Register endpoint
router.post('/register', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 8 }),
  body('fullName').optional().trim().isLength({ min: 2 }),
  body('contact_number').optional().isMobilePhone(),
  body('contactNumber').optional().isMobilePhone(), // Support both camelCase and snake_case
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    // Support both camelCase (from frontend) and snake_case (from API)
    const { email, password, fullName, contactNumber, contact_number } = req.body;
    const phoneNumber = contactNumber || contact_number;

    // Check if user already exists
    const existingUser = await ClientAccount.findOne({ where: { email } });
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'User with this email already exists',
      });
    }

    // Create user - let the model handle password hashing via beforeCreate hook
    // Do NOT hash here to avoid double-hashing
    const user = await ClientAccount.create({
      email,
      password_hash: password, // Pass plain password - model will hash it
      full_name: fullName || email.split('@')[0], // Use email username if fullName not provided
      contact_number: phoneNumber, // Use the normalized phone number
      is_verified: true, // Auto-verify user
      metadata: {
        registration_date: new Date().toISOString(),
        source: 'web_portal',
      },
    });

    // Generate token for auto-login
    const token = generateToken(user);

    res.status(201).json({
      success: true,
      message: 'User registered and verified successfully.',
      data: {
        token,
        user: {
          id: user.id,
          email: user.email,
          fullName: user.full_name,
        },
      },
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed. Please try again.',
    });
  }
});

// Login endpoint
router.post('/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').exists(),
  body('rememberMe').optional().isBoolean(),
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { email, password, rememberMe } = req.body;

    // Find user
    const user = await ClientAccount.findOne({ where: { email } });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
    }

    // Generate token
    const token = generateToken(user);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        token,
        user: {
          id: user.id,
          email: user.email,
          fullName: user.full_name,
        },
      },
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed. Please try again.',
    });
  }
});

// Verify OTP endpoint
router.post('/verify-otp', [
  body('email').isEmail().normalizeEmail(),
  body('otp').isLength({ min: 6, max: 6 }).isNumeric(),
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { email, otp } = req.body;

    // Find user
    const user = await ClientAccount.findOne({ where: { email } });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Find OTP record
    const otpRecord = await OtpVerification.findOne({
      where: {
        client_id: user.id,
        otp_code: otp,
        is_used: false,
        expires_at: {
          [Sequelize.Op.gt]: new Date(),
        },
      },
      order: [['created_at', 'DESC']],
    });

    if (!otpRecord) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired OTP',
      });
    }

    // Mark OTP as used
    await otpRecord.update({ is_used: true });

    // Mark user as verified
    await user.update({ is_verified: true });

    // Generate token
    const token = generateToken(user);

    res.json({
      success: true,
      message: 'Email verified successfully',
      data: {
        token,
        user: {
          id: user.id,
          email: user.email,
          fullName: user.full_name,
        },
      },
    });

  } catch (error) {
    console.error('OTP verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Verification failed. Please try again.',
    });
  }
});

// Resend OTP endpoint
router.post('/resend-otp', [
  body('email').isEmail().normalizeEmail(),
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { email } = req.body;

    // Find user
    const user = await ClientAccount.findOne({ where: { email } });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Check if user is already verified
    if (user.is_verified) {
      return res.status(400).json({
        success: false,
        message: 'User is already verified',
      });
    }

    // Generate new OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000);

    await OtpVerification.create({
      client_id: user.id,
      otp_code: otp,
      expires_at: expiresAt,
      purpose: 'resend',
      attempts: 0,
    });

    // DEMO MODE: Return OTP code directly instead of sending email
    // In production, remove otpCode from response and send via email
    console.log(`DEMO MODE - New OTP for ${email}: ${otp}`);

    res.json({
      success: true,
      message: 'OTP sent successfully',
    });

  } catch (error) {
    console.error('Resend OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send OTP. Please try again.',
    });
  }
});

export default router;
