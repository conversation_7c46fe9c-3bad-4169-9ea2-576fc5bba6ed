// Sequelize Database Configuration - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Updated: November 19, 2025 (Migrated to PostgreSQL 16+)
// Purpose: Database configuration for development, test, and production environments
// Security: Environment-based configuration with secure defaults
// Standards: Sequelize v6+ configuration with PostgreSQL dialect

import dotenv from 'dotenv';
dotenv.config(); // Load environment variables

export default {
  // Development environment configuration
  development: {
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'smart_governance_auth',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    logging: console.log,
    timezone: '+00',
    dialectOptions: {
      ssl: process.env.DB_SSL === 'true' ? { require: true, rejectUnauthorized: false } : false,
    },
  },

  // Test environment configuration
  test: {
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME_TEST || 'smart_governance_auth_test',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    logging: false,
    timezone: '+00',
    dialectOptions: {
      ssl: false,
    },
  },

  // Production environment configuration
  production: {
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    pool: {
      max: 20,
      min: 5,
      acquire: 60000,
      idle: 20000,
    },
    logging: false,
    timezone: '+00',
    dialectOptions: {
      ssl: process.env.DB_SSL === 'true' ? { require: true, rejectUnauthorized: false } : false,
      connectTimeout: 60000,
      statement_timeout: 30000,
      idle_in_transaction_session_timeout: 300000,
    },
    define: {
      timestamps: true,
      underscored: false,
      paranoid: false,
    },
  },
};
