-- MySQL Test Validation Scripts for Smart Governance Authentication Database
-- BSCS Mini-Thesis Project - Holy Trinity College
-- Created: November 19, 2025
-- Purpose: Comprehensive testing of schema constraints, relationships, and security measures
-- Standards: Executable SQL to validate all database components and constraints

USE smart_governance_auth;

-- ============================================================================
-- SCHEMA VERIFICATION
-- ============================================================================

-- Verify table structures
DESCRIBE client_accounts;
DESCRIBE otp_verification;

-- Check indexes
SHOW INDEX FROM client_accounts;
SHOW INDEX FROM otp_verification;

-- ============================================================================
-- SAMPLE DATA INSERTION
-- ============================================================================

-- Insert test user with bcrypt-hashed password
-- Password: "TestPassword123!" (hashed with cost factor 10)
INSERT INTO client_accounts (id, email, password_hash, full_name, contact_number, is_verified)
VALUES (
    UUID(),
    '<EMAIL>',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- bcrypt hash for "TestPassword123!"
    'Test User',
    '+************',
    FALSE
);

-- Get the inserted user ID for OTP testing
SET @test_user_id = (SELECT id FROM client_accounts WHERE email = '<EMAIL>' LIMIT 1);

-- Insert valid OTP for registration
INSERT INTO otp_verification (id, client_id, otp_code, expires_at, is_used, purpose)
VALUES (
    UUID(),
    @test_user_id,
    '123456',
    DATE_ADD(NOW(), INTERVAL 5 MINUTE), -- Valid for 5 minutes
    FALSE,
    'Registration'
);

-- Insert expired OTP for testing
INSERT INTO otp_verification (id, client_id, otp_code, expires_at, is_used, purpose)
VALUES (
    UUID(),
    @test_user_id,
    '654321',
    DATE_SUB(NOW(), INTERVAL 1 MINUTE), -- Already expired
    FALSE,
    'Registration'
);

-- ============================================================================
-- CONSTRAINT TESTING
-- ============================================================================

-- Test 1: UNIQUE constraint on email (should fail)
-- Expected: Error 1062 (23000): Duplicate entry '<EMAIL>' for key 'email'
INSERT INTO client_accounts (id, email, password_hash, full_name)
VALUES (UUID(), '<EMAIL>', '$2a$10$dummy.hash', 'Duplicate User');

-- Test 2: FOREIGN KEY constraint (should fail)
-- Expected: Error 1452 (23000): Cannot add or update a child row: a foreign key constraint fails
INSERT INTO otp_verification (id, client_id, otp_code, expires_at)
VALUES (UUID(), UUID(), '999999', DATE_ADD(NOW(), INTERVAL 5 MINUTE));

-- Test 3: ENUM constraint (should fail)
-- Expected: Error 1265 (01000): Data truncated for column 'purpose' at row 1
INSERT INTO otp_verification (id, client_id, otp_code, expires_at, purpose)
VALUES (UUID(), @test_user_id, '888888', DATE_ADD(NOW(), INTERVAL 5 MINUTE), 'Invalid Purpose');

-- Test 4: NOT NULL constraint on required fields (should fail)
-- Expected: Error 1048 (23000): Column 'email' cannot be null
INSERT INTO client_accounts (id, password_hash, full_name)
VALUES (UUID(), '$2a$10$dummy.hash', 'Null Email User');

-- ============================================================================
-- QUERY TESTING
-- ============================================================================

-- Test 5: Valid OTP verification query
-- Expected: Should return 1 row (the valid OTP)
SELECT * FROM otp_verification
WHERE client_id = @test_user_id
  AND otp_code = '123456'
  AND is_used = FALSE
  AND expires_at > NOW();

-- Test 6: Expired OTP verification query
-- Expected: Should return 0 rows (expired OTP)
SELECT * FROM otp_verification
WHERE client_id = @test_user_id
  AND otp_code = '654321'
  AND is_used = FALSE
  AND expires_at > NOW();

-- Test 7: User login query (email lookup)
-- Expected: Should return 1 row with user data
SELECT id, email, full_name, is_verified, created_at
FROM client_accounts
WHERE email = '<EMAIL>';

-- Test 8: Join query - User with pending OTPs
-- Expected: Should return user with their valid OTPs
SELECT
    ca.id,
    ca.email,
    ca.full_name,
    ca.is_verified,
    ov.otp_code,
    ov.expires_at,
    ov.purpose
FROM client_accounts ca
LEFT JOIN otp_verification ov ON ca.id = ov.client_id
WHERE ca.email = '<EMAIL>'
  AND (ov.is_used = FALSE OR ov.is_used IS NULL)
  AND (ov.expires_at > NOW() OR ov.expires_at IS NULL);

-- Test 9: Statistics query
-- Expected: Should show counts of verified/unverified users
SELECT
    COUNT(*) as total_users,
    SUM(CASE WHEN is_verified = TRUE THEN 1 ELSE 0 END) as verified_users,
    SUM(CASE WHEN is_verified = FALSE THEN 1 ELSE 0 END) as unverified_users,
    ROUND((SUM(CASE WHEN is_verified = TRUE THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as verification_rate_percent
FROM client_accounts;

-- ============================================================================
-- UPDATE TESTING (SIMULATE VERIFICATION FLOW)
-- ============================================================================

-- Test 10: Mark OTP as used after verification
-- Expected: Should update 1 row
UPDATE otp_verification
SET is_used = TRUE, updated_at = CURRENT_TIMESTAMP
WHERE client_id = @test_user_id
  AND otp_code = '123456'
  AND is_used = FALSE;

-- Test 11: Mark user as verified
-- Expected: Should update 1 row
UPDATE client_accounts
SET is_verified = TRUE, updated_at = CURRENT_TIMESTAMP
WHERE email = '<EMAIL>';

-- Verify the updates
SELECT email, is_verified FROM client_accounts WHERE email = '<EMAIL>';
SELECT otp_code, is_used FROM otp_verification WHERE client_id = @test_user_id;

-- ============================================================================
-- CASCADE DELETION TESTING
-- ============================================================================

-- Test 12: Delete user and verify cascade deletion of OTPs
-- Expected: User deletion should automatically delete associated OTPs
SELECT COUNT(*) as otps_before_delete FROM otp_verification WHERE client_id = @test_user_id;

DELETE FROM client_accounts WHERE email = '<EMAIL>';

SELECT COUNT(*) as otps_after_delete FROM otp_verification WHERE client_id = @test_user_id;
SELECT COUNT(*) as users_after_delete FROM client_accounts WHERE email = '<EMAIL>';

-- ============================================================================
-- CLEANUP EXPIRED OTPS (MAINTENANCE)
-- ============================================================================

-- Test 13: Cleanup expired OTPs
-- Expected: Should delete expired OTPs (at least the one we inserted)
SELECT COUNT(*) as expired_otps_before_cleanup
FROM otp_verification
WHERE expires_at < NOW();

DELETE FROM otp_verification WHERE expires_at < NOW();

SELECT COUNT(*) as expired_otps_after_cleanup
FROM otp_verification
WHERE expires_at < NOW();

-- ============================================================================
-- FINAL VERIFICATION
-- ============================================================================

-- Verify all tables are clean after testing
SELECT COUNT(*) as remaining_users FROM client_accounts;
SELECT COUNT(*) as remaining_otps FROM otp_verification;

-- Expected Results Summary:
-- 1. UNIQUE constraint test: Should show error about duplicate entry
-- 2. FOREIGN KEY constraint test: Should show error about foreign key constraint
-- 3. ENUM constraint test: Should show error about data truncation
-- 4. NOT NULL constraint test: Should show error about null column
-- 5. Valid OTP query: Should return 1 row before marking as used
-- 6. Expired OTP query: Should return 0 rows
-- 7. User login query: Should return 1 row with user data
-- 8. Join query: Should return user with OTP data
-- 9. Statistics query: Should show user counts and verification rate
-- 10. OTP update: Should affect 1 row
-- 11. User verification update: Should affect 1 row
-- 12. Cascade deletion: OTPs should be 0 after user deletion
-- 13. Cleanup: Should delete expired OTPs
-- 14. Final counts: Should be 0 users and 0 OTPs (clean state)

-- ============================================================================
-- CLEANUP TEST DATA (OPTIONAL - Uncomment to clean database)
-- ============================================================================

-- WARNING: Only run this if you want to remove all test data
-- DELETE FROM otp_verification WHERE client_id IN (SELECT id FROM client_accounts WHERE email LIKE 'test%');
-- DELETE FROM client_accounts WHERE email LIKE 'test%';
