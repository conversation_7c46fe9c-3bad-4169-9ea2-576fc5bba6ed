# Diagram 10: Component Diagram

## Overview
This component diagram shows the frontend component hierarchy, including pages, UI components, services, context providers, and their relationships in the React application.

---

## Component Structure
- **Pages:** Top-level route components
- **UI Components:** Reusable UI elements (shadcn/ui)
- **Services:** API communication layer
- **Context:** Global state management
- **Hooks:** Custom React hooks
- **Utils:** Utility functions

---

## Mermaid Component Diagram

```mermaid
graph TB
    subgraph "React Application (Port 5173)"
        subgraph "App Component"
            App[App.tsx<br/>Main Application]
            Router[React Router<br/>Route Configuration]
        end
        
        subgraph "Context Providers"
            AuthContext[AuthContext<br/>User authentication state<br/>JWT token management]
            ThemeContext[ThemeContext<br/>Dark/Light mode]
        end
        
        subgraph "Pages (Routes)"
            HomePage[HomePage<br/>/]
            LoginPage[LoginPage<br/>/login]
            RegisterPage[RegisterPage<br/>/register]
            OTPPage[OTPVerificationPage<br/>/verify-otp]
            DashboardPage[DashboardPage<br/>/dashboard]
            ProfilePage[ProfilePage<br/>/profile]
            BusinessPage[BusinessProfilePage<br/>/business-profiles]
            ApplicationPage[ApplicationFormPage<br/>/applications/new]
            StatusPage[ApplicationStatusPage<br/>/applications]
            DocumentPage[DocumentUploadPage<br/>/applications/:id/documents]
            NotificationPage[NotificationsPage<br/>/notifications]
            FeedbackPage[FeedbackPage<br/>/feedback]
        end
        
        subgraph "UI Components (shadcn/ui)"
            Button[Button<br/>Reusable button]
            Input[Input<br/>Text input field]
            Card[Card<br/>Container component]
            Select[Select<br/>Dropdown select]
            Textarea[Textarea<br/>Multi-line input]
            Badge[Badge<br/>Status indicator]
            Dialog[Dialog<br/>Modal dialog]
            Toast[Toast<br/>Notification toast]
            Table[Table<br/>Data table]
            Form[Form<br/>Form wrapper]
        end
        
        subgraph "Custom Components"
            ProtectedRoute[ProtectedRoute<br/>Auth guard]
            Navbar[Navbar<br/>Navigation bar]
            Sidebar[Sidebar<br/>Side navigation]
            FileUpload[FileUpload<br/>File upload component]
            StatusBadge[StatusBadge<br/>Application status]
            NotificationBell[NotificationBell<br/>Notification icon]
        end
        
        subgraph "Services (API Layer)"
            AuthService[authService<br/>POST /api/auth/register<br/>POST /api/auth/login<br/>POST /api/auth/verify-otp]
            ProfileService[profileService<br/>GET /api/profile<br/>PUT /api/profile<br/>PUT /api/profile/password]
            BusinessService[businessService<br/>GET /api/business-profiles<br/>POST /api/business-profiles<br/>PUT /api/business-profiles/:id]
            ApplicationService[applicationService<br/>GET /api/applications<br/>POST /api/applications<br/>POST /api/applications/:id/submit]
            DocumentService[documentService<br/>POST /api/documents/upload<br/>GET /api/documents/:id<br/>DELETE /api/documents/:id]
            NotificationService[notificationService<br/>GET /api/notifications<br/>PUT /api/notifications/:id/read<br/>PUT /api/notifications/read-all]
            FeedbackService[feedbackService<br/>POST /api/feedback<br/>GET /api/feedback/stats<br/>GET /api/feedback/my-feedback]
        end
        
        subgraph "Utilities"
            AxiosConfig[Axios Configuration<br/>Base URL: http://localhost:3001<br/>JWT interceptor<br/>Error handling]
            Validators[Yup Validators<br/>Email validation<br/>Password strength<br/>Phone number format]
            Formatters[Formatters<br/>Date formatting<br/>File size formatting<br/>Currency formatting]
        end
        
        subgraph "Hooks"
            useAuth[useAuth<br/>Access AuthContext]
            useToast[useToast<br/>Show toast notifications]
            useForm[useForm<br/>React Hook Form]
        end
    end
    
    %% App Structure
    App --> Router
    App --> AuthContext
    App --> ThemeContext
    
    %% Router to Pages
    Router --> HomePage
    Router --> LoginPage
    Router --> RegisterPage
    Router --> OTPPage
    Router --> DashboardPage
    Router --> ProfilePage
    Router --> BusinessPage
    Router --> ApplicationPage
    Router --> StatusPage
    Router --> DocumentPage
    Router --> NotificationPage
    Router --> FeedbackPage
    
    %% Protected Routes
    ProtectedRoute --> DashboardPage
    ProtectedRoute --> ProfilePage
    ProtectedRoute --> BusinessPage
    ProtectedRoute --> ApplicationPage
    ProtectedRoute --> StatusPage
    ProtectedRoute --> DocumentPage
    ProtectedRoute --> NotificationPage
    ProtectedRoute --> FeedbackPage
    
    %% Pages use UI Components
    LoginPage --> Button
    LoginPage --> Input
    LoginPage --> Card
    LoginPage --> Form
    
    RegisterPage --> Button
    RegisterPage --> Input
    RegisterPage --> Card
    RegisterPage --> Form
    
    ProfilePage --> Button
    ProfilePage --> Input
    ProfilePage --> Card
    
    BusinessPage --> Button
    BusinessPage --> Input
    BusinessPage --> Card
    BusinessPage --> Table
    BusinessPage --> Dialog
    
    ApplicationPage --> Button
    ApplicationPage --> Input
    ApplicationPage --> Select
    ApplicationPage --> Card
    
    StatusPage --> Card
    StatusPage --> Table
    StatusPage --> Badge
    StatusPage --> StatusBadge
    
    DocumentPage --> Button
    DocumentPage --> Card
    DocumentPage --> FileUpload
    DocumentPage --> Table
    
    NotificationPage --> Card
    NotificationPage --> Badge
    NotificationPage --> NotificationBell
    
    FeedbackPage --> Button
    FeedbackPage --> Textarea
    FeedbackPage --> Card
    
    %% Pages use Services
    LoginPage --> AuthService
    RegisterPage --> AuthService
    OTPPage --> AuthService
    ProfilePage --> ProfileService
    BusinessPage --> BusinessService
    ApplicationPage --> ApplicationService
    StatusPage --> ApplicationService
    DocumentPage --> DocumentService
    NotificationPage --> NotificationService
    FeedbackPage --> FeedbackService
    
    %% Services use Axios
    AuthService --> AxiosConfig
    ProfileService --> AxiosConfig
    BusinessService --> AxiosConfig
    ApplicationService --> AxiosConfig
    DocumentService --> AxiosConfig
    NotificationService --> AxiosConfig
    FeedbackService --> AxiosConfig
    
    %% Pages use Hooks
    LoginPage --> useAuth
    LoginPage --> useToast
    LoginPage --> useForm
    
    ProfilePage --> useAuth
    ProfilePage --> useToast
    ProfilePage --> useForm
    
    %% Pages use Validators
    LoginPage --> Validators
    RegisterPage --> Validators
    ProfilePage --> Validators
    
    %% Navbar uses Context
    Navbar --> AuthContext
    Navbar --> NotificationBell
    
    %% Styling
    classDef page fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef component fill:#10b981,stroke:#059669,color:#fff
    classDef service fill:#8b5cf6,stroke:#7c3aed,color:#fff
    classDef context fill:#f59e0b,stroke:#d97706,color:#fff
    classDef util fill:#6b7280,stroke:#4b5563,color:#fff
    
    class HomePage,LoginPage,RegisterPage,OTPPage,DashboardPage,ProfilePage,BusinessPage,ApplicationPage,StatusPage,DocumentPage,NotificationPage,FeedbackPage page
    class Button,Input,Card,Select,Textarea,Badge,Dialog,Toast,Table,Form,ProtectedRoute,Navbar,Sidebar,FileUpload,StatusBadge,NotificationBell component
    class AuthService,ProfileService,BusinessService,ApplicationService,DocumentService,NotificationService,FeedbackService service
    class AuthContext,ThemeContext context
    class AxiosConfig,Validators,Formatters,useAuth,useToast,useForm util
```

---

## Component Details

### Pages (12 Total)

#### Public Pages
1. **HomePage** (`/`) - Landing page with system overview
2. **LoginPage** (`/login`) - User login form
3. **RegisterPage** (`/register`) - User registration form
4. **OTPVerificationPage** (`/verify-otp`) - Email verification with OTP

#### Protected Pages (Require Authentication)
5. **DashboardPage** (`/dashboard`) - User dashboard with statistics
6. **ProfilePage** (`/profile`) - View and edit user profile
7. **BusinessProfilePage** (`/business-profiles`) - Manage business profiles
8. **ApplicationFormPage** (`/applications/new`) - Create new application
9. **ApplicationStatusPage** (`/applications`) - View all applications
10. **DocumentUploadPage** (`/applications/:id/documents`) - Upload documents
11. **NotificationsPage** (`/notifications`) - View notifications
12. **FeedbackPage** (`/feedback`) - Submit feedback

---

### UI Components (shadcn/ui - 10 Total)

1. **Button** - Reusable button with variants (default, outline, ghost)
2. **Input** - Text input field with validation
3. **Card** - Container component with header, content, footer
4. **Select** - Dropdown select component
5. **Textarea** - Multi-line text input
6. **Badge** - Status indicator (success, error, warning, info)
7. **Dialog** - Modal dialog for confirmations
8. **Toast** - Notification toast messages
9. **Table** - Data table with sorting and pagination
10. **Form** - Form wrapper with React Hook Form integration

---

### Custom Components (6 Total)

1. **ProtectedRoute** - Authentication guard for protected routes
2. **Navbar** - Top navigation bar with user menu
3. **Sidebar** - Side navigation menu
4. **FileUpload** - File upload component with drag-and-drop
5. **StatusBadge** - Application status badge with colors
6. **NotificationBell** - Notification icon with unread count

---

### Services (7 Total)

1. **authService** - Authentication API calls
2. **profileService** - Profile management API calls
3. **businessService** - Business profile API calls
4. **applicationService** - Application API calls
5. **documentService** - Document upload API calls
6. **notificationService** - Notification API calls
7. **feedbackService** - Feedback API calls

---

### Context Providers (2 Total)

1. **AuthContext** - User authentication state, JWT token management
2. **ThemeContext** - Dark/Light mode theme management

---

### Utilities (3 Categories)

1. **AxiosConfig** - Axios instance with base URL and JWT interceptor
2. **Validators** - Yup validation schemas
3. **Formatters** - Date, file size, currency formatters

---

### Hooks (3 Total)

1. **useAuth** - Access AuthContext (user, token, login, logout)
2. **useToast** - Show toast notifications
3. **useForm** - React Hook Form integration

---

## Component Dependencies

### Authentication Flow
```
LoginPage → useAuth → AuthContext → authService → AxiosConfig → Backend API
```

### Protected Route Flow
```
ProtectedRoute → useAuth → AuthContext → (Redirect to /login if not authenticated)
```

### Form Submission Flow
```
Page → useForm → Validators → Service → AxiosConfig → Backend API → useToast
```

### File Upload Flow
```
DocumentPage → FileUpload → documentService → AxiosConfig → Multer Middleware
```

---

## Technology Stack

### Core Libraries
- **React:** 18.2.0
- **React Router:** 6.20.0
- **TypeScript:** 5.3.3
- **Vite:** 5.0.7

### UI Libraries
- **Tailwind CSS:** 3.3.6
- **shadcn/ui:** Latest (Radix UI components)
- **Lucide React:** Latest (Icons)
- **Framer Motion:** 10.16.5 (Animations)

### Form Management
- **React Hook Form:** 7.48.2
- **Yup:** 1.3.3

### HTTP Client
- **Axios:** 1.6.2

---

**Diagram Status:** ✅ Complete  
**Total Components:** 38  
**Pages:** 12  
**UI Components:** 16  
**Services:** 7  
**Context Providers:** 2  
**Last Updated:** November 20, 2025

