// Notification Service - Notification Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import apiClient from './apiClient';

export interface Notification {
  id: string;
  client_id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  is_read: boolean;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export const notificationService = {
  /**
   * Get all notifications for authenticated user
   */
  getNotifications: async (unreadOnly: boolean = false, limit: number = 50) => {
    const response = await apiClient.get<{ success: boolean; data: Notification[]; unread_count: number }>(
      '/notifications',
      {
        params: { unread_only: unreadOnly, limit },
      }
    );
    return response.data;
  },

  /**
   * Get unread notification count
   */
  getUnreadCount: async () => {
    const response = await apiClient.get<{ success: boolean; unread_count: number }>(
      '/notifications/unread-count'
    );
    return response.data;
  },

  /**
   * Mark notification as read
   */
  markAsRead: async (id: string) => {
    const response = await apiClient.put<{ success: boolean; message: string; data: Notification }>(
      `/notifications/${id}/read`
    );
    return response.data;
  },

  /**
   * Mark all notifications as read
   */
  markAllAsRead: async () => {
    const response = await apiClient.put<{ success: boolean; message: string; updated_count: number }>(
      '/notifications/read-all'
    );
    return response.data;
  },
};

