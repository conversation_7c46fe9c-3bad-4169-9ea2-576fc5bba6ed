// Profile Routes - User Profile Management API
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define API routes for user profile operations

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { getProfile, updateProfile } = require('../controllers/profileController');

/**
 * @route   GET /api/profile
 * @desc    Get user profile
 * @access  Private
 */
router.get('/', authenticateToken, getProfile);

/**
 * @route   PUT /api/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/', authenticateToken, updateProfile);

export default router;

