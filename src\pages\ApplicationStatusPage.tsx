// Application Status Page - Track Application Status
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FileText, Clock, CheckCircle, XCircle, AlertCircle, Eye, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

import { applicationService, BusinessApplication } from '@/services/applicationService';
import { useToast } from '@/hooks/useToast';

const ApplicationStatusPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [applications, setApplications] = useState<BusinessApplication[]>([]);
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      setIsLoading(true);
      const response = await applicationService.getApplications();
      setApplications(response.data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load applications',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <Clock className="w-5 h-5" />;
      case 'submitted':
      case 'under_review':
        return <AlertCircle className="w-5 h-5" />;
      case 'approved':
        return <CheckCircle className="w-5 h-5" />;
      case 'rejected':
        return <XCircle className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'draft':
        return 'draft';
      case 'submitted':
        return 'submitted';
      case 'under_review':
        return 'info';
      case 'approved':
        return 'approved';
      case 'rejected':
        return 'rejected';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
      <div className="max-w-5xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Application Status
            </h1>
            <p className="text-gray-600 mt-1">Track your permit applications</p>
          </div>
          <Button onClick={() => navigate('/applications/new')} className="gap-2">
            <FileText className="w-5 h-5" />
            New Application
          </Button>
        </motion.div>

        {/* Applications List */}
        {applications.length === 0 ? (
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardContent className="py-12 text-center">
              <FileText className="w-16 h-16 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Applications</h3>
              <p className="text-gray-600 mb-4">You haven't submitted any applications yet</p>
              <Button onClick={() => navigate('/applications/new')} className="gap-2">
                <FileText className="w-5 h-5" />
                Create Application
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {applications.map((application, index) => (
              <motion.div
                key={application.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-4">
                        <div className="p-3 bg-blue-100 rounded-lg">
                          {getStatusIcon(application.status)}
                        </div>
                        <div>
                          <h3 className="text-lg font-bold text-gray-900">
                            {application.businessProfile?.business_name || 'Business Name'}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {application.application_type === 'new' ? 'New Permit' : 'Renewal'} Application
                          </p>
                        </div>
                      </div>
                      <Badge variant={getStatusBadgeVariant(application.status)}>
                        {application.status.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <p className="text-gray-500">Created</p>
                        <p className="font-medium text-gray-900">
                          {formatDate(application.created_at)}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-500">Submitted</p>
                        <p className="font-medium text-gray-900">
                          {formatDate(application.submitted_at)}
                        </p>
                      </div>
                      {application.reviewed_at && (
                        <div>
                          <p className="text-gray-500">Reviewed</p>
                          <p className="font-medium text-gray-900">
                            {formatDate(application.reviewed_at)}
                          </p>
                        </div>
                      )}
                      {application.notes && (
                        <div className="col-span-2">
                          <p className="text-gray-500 mb-1">Notes</p>
                          <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
                            {application.notes}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate(`/applications/${application.id}`)}
                        className="gap-2"
                      >
                        <Eye className="w-4 h-4" />
                        View Details
                      </Button>
                      {application.status === 'draft' && (
                        <Button
                          size="sm"
                          onClick={() => navigate(`/applications/${application.id}/documents`)}
                          className="gap-2"
                        >
                          Continue Application
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationStatusPage;

