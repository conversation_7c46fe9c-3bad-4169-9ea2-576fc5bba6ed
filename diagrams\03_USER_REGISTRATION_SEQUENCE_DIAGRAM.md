# Diagram 3: User Registration Sequence Diagram

## Overview
This sequence diagram shows the complete user registration flow from form submission through backend validation, password hashing with bcrypt, OTP generation, email sending, and database insertion.

---

## Process Flow
1. User submits registration form
2. Frontend validates input (client-side)
3. Backend validates input (server-side)
4. Check email uniqueness
5. Hash password with bcrypt (10 rounds)
6. Generate 6-digit OTP
7. Create user account
8. Create OTP record
9. Send OTP email
10. Return success response

---

## Mermaid Sequence Diagram

```mermaid
sequenceDiagram
    actor User
    participant U<PERSON> as React Frontend<br/>(RegistrationPage)
    participant Form as React Hook Form<br/>+ Yup Validation
    participant Axios as Axios HTTP Client<br/>(authService)
    participant Express as Express Server<br/>(Port 3001)
    participant AuthRoute as /api/auth/register<br/>(authRoutes)
    participant AuthCtrl as authController<br/>(register function)
    participant bcrypt as bcrypt Library<br/>(10 rounds)
    participant O<PERSON> as OTP Generator<br/>(6 digits)
    participant Model as Sequelize Models<br/>(ClientAccount, OtpVerification)
    participant DB as PostgreSQL 16<br/>(smart_governance_auth)
    participant Em<PERSON> as Nodemailer<br/>(Email Service)

    %% User Input
    User->>UI: Enter email, password, full name
    UI->>Form: Submit registration form

    %% Client-side Validation
    Form->>Form: Validate email format
    Form->>Form: Validate password length (min 8)
    Form->>Form: Validate full name (required)
    
    alt Validation Fails
        Form-->>UI: Display validation errors
        UI-->>User: Show error messages
    end

    %% API Request
    Form->>Axios: POST /api/auth/register<br/>{email, password, full_name}
    Axios->>Express: HTTP POST with JSON body

    %% Backend Routing
    Express->>AuthRoute: Route to /api/auth/register
    AuthRoute->>AuthCtrl: Call register(req, res)

    %% Server-side Validation
    AuthCtrl->>AuthCtrl: Validate request body
    AuthCtrl->>AuthCtrl: Check required fields

    alt Missing Required Fields
        AuthCtrl-->>Express: 400 Bad Request
        Express-->>Axios: Error response
        Axios-->>UI: Display error
        UI-->>User: "All fields are required"
    end

    %% Check Email Uniqueness
    AuthCtrl->>Model: ClientAccount.findOne({where: {email}})
    Model->>DB: SELECT * FROM client_accounts WHERE email = ?
    DB-->>Model: Query result
    Model-->>AuthCtrl: User object or null

    alt Email Already Exists
        AuthCtrl-->>Express: 400 Bad Request
        Express-->>Axios: {message: "Email already registered"}
        Axios-->>UI: Display error
        UI-->>User: "Email already registered"
    end

    %% Hash Password
    AuthCtrl->>bcrypt: genSalt(10)
    bcrypt-->>AuthCtrl: Salt generated
    AuthCtrl->>bcrypt: hash(password, salt)
    Note over bcrypt: bcrypt hashing<br/>10 rounds<br/>~100ms processing time
    bcrypt-->>AuthCtrl: password_hash

    %% Generate OTP
    AuthCtrl->>OTP: Generate 6-digit OTP
    Note over OTP: Random 6-digit code<br/>Example: 123456
    OTP-->>AuthCtrl: otp_code
    AuthCtrl->>AuthCtrl: Calculate expires_at<br/>(current time + 10 minutes)

    %% Create User Account
    AuthCtrl->>Model: ClientAccount.create({<br/>email, password_hash, full_name,<br/>is_verified: false})
    Model->>DB: INSERT INTO client_accounts<br/>(id, email, password_hash, full_name,<br/>is_verified, created_at, updated_at)<br/>VALUES (uuid_generate_v4(), ?, ?, ?, FALSE, NOW(), NOW())
    DB-->>Model: New user record with UUID
    Model-->>AuthCtrl: user object

    %% Create OTP Record
    AuthCtrl->>Model: OtpVerification.create({<br/>client_account_id, otp_code,<br/>purpose: 'registration', expires_at})
    Model->>DB: INSERT INTO otp_verification<br/>(id, client_account_id, otp_code,<br/>purpose, expires_at, created_at, updated_at)<br/>VALUES (uuid_generate_v4(), ?, ?, 'registration', ?, NOW(), NOW())
    DB-->>Model: New OTP record
    Model-->>AuthCtrl: otp object

    %% Send OTP Email
    AuthCtrl->>Email: sendMail({<br/>to: email,<br/>subject: "Verify Your Email",<br/>text: "Your OTP: " + otp_code})
    Note over Email: In demo mode:<br/>console.log(OTP)<br/>Production: SMTP send
    Email-->>AuthCtrl: Email sent (or logged)

    %% Success Response
    AuthCtrl->>AuthCtrl: Prepare response<br/>(exclude password_hash)
    AuthCtrl-->>Express: 201 Created<br/>{message, user: {id, email, full_name}}
    Express-->>Axios: Success response
    Axios-->>UI: Registration successful
    UI-->>User: "Registration successful!<br/>Please check your email for OTP"
    UI->>UI: Navigate to OTP verification page

    %% Error Handling
    Note over AuthCtrl,DB: Error Handling:<br/>- Database errors: 500 Internal Server Error<br/>- Validation errors: 400 Bad Request<br/>- Email send failure: Log error but continue
```

---

## Step-by-Step Breakdown

### Step 1: User Input (Frontend)
- User fills registration form: email, password, full name
- React Hook Form manages form state
- Real-time validation feedback

### Step 2: Client-Side Validation (Yup Schema)
```typescript
const registrationSchema = yup.object({
  email: yup.string().email().required(),
  password: yup.string().min(8).required(),
  full_name: yup.string().required()
});
```

### Step 3: API Request (Axios)
```typescript
POST http://localhost:3001/api/auth/register
Headers: { "Content-Type": "application/json" }
Body: { email, password, full_name }
```

### Step 4: Server-Side Validation
- Check all required fields present
- Validate email format
- Validate password length

### Step 5: Email Uniqueness Check
```sql
SELECT * FROM client_accounts WHERE email = '<EMAIL>';
```
- If exists: Return 400 error
- If not exists: Continue

### Step 6: Password Hashing (bcrypt)
```javascript
const salt = await bcrypt.genSalt(10);
const password_hash = await bcrypt.hash(password, salt);
```
- **Rounds:** 10 (2^10 = 1,024 iterations)
- **Time:** ~100ms
- **Output:** 60-character hash

### Step 7: OTP Generation
```javascript
const otp_code = Math.floor(100000 + Math.random() * 900000).toString();
const expires_at = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
```

### Step 8: Database Insertion (User)
```sql
INSERT INTO client_accounts (
  id, email, password_hash, full_name, is_verified, created_at, updated_at
) VALUES (
  uuid_generate_v4(), '<EMAIL>', '$2b$10$...', 'John Doe', FALSE, NOW(), NOW()
);
```

### Step 9: Database Insertion (OTP)
```sql
INSERT INTO otp_verification (
  id, client_account_id, otp_code, purpose, expires_at, created_at, updated_at
) VALUES (
  uuid_generate_v4(), 'user-uuid', '123456', 'registration', '2025-11-20 10:10:00', NOW(), NOW()
);
```

### Step 10: Email Sending
```javascript
// Demo mode (console.log)
console.log(`OTP for ${email}: ${otp_code}`);

// Production mode (SMTP)
await transporter.sendMail({
  to: email,
  subject: 'Verify Your Email - Smart Governance',
  text: `Your OTP code is: ${otp_code}. Valid for 10 minutes.`
});
```

### Step 11: Success Response
```json
{
  "message": "Registration successful. Please check your email for OTP.",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "is_verified": false
  }
}
```

---

## Error Scenarios

| Error Type | HTTP Status | Response Message | User Action |
|------------|-------------|------------------|-------------|
| Missing fields | 400 | "All fields are required" | Fill all fields |
| Invalid email format | 400 | "Invalid email format" | Correct email |
| Password too short | 400 | "Password must be at least 8 characters" | Use longer password |
| Email already exists | 400 | "Email already registered" | Use different email or login |
| Database error | 500 | "Registration failed. Please try again." | Retry registration |
| Email send failure | 201 | Success (logged error) | Continue to OTP page |

---

## Security Considerations

1. **Password Hashing:** bcrypt with 10 rounds (industry standard)
2. **OTP Expiry:** 10-minute window to prevent replay attacks
3. **Email Verification:** Required before account activation
4. **No Password in Response:** password_hash excluded from all responses
5. **UUID Primary Keys:** Non-sequential, harder to enumerate
6. **Input Validation:** Both client and server-side
7. **HTTPS:** Required in production (not in local dev)

---

## Database Tables Involved

### client_accounts
- **Operation:** INSERT
- **Fields:** id (UUID), email, password_hash, full_name, is_verified (FALSE), created_at, updated_at

### otp_verification
- **Operation:** INSERT
- **Fields:** id (UUID), client_account_id (FK), otp_code, purpose ('registration'), expires_at, created_at, updated_at

---

## Performance Metrics

- **Client-side validation:** < 10ms
- **bcrypt hashing:** ~100ms (10 rounds)
- **Database INSERT:** ~50ms (2 queries)
- **Email sending:** ~200ms (SMTP) or instant (console.log)
- **Total time:** ~350-500ms

---

**Diagram Status:** ✅ Complete  
**Participants:** 11  
**Steps:** 30+  
**Error Paths:** 5  
**Last Updated:** November 20, 2025

