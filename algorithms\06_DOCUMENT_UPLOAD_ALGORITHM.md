# Algorithm 6: Document Upload and Validation

## Overview
This algorithm handles secure file uploads for business permit applications, including file type validation, size checking, storage management, and database record creation using Multer middleware.

---

## Algorithm Complexity
- **Time Complexity:** O(n) where n = file size (for file I/O operations)
- **Space Complexity:** O(n) where n = file size (temporary storage during upload)
- **Database Operations:** 2 queries (1 SELECT for validation, 1 INSERT for document record)

---

## Input Parameters
```typescript
interface DocumentUploadInput {
  application_id: UUID;          // Target application
  document_type: string;         // Type of document (DTI, Barangay Clearance, etc.)
  file: File;                    // Uploaded file (multipart/form-data)
  client_account_id: UUID;       // From JWT token
}
```

## Output
```typescript
interface DocumentUploadOutput {
  success: boolean;
  message: string;
  document?: {
    id: UUID;
    application_id: UUID;
    document_type: string;
    file_name: string;
    file_size: number;
    mime_type: string;
    created_at: Date;
  };
}
```

---

## Pseudocode

```
ALGORITHM UploadDocument(application_id, document_type, file, client_account_id)
BEGIN
  // Step 1: Validate File Presence
  IF file is empty THEN
    RETURN error("No file uploaded")
  END IF
  
  // Step 2: Validate File Type
  allowed_types ← ['application/pdf', 'image/jpeg', 'image/png']
  
  IF file.mimetype NOT IN allowed_types THEN
    RETURN error("Invalid file type. Only PDF, JPG, and PNG allowed")
  END IF
  
  // Step 3: Validate File Size (5MB maximum)
  max_size ← 5 * 1024 * 1024  // 5MB in bytes
  
  IF file.size > max_size THEN
    RETURN error("File size exceeds 5MB limit")
  END IF
  
  // Step 4: Verify Application Ownership and Status
  application ← DATABASE.query(
    "SELECT a.*, bp.client_account_id 
     FROM business_applications a
     JOIN business_profiles bp ON a.business_profile_id = bp.id
     WHERE a.id = ?",
    application_id
  )
  
  IF application NOT EXISTS THEN
    RETURN error(404, "Application not found")
  END IF
  
  IF application.client_account_id ≠ client_account_id THEN
    RETURN error(403, "Access denied")
  END IF
  
  IF application.status ≠ 'draft' THEN
    RETURN error("Cannot upload documents to submitted applications")
  END IF
  
  // Step 5: Generate Unique Filename
  timestamp ← Date.now()
  unique_id ← UUID.generate_v4()
  file_extension ← GET_FILE_EXTENSION(file.originalname)
  unique_filename ← timestamp + '-' + unique_id + '-' + file.originalname
  
  // Step 6: Save File to Disk (Multer handles this)
  file_path ← 'uploads/documents/' + unique_filename
  SAVE_FILE(file, file_path)
  
  // Step 7: Create Database Record
  document_id ← UUID.generate_v4()
  
  document ← DATABASE.insert("document_uploads", {
    id: document_id,
    application_id: application_id,
    document_type: document_type,
    file_path: file_path,
    file_name: file.originalname,
    file_size: file.size,
    mime_type: file.mimetype,
    metadata: {},
    created_at: CURRENT_TIMESTAMP,
    updated_at: CURRENT_TIMESTAMP
  })
  
  // Step 8: Return Success Response
  RETURN success({
    message: "Document uploaded successfully",
    document: document
  })
END

ALGORITHM DeleteDocument(document_id, client_account_id)
BEGIN
  // Step 1: Find Document with Application Status
  document ← DATABASE.query(
    "SELECT d.*, a.status, bp.client_account_id
     FROM document_uploads d
     JOIN business_applications a ON d.application_id = a.id
     JOIN business_profiles bp ON a.business_profile_id = bp.id
     WHERE d.id = ?",
    document_id
  )
  
  IF document NOT EXISTS THEN
    RETURN error(404, "Document not found")
  END IF
  
  // Step 2: Verify Ownership
  IF document.client_account_id ≠ client_account_id THEN
    RETURN error(403, "Access denied")
  END IF
  
  // Step 3: Check Application Status
  IF document.status ≠ 'draft' THEN
    RETURN error("Cannot delete documents from submitted applications")
  END IF
  
  // Step 4: Delete File from Disk
  TRY
    DELETE_FILE(document.file_path)
  CATCH error
    LOG_ERROR("Failed to delete file: " + document.file_path)
  END TRY
  
  // Step 5: Delete Database Record
  DATABASE.delete("document_uploads", WHERE id = document_id)
  
  // Step 6: Return Success Response
  RETURN success({
    message: "Document deleted successfully"
  })
END
```

---

## Flowchart (Upload)

```mermaid
flowchart TD
    Start([Start: Upload Document]) --> CheckFile{File<br/>Provided?}
    CheckFile -->|No| ErrorNoFile[Return Error:<br/>No file uploaded]
    CheckFile -->|Yes| ValidateType{File Type<br/>Valid?}
    ValidateType -->|No| ErrorType[Return Error:<br/>Invalid file type]
    ValidateType -->|Yes| ValidateSize{File Size<br/>≤ 5MB?}
    ValidateSize -->|No| ErrorSize[Return Error:<br/>File too large]
    ValidateSize -->|Yes| FindApp[Query: Find application<br/>with ownership check]
    FindApp --> AppExists{Application<br/>Found?}
    AppExists -->|No| ErrorNotFound[Return Error 404]
    AppExists -->|Yes| CheckOwner{User Owns<br/>Application?}
    CheckOwner -->|No| ErrorAccess[Return Error 403]
    CheckOwner -->|Yes| CheckStatus{Status is<br/>'draft'?}
    CheckStatus -->|No| ErrorSubmitted[Return Error:<br/>Cannot modify]
    CheckStatus -->|Yes| GenerateFilename[Generate unique filename<br/>with timestamp + UUID]
    GenerateFilename --> SaveFile[Save file to disk:<br/>uploads/documents/]
    SaveFile --> InsertDB[Insert record into<br/>document_uploads table]
    InsertDB --> Success[Return Success:<br/>Document uploaded]
    Success --> End([End])
    ErrorNoFile --> End
    ErrorType --> End
    ErrorSize --> End
    ErrorNotFound --> End
    ErrorAccess --> End
    ErrorSubmitted --> End
```

---

## Allowed Document Types

| Document Type | Description |
|---------------|-------------|
| DTI Registration | Department of Trade and Industry registration |
| Mayor's Permit | Previous mayor's permit (for renewal) |
| Barangay Clearance | Clearance from barangay |
| Fire Safety Certificate | Fire safety inspection certificate |
| Sanitary Permit | Health and sanitation permit |
| Environmental Clearance | Environmental compliance certificate |
| Building Permit | Building permit (if applicable) |
| Business Plan | Business plan document |
| Financial Statements | Financial documents |
| Other | Other supporting documents |

---

## File Validation Rules

### Allowed MIME Types
- `application/pdf` - PDF documents
- `image/jpeg` - JPEG images
- `image/png` - PNG images

### File Size Limit
- **Maximum:** 5MB (5,242,880 bytes)
- **Reason:** Balance between quality and server storage

### Filename Sanitization
- Original filename preserved in database
- Stored filename: `{timestamp}-{uuid}-{original_name}`
- Prevents filename collisions
- Maintains file extension

---

## Security Considerations

1. **File Type Validation:** Whitelist approach (only PDF, JPG, PNG)
2. **File Size Limit:** Prevents DoS attacks via large uploads
3. **Ownership Verification:** Users can only upload to their own applications
4. **Status Check:** Prevents modification of submitted applications
5. **Unique Filenames:** Prevents overwriting existing files
6. **Path Traversal Prevention:** Multer handles secure file paths

---

## Multer Configuration

```javascript
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/documents/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
    cb(null, `${uniqueSuffix}-${file.originalname}`);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: { fileSize: 5 * 1024 * 1024 }
});
```

---

## Database Tables Involved

### document_uploads
- **Operations:** INSERT, SELECT, DELETE
- **Fields:** id, application_id, document_type, file_path, file_name, file_size, mime_type, metadata

### business_applications
- **Operations:** SELECT (for validation)
- **Fields:** id, business_profile_id, status

### business_profiles
- **Operations:** SELECT (for ownership verification)
- **Fields:** id, client_account_id

---

## Implementation Files

- **Controller:** `controllers/documentController.js`
- **Model:** `models/DocumentUpload.js`
- **Middleware:** `middleware/upload.js`
- **Routes:** `routes/documents.js`
- **Frontend:** `src/pages/DocumentUploadPage.tsx`
- **Service:** `src/services/documentService.ts`

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

