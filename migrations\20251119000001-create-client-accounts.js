// Sequelize Migration: Create Client Accounts Table
// BSCS Mini-Thesis Project - Holy Trinity College
// Created: November 19, 2025
// Purpose: Migration to create client_accounts table with all constraints and indexes
// Standards: Sequelize v6+ migration syntax, MySQL 8.0+ compatibility

'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('client_accounts', {
      // Primary key: UUID v4 for secure identifiers
      id: {
        type: Sequelize.CHAR(36),
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        comment: 'Unique user identifier',
      },
      // Email with uniqueness constraint
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        comment: 'User email for login',
      },
      // Password hash (bcrypt)
      password_hash: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'bcrypt-hashed password',
      },
      // Full name
      full_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'User\'s full name',
      },
      // Optional contact number
      contact_number: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'Optional phone number',
      },
      // Verification status
      is_verified: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Email/OTP verification status',
      },
      // Timestamps
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: 'Account creation timestamp',
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
        comment: 'Last modification timestamp',
      },
    }, {
      // Table options
      engine: 'InnoDB',
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      comment: 'Store registered user information with secure password storage',
    });

    // Create indexes for performance
    await queryInterface.addIndex('client_accounts', ['email'], {
      name: 'idx_client_accounts_email_search',
      comment: 'Fast login queries',
    });

    await queryInterface.addIndex('client_accounts', ['is_verified'], {
      name: 'idx_client_accounts_is_verified',
      comment: 'Filter verified users',
    });
  },

  async down(queryInterface, Sequelize) {
    // Drop the table (reverse migration)
    await queryInterface.dropTable('client_accounts');
  }
};
