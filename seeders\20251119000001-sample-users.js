'use strict';

/**
 * PostgreSQL Sample Users Seeder
 * BSCS Mini-Thesis Project - Holy Trinity College PCMRC
 * Created: November 19, 2025
 * Purpose: Seed sample government users for testing Smart Governance Portal
 * Security: Uses bcrypt-hashed passwords, realistic government email domains
 * Standards: Sequelize with PostgreSQL UUID generation and JSONB metadata
 */

const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  async up(queryInterface, Sequelize) {
    // Hash the password using bcrypt for "AdminPass123!"
    const hashedPassword = '$2b$10$yVEepkJPPKXSMR3YfC3f7uxQrfKSvn2okVfmx7TBb06HOA65C.Zw2'; // bcrypt hash of "AdminPass123!"

    console.log('🌱 Seeding sample users for Smart Governance Portal...');

    // Insert sample verified users (government roles)
    await queryInterface.bulkInsert('client_accounts', [
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password_hash: hashedPassword,
        full_name: 'System Administrator',
        contact_number: '+************',
        is_verified: true,
        metadata: JSON.stringify({
          department: 'IT',
          position: 'System Administrator',
          clearance_level: 'admin',
          employee_id: 'ADM001'
        }),
        created_at: new Date('2025-11-19T08:00:00.000Z'),
        updated_at: new Date('2025-11-19T08:00:00.000Z'),
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password_hash: hashedPassword,
        full_name: 'Business Permit Clerk',
        contact_number: '+************',
        is_verified: true,
        metadata: JSON.stringify({
          department: 'BPLO',
          position: 'Permit Processing Clerk',
          clearance_level: 'standard',
          employee_id: 'CLK001'
        }),
        created_at: new Date('2025-11-19T08:15:00.000Z'),
        updated_at: new Date('2025-11-19T08:15:00.000Z'),
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password_hash: hashedPassword,
        full_name: 'Permit Inspector',
        contact_number: '+************',
        is_verified: true,
        metadata: JSON.stringify({
          department: 'BPLO',
          position: 'Field Inspector',
          clearance_level: 'inspector',
          employee_id: 'INS001'
        }),
        created_at: new Date('2025-11-19T08:30:00.000Z'),
        updated_at: new Date('2025-11-19T08:30:00.000Z'),
      },
      {
        id: uuidv4(),
        email: '<EMAIL>',
        password_hash: hashedPassword,
        full_name: 'New Applicant',
        contact_number: '+************',
        is_verified: false,
        metadata: JSON.stringify({
          department: 'Applicant',
          position: 'Business Owner',
          clearance_level: 'pending',
          application_type: 'new_business'
        }),
        created_at: new Date('2025-11-19T09:00:00.000Z'),
        updated_at: new Date('2025-11-19T09:00:00.000Z'),
      },
    ]);

    console.log('✅ Sample users inserted successfully');

    console.log('🎯 Sample user credentials for testing:');
    console.log('📧 <EMAIL> | 🔑 AdminPass123!');
    console.log('📧 <EMAIL> | 🔑 AdminPass123!');
    console.log('📧 <EMAIL> | 🔑 AdminPass123!');
    console.log('📧 <EMAIL> | 🔑 AdminPass123!');
  },

  async down(queryInterface, Sequelize) {
    console.log('🗑️ Removing sample users...');

    // Remove in reverse order due to foreign key constraints
    await queryInterface.bulkDelete('otp_verification', null, {});
    await queryInterface.bulkDelete('client_accounts', null, {});

    console.log('✅ Sample users removed successfully');
  },
};
