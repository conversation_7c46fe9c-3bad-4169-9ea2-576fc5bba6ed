'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('clients', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        comment: 'UUID v4 primary key for secure identifiers',
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        comment: 'Unique email address for client login',
      },
      password_hash: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'bcrypt-hashed password',
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Client organization name',
      },
      contact_number: {
        type: Sequelize.STRING(50),
        allowNull: false,
        comment: 'Required contact number for the client organization',
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Optional address for the client organization',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'JSONB field for flexible metadata storage',
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        comment: 'Account creation timestamp',
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        comment: 'Last modification timestamp',
      },
    });

    // Create indexes for clients table
    await queryInterface.addIndex('clients', ['email'], {
      name: 'idx_clients_email_search',
      using: 'btree',
    });
    await queryInterface.addIndex('clients', ['name'], {
      name: 'idx_clients_name',
      using: 'btree',
    });
    await queryInterface.addIndex('clients', ['metadata'], {
      name: 'idx_clients_metadata',
      using: 'gin', // GIN index for JSONB
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('clients');
  },
};
