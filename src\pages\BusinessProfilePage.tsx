// Business Profile Page - Business Profile Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { Building2, MapPin, Phone, FileText, Plus, Edit2, Loader2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';

import { businessProfileSchema, BusinessProfileFormData } from '@/schemas/businessSchemas';
import { businessService, BusinessProfile } from '@/services/businessService';
import { useToast } from '@/hooks/useToast';

const BusinessProfilePage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [profiles, setProfiles] = useState<BusinessProfile[]>([]);
  const [editingProfile, setEditingProfile] = useState<BusinessProfile | null>(null);
  const [showForm, setShowForm] = useState(false);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<BusinessProfileFormData>({
    resolver: yupResolver(businessProfileSchema),
    mode: 'onBlur',
  });

  useEffect(() => {
    fetchProfiles();
  }, []);

  const fetchProfiles = async () => {
    try {
      setIsFetching(true);
      const response = await businessService.getBusinessProfiles();
      setProfiles(response.data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load business profiles',
      });
    } finally {
      setIsFetching(false);
    }
  };

  const onSubmit = async (data: BusinessProfileFormData) => {
    setIsLoading(true);
    try {
      if (editingProfile) {
        await businessService.updateBusinessProfile(editingProfile.id, data);
        toast({
          variant: 'success',
          title: 'Success',
          description: 'Business profile updated successfully!',
        });
      } else {
        await businessService.createBusinessProfile(data);
        toast({
          variant: 'success',
          title: 'Success',
          description: 'Business profile created successfully!',
        });
      }
      
      reset();
      setShowForm(false);
      setEditingProfile(null);
      fetchProfiles();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to save business profile',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (profile: BusinessProfile) => {
    setEditingProfile(profile);
    reset({
      business_name: profile.business_name,
      business_type: profile.business_type || '',
      address: profile.address || '',
      contact_number: profile.contact_number || '',
      tin_number: profile.tin_number || '',
    });
    setShowForm(true);
  };

  const handleCancel = () => {
    setShowForm(false);
    setEditingProfile(null);
    reset();
  };

  if (isFetching) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center"
        >
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Business Profiles
            </h1>
            <p className="text-gray-600 mt-1">Manage your business information</p>
          </div>
          {!showForm && (
            <Button onClick={() => setShowForm(true)} className="gap-2">
              <Plus className="w-5 h-5" />
              Add Business
            </Button>
          )}
        </motion.div>

        {/* Form */}
        {showForm && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>
                  {editingProfile ? 'Edit Business Profile' : 'Create Business Profile'}
                </CardTitle>
                <CardDescription>
                  {editingProfile ? 'Update your business information' : 'Add a new business profile'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                  {/* Business Name */}
                  <div className="space-y-2">
                    <Label htmlFor="business_name">
                      Business Name <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Building2 className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Input
                        id="business_name"
                        {...register('business_name')}
                        error={errors.business_name?.message}
                        className="pl-10"
                        placeholder="Enter business name"
                      />
                    </div>
                    {errors.business_name && (
                      <p className="text-sm text-red-600">{errors.business_name.message}</p>
                    )}
                  </div>

                  {/* Business Type */}
                  <div className="space-y-2">
                    <Label htmlFor="business_type">Business Type</Label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Input
                        id="business_type"
                        {...register('business_type')}
                        error={errors.business_type?.message}
                        className="pl-10"
                        placeholder="e.g., Retail, Restaurant, Services"
                      />
                    </div>
                    {errors.business_type && (
                      <p className="text-sm text-red-600">{errors.business_type.message}</p>
                    )}
                  </div>

                  {/* Address */}
                  <div className="space-y-2">
                    <Label htmlFor="address">Business Address</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                      <Textarea
                        id="address"
                        {...register('address')}
                        className="pl-10 min-h-[80px]"
                        placeholder="Enter complete business address"
                      />
                    </div>
                  </div>

                  {/* Contact Number */}
                  <div className="space-y-2">
                    <Label htmlFor="contact_number">Contact Number</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Input
                        id="contact_number"
                        {...register('contact_number')}
                        error={errors.contact_number?.message}
                        className="pl-10"
                        placeholder="+63 XXX XXX XXXX"
                      />
                    </div>
                    {errors.contact_number && (
                      <p className="text-sm text-red-600">{errors.contact_number.message}</p>
                    )}
                  </div>

                  {/* TIN Number */}
                  <div className="space-y-2">
                    <Label htmlFor="tin_number">TIN Number</Label>
                    <Input
                      id="tin_number"
                      {...register('tin_number')}
                      error={errors.tin_number?.message}
                      placeholder="XXX-XXX-XXX-XXX"
                    />
                    {errors.tin_number && (
                      <p className="text-sm text-red-600">{errors.tin_number.message}</p>
                    )}
                  </div>

                  {/* Buttons */}
                  <div className="flex gap-3 pt-4">
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="flex-1"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        editingProfile ? 'Update Profile' : 'Create Profile'
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isLoading}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Business Profiles List */}
        <div className="grid gap-4">
          {profiles.length === 0 && !showForm ? (
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="py-12 text-center">
                <Building2 className="w-16 h-16 mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Business Profiles</h3>
                <p className="text-gray-600 mb-4">Get started by creating your first business profile</p>
                <Button onClick={() => setShowForm(true)} className="gap-2">
                  <Plus className="w-5 h-5" />
                  Add Business Profile
                </Button>
              </CardContent>
            </Card>
          ) : (
            profiles.map((profile, index) => (
              <motion.div
                key={profile.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm hover:shadow-xl transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">{profile.business_name}</h3>
                        {profile.business_type && (
                          <p className="text-sm text-gray-600 mb-3">{profile.business_type}</p>
                        )}
                        <div className="space-y-2 text-sm text-gray-700">
                          {profile.address && (
                            <div className="flex items-start gap-2">
                              <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                              <span>{profile.address}</span>
                            </div>
                          )}
                          {profile.contact_number && (
                            <div className="flex items-center gap-2">
                              <Phone className="w-4 h-4 text-gray-400" />
                              <span>{profile.contact_number}</span>
                            </div>
                          )}
                          {profile.tin_number && (
                            <div className="flex items-center gap-2">
                              <FileText className="w-4 h-4 text-gray-400" />
                              <span>TIN: {profile.tin_number}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(profile)}
                        className="gap-2"
                      >
                        <Edit2 className="w-4 h-4" />
                        Edit
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default BusinessProfilePage;

