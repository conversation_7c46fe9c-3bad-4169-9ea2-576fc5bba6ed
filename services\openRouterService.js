const axios = require('axios');
const { withRetry } = require('../utils/apiRetry');

class OpenRouterService {
  constructor(apiKey) {
    if (!apiKey) {
      throw new Error('OpenRouter API key is required');
    }
    
    this.client = axios.create({
      baseURL: 'https://openrouter.ai/api/v1',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 seconds timeout
    });
  }

  /**
   * Make a request to OpenRouter API with retry logic
   * @param {string} endpoint - API endpoint
   * @param {Object} data - Request data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - API response
   */
  async request(endpoint, data = {}, options = {}) {
    try {
      return await withRetry(
        async () => {
          const response = await this.client.post(endpoint, data, options);
          return response.data;
        },
        {
          maxRetries: 3,
          initialDelay: 1000,
          shouldRetry: (error) => {
            // Retry on network errors or 5xx server errors or rate limits
            return !error.response || 
                   error.response.status >= 500 || 
                   error.response.status === 429;
          }
        }
      );
    } catch (error) {
      console.error('OpenRouter API request failed:', error);
      throw new Error(`OpenRouter API error: ${error.message}`);
    }
  }

  /**
   * Generate text using OpenRouter
   * @param {Object} params - Generation parameters
   * @param {string} params.model - Model to use
   * @param {Array} params.messages - Array of message objects
   * @param {number} params.max_tokens - Maximum number of tokens to generate
   * @param {number} params.temperature - Sampling temperature
   * @returns {Promise<Object>} - Generated text
   */
  async generateText({ model, messages, max_tokens = 100, temperature = 0.7 }) {
    return this.request('/chat/completions', {
      model,
      messages,
      max_tokens,
      temperature,
    });
  }
}

// Create a singleton instance
let openRouterServiceInstance = null;

/**
 * Get or create the OpenRouter service instance
 * @param {string} apiKey - OpenRouter API key
 * @returns {OpenRouterService} - OpenRouter service instance
 */
const getOpenRouterService = (apiKey) => {
  if (!openRouterServiceInstance && apiKey) {
    openRouterServiceInstance = new OpenRouterService(apiKey);
  } else if (!openRouterServiceInstance) {
    throw new Error('OpenRouter API key is required for first initialization');
  }
  return openRouterServiceInstance;
};

module.exports = { getOpenRouterService };
