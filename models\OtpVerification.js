// Sequelize Model for OTP Verification
// BSCS Mini-Thesis Project - Holy Trinity College
// Created: November 19, 2025
// Purpose: Define OtpVerification model with expiration logic and security validations
// Security: Prevents OTP reuse, enforces expiration, validates format
// Standards: Sequelize v6+ conventions, ENUM validation, composite indexes

const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const OtpVerification = sequelize.define('OtpVerification', {
    // Primary key: UUID v4 for secure identifiers
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique OTP record identifier',
    },
    // Foreign key to client_accounts table
    client_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'client_accounts',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Reference to user account',
    },
    // OTP code with strict format validation
    otp_code: {
      type: DataTypes.STRING(6),
      allowNull: false,
      validate: {
        is: {
          args: /^\d{6}$/,
          msg: 'OTP must be exactly 6 digits',
        },
        notEmpty: {
          msg: 'OTP code is required',
        },
      },
      comment: '6-digit numeric code (000000-999999)',
    },
    // Expiration timestamp for security
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
      validate: {
        isAfter: {
          args: new Date().toISOString(),
          msg: 'Expiration date must be in the future',
        },
      },
      comment: 'Expiration timestamp (typically 5 minutes from creation)',
    },
    // Flag to prevent OTP reuse after verification
    is_used: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether OTP has been consumed',
    },
    // ENUM for OTP purpose (Registration or Password Reset)
    purpose: {
      type: DataTypes.ENUM('Registration', 'Password Reset'),
      allowNull: false,
      defaultValue: 'Registration',
      validate: {
        isIn: {
          args: [['Registration', 'Password Reset']],
          msg: 'Purpose must be either Registration or Password Reset',
        },
      },
      comment: 'OTP use case type',
    },
  }, {
    // Table configuration
    tableName: 'otp_verification',
    timestamps: true, // Enables createdAt and updatedAt fields
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Foreign key index for performance
      {
        fields: ['client_id'],
        name: 'idx_otp_verification_client_id',
      },
      // Composite index for fast OTP verification queries
      {
        fields: ['otp_code', 'is_used', 'expires_at'],
        name: 'idx_otp_verification_code_used_expires',
      },
      // Index for cleanup of expired OTPs
      {
        fields: ['expires_at'],
        name: 'idx_otp_verification_expires_at',
      },
    ],
  });

  // Instance method to check if OTP is valid (not expired and not used)
  OtpVerification.prototype.isValid = function() {
    const now = new Date();
    return !this.is_used && this.expires_at > now;
  };

  // Static method to generate a new OTP for a client
  OtpVerification.generateOTP = async function(clientId, purpose = 'Registration') {
    // Generate 6-digit random OTP
    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Set expiration to 5 minutes from now
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + 5);

    // Create OTP record
    return await this.create({
      client_id: clientId,
      otp_code: otpCode,
      expires_at: expiresAt,
      purpose: purpose,
      is_used: false,
    });
  };

  // Static method to verify OTP code for a client
  OtpVerification.verifyOTP = async function(clientId, otpCode, purpose = 'Registration') {
    // Find valid OTP for the client
    const otpRecord = await this.findOne({
      where: {
        client_id: clientId,
        otp_code: otpCode,
        purpose: purpose,
        is_used: false,
        expires_at: {
          [Op.gt]: new Date(), // Not expired
        },
      },
    });

    if (!otpRecord) {
      return null; // Invalid or expired OTP
    }

    // Mark OTP as used to prevent reuse
    await otpRecord.update({ is_used: true });

    return otpRecord;
  };

  // Static method to clean up expired OTPs (for maintenance)
  OtpVerification.cleanupExpired = async function() {
    const deletedCount = await this.destroy({
      where: {
        expires_at: {
          [Op.lt]: new Date(),
        },
      },
    });
    return deletedCount;
  };

  return OtpVerification;
};
