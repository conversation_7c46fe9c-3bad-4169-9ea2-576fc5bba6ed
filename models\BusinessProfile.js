// Sequelize Model for Business Profile - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define BusinessProfile model for storing business information
// Security: UUID primary keys, unique TIN validation, foreign key constraints
// Standards: Sequelize v6+ conventions with PostgreSQL data types

module.exports = (sequelize, DataTypes) => {
  const BusinessProfile = sequelize.define('BusinessProfile', {
    // Primary key: UUID v4 for secure identifiers
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique business profile identifier (UUID v4)',
    },
    // Foreign key to client_accounts table
    client_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'client_accounts',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Reference to user account',
    },
    // Business name with validation
    business_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: {
          args: [2, 255],
          msg: 'Business name must be between 2 and 255 characters',
        },
        notEmpty: {
          msg: 'Business name is required',
        },
      },
      comment: 'Registered business name',
    },
    // Business type/category
    business_type: {
      type: DataTypes.STRING(100),
      allowNull: true,
      validate: {
        len: {
          args: [0, 100],
          msg: 'Business type must not exceed 100 characters',
        },
      },
      comment: 'Type or category of business (e.g., Retail, Food Service, etc.)',
    },
    // Business address
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Complete business address',
    },
    // Contact number with validation
    contact_number: {
      type: DataTypes.STRING(50),
      allowNull: true,
      validate: {
        is: {
          args: /^[\+]?[1-9][\d]{0,15}$/,
          msg: 'Please enter a valid phone number',
        },
      },
      comment: 'Business contact number',
    },
    // Tax Identification Number (TIN) with uniqueness
    tin_number: {
      type: DataTypes.STRING(50),
      allowNull: true,
      unique: {
        msg: 'TIN number is already registered',
      },
      validate: {
        is: {
          args: /^[\d\-]+$/,
          msg: 'TIN must contain only numbers and hyphens',
        },
      },
      comment: 'Tax Identification Number (unique)',
    },
    // Additional metadata for flexible extensions
    metadata: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
      comment: 'JSONB field for flexible metadata (owner info, business hours, etc.)',
    },
  }, {
    // Table configuration
    tableName: 'business_profiles',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Foreign key index for performance
      {
        fields: ['client_id'],
        name: 'idx_business_profiles_client_id',
        using: 'btree',
      },
      // Business name search index
      {
        fields: ['business_name'],
        name: 'idx_business_profiles_business_name',
        using: 'btree',
      },
      // TIN lookup index
      {
        fields: ['tin_number'],
        name: 'idx_business_profiles_tin_number',
        using: 'btree',
      },
      // GIN index for JSONB metadata queries
      {
        fields: ['metadata'],
        name: 'idx_business_profiles_metadata',
        using: 'gin',
      },
    ],
  });

  // Instance method to update metadata
  BusinessProfile.prototype.updateMetadata = async function(key, value) {
    const metadata = { ...this.metadata };
    metadata[key] = value;
    return await this.update({ metadata });
  };

  // Static method to find business by TIN
  BusinessProfile.findByTIN = async function(tinNumber) {
    return await this.findOne({
      where: { tin_number: tinNumber },
    });
  };

  // Static method to find all businesses for a client
  BusinessProfile.findByClientId = async function(clientId) {
    return await this.findAll({
      where: { client_id: clientId },
      order: [['created_at', 'DESC']],
    });
  };

  return BusinessProfile;
};

