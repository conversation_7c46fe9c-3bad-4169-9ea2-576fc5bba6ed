storyboard:
  system_title: "Smart Governance: Business Permit Processing and Renewal System"
  location: "General Santos City - Business Permits and Licensing Office (BPLO)"
  
  # ========================================
  # USER PERSPECTIVE
  # ========================================
  user_scenes:
    
    # -------------------- REGISTRATION & LOGIN --------------------
    - id: USER-001
      scene_title: "User Registration"
      description: "New user creates an account to apply for business permits"
      visuals:
        - Registration form with green theme (#0B8457 primary, #3EB489 accent, #F2F7F5 background)
        - BPLO GenSan City logo and branding
        - Input fields for personal and business information
        - Terms and conditions checkbox
        - Register button (green)
      user_actions:
        - Navigate to registration page
        - Fill in personal details (name, email, phone, address)
        - Enter business information (business name, type, address)
        - Create username and password
        - Accept terms and conditions
        - Click "Register" button
      system_response:
        - Validate all required fields
        - Check email uniqueness in database
        - Hash password using bcrypt
        - Insert user record into users table
        - Set initial status as 'inactive' and verification_status as 'pending'
        - Send verification email to user
        - Display success message
        - Redirect to login page
        - Store user_id, created_at timestamp
      database_operations:
        - INSERT INTO users (name, email, phone, address, business_name, business_type, password_hash, status, verification_status, created_at)
      
    - id: USER-002
      scene_title: "Email Verification"
      description: "User verifies email address via verification link"
      visuals:
        - Email with verification link
        - Verification success page with green checkmark
        - Login prompt
      user_actions:
        - Open verification email
        - Click verification link
        - View confirmation message
      system_response:
        - Validate verification token
        - Update user verification_status to 'verified'
        - Update status to 'active'
        - Store verification timestamp
        - Display success message
        - Enable login access
      database_operations:
        - UPDATE users SET verification_status='verified', status='active', email_verified_at=NOW() WHERE user_id=?

    - id: USER-003
      scene_title: "User Login"
      description: "Registered user logs into the system"
      visuals:
        - Login page with green gradient background
        - BPLO logo
        - Username/email and password fields
        - "Forgot Password?" link
        - Sign In button (green)
        - "New user? Register here" link
      user_actions:
        - Enter username or email
        - Enter password
        - Click "Sign In" button
      system_response:
        - Validate credentials against database
        - Hash entered password and compare with stored hash
        - Check if account is active
        - Generate session token/JWT
        - Update last_login timestamp
        - Redirect to user dashboard
        - Display welcome message with user's name
      database_operations:
        - SELECT * FROM users WHERE (email=? OR username=?) AND status='active'
        - UPDATE users SET last_login=NOW() WHERE user_id=?
      authentication:
        - Create JWT token with user_id, role='user', expiry=24h
        - Store session in cookies/localStorage

    - id: USER-004
      scene_title: "User Dashboard Homepage"
      description: "User views their dashboard after login"
      visuals:
        - Welcome header with user name
        - Navigation menu (Dashboard, New Application, Renewals, Payments, Profile, Logout)
        - Overview cards showing application count, renewal count, pending payments
        - Quick action buttons (Apply for New Permit, Renew Permit)
        - Recent activity timeline
        - Notification bell with badge
        - BPLO contact information
      user_actions:
        - View dashboard statistics
        - Review recent activity
        - Check notifications
        - Navigate to different sections
      system_response:
        - Fetch user's applications from database
        - Count pending, approved, rejected applications
        - Fetch renewal records
        - Calculate payment status
        - Load recent activities (last 10 actions)
        - Display unread notification count
      database_operations:
        - SELECT COUNT(*) FROM applications WHERE user_id=? GROUP BY status
        - SELECT COUNT(*) FROM renewals WHERE user_id=? GROUP BY status
        - SELECT * FROM activities WHERE user_id=? ORDER BY created_at DESC LIMIT 10
        - SELECT COUNT(*) FROM notifications WHERE user_id=? AND is_read=false

    # -------------------- NEW APPLICATION PROCESS --------------------
    - id: USER-005
      scene_title: "Start New Business Permit Application"
      description: "User initiates a new business permit application"
      visuals:
        - "New Application" form header
        - Step indicator (Step 1 of 4: Business Information)
        - Form fields for business details
        - Business type dropdown
        - Address fields
        - Next button (green)
      user_actions:
        - Click "Apply for New Permit" button from dashboard
        - Fill in business name
        - Select business type from dropdown (Restaurant, IT Services, Retail, Logistics, etc.)
        - Enter business address
        - Enter contact information
        - Provide business description
        - Click "Next" to proceed
      system_response:
        - Display application form
        - Validate required fields
        - Store form data in session/temporary storage
        - Navigate to document upload step
      validation_rules:
        - Business name: required, min 3 characters, max 200 characters
        - Business type: required, must be from predefined list
        - Address: required, complete address format
        - Contact: required, valid phone format (+63)

    - id: USER-006
      scene_title: "Upload Required Documents"
      description: "User uploads necessary business permit documents"
      visuals:
        - "New Application" form header
        - Step indicator (Step 2 of 4: Document Upload)
        - File upload zones for each required document
        - List of required documents with checkboxes
        - Upload progress indicators
        - File preview thumbnails
        - Previous and Next buttons
      user_actions:
        - View list of required documents (Business Registration, Tax Clearance, Barangay Clearance, Health Permit, Fire Safety Certificate, etc.)
        - Click upload zone for each document
        - Select files from device (PDF, JPG, PNG formats)
        - Preview uploaded files
        - Remove/replace documents if needed
        - Click "Next" after all uploads complete
      system_response:
        - Validate file types (PDF, JPG, PNG only)
        - Check file sizes (max 5MB per file)
        - Generate unique filenames
        - Upload files to server storage/cloud
        - Create database records for each document
        - Store file paths and metadata
        - Validate all required documents uploaded
        - Navigate to review step
      database_operations:
        - INSERT INTO documents (application_id, document_type, file_path, file_name, file_size, uploaded_at)
      file_storage:
        - Store files in /uploads/applications/{application_id}/{document_type}/
        - Generate secure URLs for file access

    - id: USER-007
      scene_title: "Review Application Details"
      description: "User reviews all application information before submission"
      visuals:
        - "New Application" form header
        - Step indicator (Step 3 of 4: Review)
        - Summary cards showing business information
        - List of uploaded documents with view/download buttons
        - Fee calculation breakdown
        - Total amount display (₱ format)
        - Edit buttons for each section
        - Certification checkbox ("I certify that all information is true and correct")
        - Previous and Submit buttons
      user_actions:
        - Review all entered business information
        - View uploaded documents
        - Check fee calculation
        - Click Edit if corrections needed
        - Check certification checkbox
        - Click "Submit Application" button
      system_response:
        - Display comprehensive summary
        - Calculate application fees based on business type
        - Show itemized fee breakdown
        - Validate certification checkbox
        - Create application record in database
        - Generate unique application ID (format: BP-YYYY-NNNN)
        - Set initial status as 'pending'
        - Create activity log entry
        - Navigate to payment step
      database_operations:
        - INSERT INTO applications (user_id, business_name, business_type, address, contact, status, application_fee, created_at)
        - INSERT INTO activity_logs (user_id, action, description, created_at)
      business_logic:
        - Application fee calculation based on business_type lookup table
        - Application ID generation with year prefix and auto-increment

    - id: USER-008
      scene_title: "Payment Process"
      description: "User completes payment for business permit application"
      visuals:
        - "New Application" form header
        - Step indicator (Step 4 of 4: Payment)
        - Application summary with ID
        - Total amount due prominently displayed
        - Payment method selection (GCash, PayMaya, Bank Transfer, Over the Counter, Credit Card)
        - Payment instructions for selected method
        - Payment reference number field
        - Proof of payment upload zone
        - Submit Payment button
      user_actions:
        - Review application ID and amount
        - Select payment method from options
        - For digital payments (GCash/PayMaya):
          - View QR code or payment link
          - Complete payment in mobile app
          - Take screenshot of confirmation
        - Enter payment reference number
        - Upload proof of payment screenshot/receipt
        - Click "Submit Payment" button
      system_response:
        - Display payment options
        - Generate payment record with reference ID
        - For PayMongo integration:
          - Create payment intent via API
          - Redirect to payment gateway if needed
          - Listen for webhook confirmation
        - Store payment proof file
        - Update payment status to 'pending'
        - Send notification to admin for verification
        - Create notification for user confirming submission
        - Display success message with application tracking info
        - Redirect to dashboard with "Track Application" prompt
      database_operations:
        - INSERT INTO payments (application_id, amount, payment_method, reference_number, proof_file_path, status, created_at)
        - UPDATE applications SET payment_status='pending' WHERE application_id=?
        - INSERT INTO notifications (user_id, type, title, message, created_at)
      payment_integration:
        - PayMongo API: Create payment source
        - Store payment_intent_id for webhook matching
        - Handle payment success/failure callbacks
      notifications:
        - User: "Your application BP-YYYY-NNNN has been submitted. Payment verification pending."
        - Admin: "New payment submitted for application BP-YYYY-NNNN - requires verification"

    - id: USER-009
      scene_title: "Application Tracking"
      description: "User monitors the status of submitted applications"
      visuals:
        - "My Applications" page header
        - Filter dropdown (All, Pending, Approved, Rejected)
        - Search bar for application ID
        - Data table with columns: Application ID, Business Name, Date Submitted, Status, Payment Status, Actions
        - Status badges with color coding (Pending: yellow, Approved: green, Rejected: red)
        - View Details button for each application
        - Timeline showing application progress
      user_actions:
        - Navigate to "My Applications" section
        - Filter applications by status
        - Search for specific application ID
        - Click "View Details" for an application
        - View application timeline
      system_response:
        - Fetch all applications for logged-in user
        - Apply filters and search criteria
        - Display paginated results
        - Show color-coded status badges
        - Load detailed view on click
      database_operations:
        - SELECT * FROM applications WHERE user_id=? ORDER BY created_at DESC
        - SELECT * FROM application_status_history WHERE application_id=?
      ui_states:
        - Pending: Yellow badge, "Under Review" message
        - Approved: Green badge, "Download Permit" button enabled
        - Rejected: Red badge, "View Rejection Reason" button
        - Payment Pending: Orange badge, "Payment Verification in Progress"

    - id: USER-010
      scene_title: "View Application Details & History"
      description: "User views comprehensive details of a specific application"
      visuals:
        - Application details modal/page
        - Application ID prominently displayed
        - Business information summary
        - Document list with download buttons
        - Payment information and receipt
        - Status timeline with dates
        - Comments/notes from admin (if any)
        - Download permit button (if approved)
        - Print button
        - Back to applications button
      user_actions:
        - Review all application information
        - Download submitted documents
        - View payment receipt
        - Check status history timeline
        - Read admin comments if rejected
        - Download permit if approved
        - Print application details
      system_response:
        - Load complete application data
        - Fetch associated documents
        - Load payment records
        - Retrieve status change history with timestamps
        - Generate downloadable permit PDF (if approved)
        - Enable print functionality
      database_operations:
        - SELECT * FROM applications WHERE application_id=? AND user_id=?
        - SELECT * FROM documents WHERE application_id=?
        - SELECT * FROM payments WHERE application_id=?
        - SELECT * FROM application_status_history WHERE application_id=? ORDER BY changed_at

    - id: USER-011
      scene_title: "Receive Admin Notification - Application Approved"
      description: "User receives notification that application is approved"
      visuals:
        - Notification bell with red badge (unread count)
        - Notification dropdown/panel
        - Success notification with green icon
        - "Your application BP-YYYY-NNNN has been approved" message
        - View details button
        - Download permit button
      user_actions:
        - Click notification bell
        - View notification message
        - Click "View Details" or "Download Permit"
      system_response:
        - Display notification list
        - Mark notification as read when viewed
        - Navigate to application details
        - Generate and serve permit PDF
      database_operations:
        - UPDATE notifications SET is_read=true WHERE notification_id=?
      email_notification:
        - Send email to user: "Congratulations! Your business permit application has been approved"
        - Include permit download link and instructions

    - id: USER-012
      scene_title: "Download Approved Business Permit"
      description: "User downloads the approved business permit document"
      visuals:
        - Download button with permit icon
        - PDF generation progress indicator
        - Download complete confirmation
        - Permit preview option
      user_actions:
        - Click "Download Permit" button
        - Wait for PDF generation
        - Save permit file to device
        - Optionally preview before download
      system_response:
        - Generate official permit PDF with:
          - BPLO header and logo
          - Application ID and permit number
          - Business details
          - Validity dates
          - QR code for verification
          - Digital signature/seal
        - Stream PDF to user
        - Log download activity
      database_operations:
        - INSERT INTO activity_logs (user_id, action, description) VALUES (?, 'download_permit', 'Downloaded permit for BP-YYYY-NNNN')
      pdf_generation:
        - Use PDF library to create official permit
        - Include security features (watermark, QR code)
        - Store PDF in permits directory

    - id: USER-013
      scene_title: "Receive Admin Notification - Application Rejected"
      description: "User receives notification that application is rejected with reasons"
      visuals:
        - Notification with red/warning icon
        - "Your application BP-YYYY-NNNN has been rejected" message
        - View reason button
        - Reapply button
      user_actions:
        - Click notification bell
        - Read rejection notification
        - Click "View Reason"
        - Read detailed rejection reasons
        - Optionally reapply
      system_response:
        - Display notification
        - Show rejection modal with admin comments
        - Highlight which documents/information were insufficient
        - Enable reapplication flow
      database_operations:
        - SELECT rejection_reason, admin_comments FROM applications WHERE application_id=?
      email_notification:
        - Send email: "Application Update Required"
        - Include specific reasons for rejection
        - Provide reapplication instructions

    # -------------------- RENEWAL PROCESS --------------------
    - id: USER-014
      scene_title: "View Permits Eligible for Renewal"
      description: "User views business permits that can be renewed"
      visuals:
        - "My Permits" or "Renewals" page
        - List of active permits with expiry dates
        - Expiry date prominently displayed with color coding
        - "Renew" button for permits within renewal window
        - Expired permits marked with red indicator
        - Days until expiry countdown
      user_actions:
        - Navigate to Renewals section
        - View list of active permits
        - Check expiry dates
        - Click "Renew" button for eligible permit
      system_response:
        - Fetch user's approved applications
        - Calculate days until expiry for each
        - Color code based on urgency:
          - Green: >60 days
          - Yellow: 30-60 days
          - Orange: 7-30 days
          - Red: <7 days or expired
        - Enable renewal for permits within 90 days of expiry
      database_operations:
        - SELECT * FROM applications WHERE user_id=? AND status='approved' AND permit_issued=true
      business_logic:
        - Renewal window: 90 days before expiry
        - Calculate: DATEDIFF(expiry_date, CURRENT_DATE)

    - id: USER-015
      scene_title: "Start Permit Renewal Application"
      description: "User initiates renewal process for existing permit"
      visuals:
        - Renewal application form header
        - Original permit information pre-filled
        - Original permit ID reference
        - Updated information form
        - Document update section
        - Renewal fee display
        - Declaration checkbox
        - Submit button
      user_actions:
        - Verify pre-filled business information
        - Update any changed information
        - Upload updated required documents (Latest Tax Clearance, Health Permit Renewal, etc.)
        - Review renewal fee
        - Check declaration checkbox
        - Click "Submit Renewal Application"
      system_response:
        - Load original permit data
        - Pre-fill form with existing information
        - Allow editing of updatable fields
        - Validate document uploads
        - Calculate renewal fee (typically lower than new application)
        - Generate renewal ID (format: RN-YYYY-NNNN)
        - Create renewal record
        - Link to original permit
        - Navigate to payment step
      database_operations:
        - INSERT INTO renewals (user_id, original_permit_id, business_name, renewal_type, renewal_fee, status, created_at)
        - INSERT INTO documents (renewal_id, document_type, file_path)
      business_logic:
        - Renewal fee = 80% of original application fee
        - Renewal ID generation with RN prefix

    - id: USER-016
      scene_title: "Pay Renewal Fee"
      description: "User completes payment for permit renewal"
      visuals:
        - Similar to application payment (USER-008)
        - Renewal ID and original permit ID displayed
        - Renewal fee amount
        - Payment method selection
        - Payment proof upload
        - Submit payment button
      user_actions:
        - Select payment method
        - Complete payment transaction
        - Upload payment proof
        - Enter reference number
        - Submit payment
      system_response:
        - Process payment similar to new application
        - Create payment record linked to renewal_id
        - Update renewal payment_status to 'pending'
        - Notify admin for verification
        - Notify user of submission
      database_operations:
        - INSERT INTO payments (renewal_id, amount, payment_method, reference_number, status, created_at)
        - UPDATE renewals SET payment_status='pending' WHERE renewal_id=?
      notifications:
        - User: "Renewal application RN-YYYY-NNNN submitted successfully"
        - Admin: "New renewal payment requires verification - RN-YYYY-NNNN"

    - id: USER-017
      scene_title: "Track Renewal Status"
      description: "User monitors renewal application progress"
      visuals:
        - "My Renewals" section
        - Data table with renewal applications
        - Status indicators (Pending, Approved, Rejected)
        - Payment status
        - Original permit reference
        - View details button
      user_actions:
        - Navigate to Renewals section
        - Filter by status
        - View renewal details
        - Check progress timeline
      system_response:
        - Fetch user's renewal applications
        - Display with status badges
        - Show renewal timeline
      database_operations:
        - SELECT * FROM renewals WHERE user_id=? ORDER BY created_at DESC

    - id: USER-018
      scene_title: "Download Renewed Permit"
      description: "User downloads renewed business permit after approval"
      visuals:
        - Renewal approved notification
        - Download renewed permit button
        - Updated permit with new validity dates
      user_actions:
        - Receive approval notification
        - Click download button
        - Save renewed permit
      system_response:
        - Generate new permit PDF with updated validity dates
        - Increment permit version
        - Update expiry date (+1 year from current expiry)
        - Log download activity
      database_operations:
        - UPDATE applications SET expiry_date=DATE_ADD(expiry_date, INTERVAL 1 YEAR) WHERE application_id=?
        - INSERT INTO permit_history (permit_id, renewal_id, issued_at, expiry_at)

    # -------------------- USER PROFILE & SETTINGS --------------------
    - id: USER-019
      scene_title: "View and Edit User Profile"
      description: "User manages their account profile information"
      visuals:
        - Profile page with user avatar/initials
        - Personal information section (name, email, phone, address)
        - Business information section
        - Edit button for each section
        - Change password button
        - Account activity log
        - Logout button
      user_actions:
        - Navigate to Profile section
        - View current information
        - Click "Edit Profile"
        - Update personal details
        - Update contact information
        - Click "Save Changes"
      system_response:
        - Display current user data
        - Enable edit mode
        - Validate updated information
        - Update database records
        - Send confirmation email if email changed
        - Display success message
      database_operations:
        - UPDATE users SET name=?, phone=?, address=? WHERE user_id=?
      validation_rules:
        - Email change requires re-verification
        - Phone number format validation
        - Address required field

    - id: USER-020
      scene_title: "Change Password"
      description: "User changes their account password"
      visuals:
        - Change password modal
        - Current password field
        - New password field
        - Confirm new password field
        - Password strength indicator
        - Save button
      user_actions:
        - Click "Change Password" button
        - Enter current password
        - Enter new password (min 8 characters)
        - Confirm new password
        - Click "Save" button
      system_response:
        - Validate current password
        - Check new password strength
        - Verify password confirmation match
        - Hash new password
        - Update password in database
        - Log password change activity
        - Send security notification email
      database_operations:
        - UPDATE users SET password_hash=?, password_updated_at=NOW() WHERE user_id=?
        - INSERT INTO activity_logs (user_id, action, ip_address, created_at)
      security:
        - Hash password using bcrypt with salt
        - Invalidate all existing sessions
        - Require re-login
        - Email notification of password change

    - id: USER-021
      scene_title: "View Notifications"
      description: "User views all system notifications"
      visuals:
        - Notifications page
        - Notification bell icon with badge
        - List of notifications (unread highlighted)
        - Notification types: success, info, warning, error
        - Mark as read button
        - Delete button
        - Filter by type/date
      user_actions:
        - Click notification bell
        - View notification list
        - Click individual notification to view details
        - Mark notifications as read
        - Delete old notifications
      system_response:
        - Fetch user notifications
        - Sort by date (newest first)
        - Highlight unread notifications
        - Update read status on click
        - Navigate to relevant page if clicked
      database_operations:
        - SELECT * FROM notifications WHERE user_id=? ORDER BY created_at DESC
        - UPDATE notifications SET is_read=true WHERE notification_id=?
      notification_types:
        - Application status updates
        - Payment confirmations
        - Renewal reminders
        - System announcements
        - Document requests

    - id: USER-022
      scene_title: "Forgot Password / Password Reset"
      description: "User resets forgotten password via email"
      visuals:
        - Forgot password link on login page
        - Email input form
        - Verification code input (or reset link)
        - New password form
        - Success confirmation
      user_actions:
        - Click "Forgot Password?" on login page
        - Enter registered email
        - Check email for reset link/code
        - Click reset link or enter code
        - Enter new password
        - Confirm new password
        - Submit
      system_response:
        - Validate email exists in database
        - Generate secure reset token (UUID)
        - Store token with expiry (30 minutes)
        - Send password reset email with link
        - Verify token when clicked
        - Allow password reset if token valid
        - Update password in database
        - Invalidate reset token
        - Send confirmation email
      database_operations:
        - INSERT INTO password_resets (user_id, token, expires_at) VALUES (?, UUID(), DATE_ADD(NOW(), INTERVAL 30 MINUTE))
        - UPDATE users SET password_hash=? WHERE user_id=?
        - DELETE FROM password_resets WHERE token=?
      email:
        - Subject: "Password Reset Request - BPLO GenSan"
        - Body: Reset link with token, valid for 30 minutes
        - Confirmation email after successful reset

    - id: USER-023
      scene_title: "User Logout"
      description: "User logs out from the system"
      visuals:
        - Logout confirmation modal (optional)
        - "Are you sure you want to logout?" message
        - Confirm and Cancel buttons
      user_actions:
        - Click "Logout" button from menu
        - Confirm logout action
      system_response:
        - Destroy user session
        - Clear authentication tokens
        - Clear local storage/cookies
        - Redirect to login page
        - Display "Successfully logged out" message
      session_management:
        - Delete session from sessions table
        - Clear JWT token from cookies
        - Invalidate remember-me tokens if any
      database_operations:
        - DELETE FROM sessions WHERE user_id=? AND session_token=?


  # ========================================
  # ADMIN PERSPECTIVE
  # ========================================
  admin_scenes:
    
    # -------------------- ADMIN LOGIN & DASHBOARD --------------------
    - id: ADMIN-001
      scene_title: "Admin Login"
      description: "Admin authenticates to access the BPLO admin portal"
      visuals:
        - Login page with green gradient background (linear-gradient(135deg, #0B8457 0%, #3EB489 100%))
        - White card with shadow
        - GenSan City logo in green circle (#0B8457 background)
        - "Admin Portal" title
        - "Smart Governance: Business Permit Processing System" subtitle
        - "General Santos City - BPLO" text
        - Username/Email input field
        - Password input field
        - "Sign In" button (green #0B8457)
        - "Secure access for authorized personnel only" footer text
      user_actions:
        - Open admin portal URL
        - Enter admin username or email
        - Enter admin password
        - Click "Sign In" button
      system_response:
        - Validate credentials against admin users table
        - Verify admin role and permissions
        - Check account status (active/suspended)
        - Hash password and compare with database
        - Generate admin session token
        - Update last_login timestamp
        - Redirect to admin dashboard
        - Initialize sidebar navigation
      database_operations:
        - SELECT * FROM admin_users WHERE (username=? OR email=?) AND role='admin' AND status='active'
        - UPDATE admin_users SET last_login=NOW() WHERE admin_id=?
      authentication:
        - Create JWT with admin_id, role='admin', permissions array
        - Store in secure httpOnly cookie
        - Session timeout: 8 hours
      ui_components:
        - App.tsx: State management for login status, page routing
        - LoginPage.tsx: Form component with validation
        - Credentials state with username and password fields

    - id: ADMIN-002
      scene_title: "Admin Dashboard - Homepage"
      description: "Admin views the main dashboard with system overview and analytics"
      visuals:
        - Green sidebar (#0B8457) with navigation menu
        - BPLO logo and "GenSan BPLO Admin Portal" header in sidebar
        - Main content area with #F2F7F5 background
        - Green header banner with "Homepage" title
        - "General Santos City - Business Permit Processing Overview" subtitle
        - BPLO official banner image (Business Permits and Licensing Office, City of General Santos)
        - Monthly Applications & Renewals bar chart (green bars #0B8457 and #3EB489)
        - Renewal Status Distribution pie chart
        - Recent Activity feed with color-coded activity items
        - Color coding:
          - New application: blue dot
          - New renewal: purple dot
          - Approved: green dot
          - Renewal approved: teal dot
          - Payment verified: emerald dot
          - Renewal payment: cyan dot
          - Requires review: yellow dot
          - Renewal review: orange dot
      user_actions:
        - View dashboard after successful login
        - Review overall statistics
        - Examine monthly trends in charts
        - Scroll through recent activity
        - Click activity items to navigate to details
        - Use sidebar to navigate to different sections
      system_response:
        - Fetch aggregated statistics from database
        - Load monthly data for charts (applications, renewals, payments)
        - Generate renewal status distribution
        - Retrieve last 8 recent activities with timestamps
        - Render responsive charts using Recharts library
        - Display real-time data
      database_operations:
        - SELECT COUNT(*) FROM applications WHERE MONTH(created_at) = CURRENT_MONTH GROUP BY status
        - SELECT COUNT(*) FROM renewals GROUP BY status
        - SELECT month, COUNT(*) as applications FROM applications GROUP BY MONTH(created_at)
        - SELECT * FROM activity_logs ORDER BY created_at DESC LIMIT 8
      chart_data:
        - Monthly data: Jan through Jun showing applications, renewals, payment totals
        - Renewal distribution: Pending (23), Approved (67), Rejected (5)
      ui_components:
        - Dashboard.tsx: Main dashboard component
        - Recharts: BarChart, PieChart, ResponsiveContainer
        - Card components for overview sections
        - Activity timeline with timestamps

    # -------------------- MANAGE APPLICATIONS --------------------
    - id: ADMIN-003
      scene_title: "View All Applications"
      description: "Admin views list of all business permit applications"
      visuals:
        - Green header banner: "Manage Applications"
        - "Review, approve, or reject business permit applications" subtitle
        - Four overview cards with border-left accent colors:
          - Pending Applications: 45 (orange #FFB703 left border)
          - Approved: 128 (green #0B8457 left border)
          - Rejected: 12 (red #E63946 left border)
          - Archived: 256 (gray #6B7280 left border)
        - Search bar with magnifying glass icon
        - Filter dropdown for status (All, Pending, Approved, Rejected)
        - Applications data table with green header (#0B8457 background, white text)
        - Table columns: Application ID, Business Name, Applicant, Date Submitted, Status, Actions
        - Status badges with color coding
        - Action buttons: Eye (view), Check (approve), X (reject) - icon buttons
      user_actions:
        - Navigate to "Manage Applications" from sidebar
        - View application statistics in overview cards
        - Search for applications by business name, applicant, or ID
        - Filter applications by status
        - Scroll through paginated application list
        - Click Eye icon to view application details
        - Click Check icon to approve (for pending applications)
        - Click X icon to reject (for pending applications)
      system_response:
        - Fetch all applications from database
        - Calculate counts for each status
        - Apply search filter (case-insensitive LIKE query)
        - Apply status filter
        - Paginate results (show all matching)
        - Display colored status badges
        - Enable action buttons based on status (only pending applications show approve/reject)
      database_operations:
        - SELECT COUNT(*) FROM applications WHERE status='pending'
        - SELECT COUNT(*) FROM applications WHERE status='approved'
        - SELECT COUNT(*) FROM applications WHERE status='rejected'
        - SELECT * FROM applications WHERE (business_name LIKE ? OR applicant LIKE ? OR application_id LIKE ?) AND (status=? OR 'all'=?) ORDER BY date_submitted DESC
      ui_components:
        - ManageApplications.tsx: Main component with state management
        - Search and filter state hooks
        - Table component with responsive layout
        - Badge components for status display
        - Button components with green theme

    - id: ADMIN-004
      scene_title: "View Application Details"
      description: "Admin views comprehensive details of a specific application"
      visuals:
        - Modal dialog with "Application Details - {applicationId}" title
        - Two-column grid layout for information
        - Left column:
          - Building icon with business name
          - User icon with applicant name
          - Calendar icon with date submitted
        - Right column:
          - Business type
          - Address
          - Contact information
        - Submitted Documents section with document icons and names
        - Action buttons at bottom:
          - Reject button (red outline)
          - Approve button (green solid #0B8457) - only for pending applications
      user_actions:
        - Click "Eye" icon on an application row
        - Review all business information
        - Examine submitted documents list
        - Click document names to view/download
        - Click "Approve" button if application is valid
        - Click "Reject" button if application is invalid
        - Close modal to return to list
      system_response:
        - Fetch application details by application_id
        - Load associated documents
        - Display all information in organized sections
        - Show icons for visual clarity
        - Enable/disable action buttons based on current status
        - On approve: Update status to 'approved', create notification
        - On reject: Update status to 'rejected', create notification
        - Close modal and refresh application list
      database_operations:
        - SELECT * FROM applications WHERE application_id=?
        - SELECT * FROM documents WHERE application_id=?
        - UPDATE applications SET status='approved', approved_by=?, approved_at=NOW() WHERE application_id=?
        - UPDATE applications SET status='rejected', rejected_by=?, rejected_at=NOW() WHERE application_id=?
        - INSERT INTO notifications (user_id, type, title, message, created_at)
      ui_components:
        - Dialog component for modal display
        - Grid layout for information sections
        - Lucide icons (Building, User, Calendar, FileText)
        - Action buttons with color coding

    - id: ADMIN-005
      scene_title: "Approve Application"
      description: "Admin approves a pending business permit application"
      visuals:
        - Application details modal
        - Green "Approve" button highlighted
        - Confirmation of action
        - Success message display
        - Updated status badge changing to green "Approved"
      user_actions:
        - Review application thoroughly
        - Verify all documents are present and valid
        - Click "Approve" button in details modal OR
        - Click green Check icon in applications table
        - Confirm approval action
      system_response:
        - Validate application completeness
        - Update application status to 'approved'
        - Record admin_id who approved
        - Record approval timestamp
        - Generate permit number
        - Create notification for user
        - Send email to applicant
        - Create activity log entry
        - Update UI with success toast message
        - Refresh applications list
        - Close details modal
      database_operations:
        - UPDATE applications SET status='approved', approved_by=?, approved_at=NOW(), permit_number=? WHERE application_id=?
        - INSERT INTO notifications (user_id, type, title, message) VALUES (?, 'success', 'Application Approved', 'Your application {application_id} has been approved')
        - INSERT INTO activity_logs (admin_id, action, target_id, description, created_at)
      business_logic:
        - Generate permit number: PER-{year}-{sequential_number}
        - Set permit_issued flag to true
        - Calculate expiry date: approval_date + 1 year
      notifications:
        - User notification: "Application BP-2025-XXXX Approved"
        - Email: "Congratulations! Your business permit application has been approved"
        - Admin activity log: "Approved application BP-2025-XXXX"
      ui_components:
        - Toast notification (sonner library)
        - Status badge update animation
        - Button state management

    - id: ADMIN-006
      scene_title: "Reject Application"
      description: "Admin rejects a business permit application with reason"
      visuals:
        - Application details modal
        - Red "Reject" button
        - Rejection reason textarea (optional enhancement)
        - Confirmation dialog
        - Updated status badge changing to red "Rejected"
      user_actions:
        - Review application and identify issues
        - Click "Reject" button in details modal OR
        - Click red X icon in applications table
        - Optionally enter rejection reason
        - Confirm rejection action
      system_response:
        - Update application status to 'rejected'
        - Record admin_id who rejected
        - Record rejection timestamp
        - Store rejection reason if provided
        - Create notification for user
        - Send email to applicant with rejection reason
        - Create activity log entry
        - Display success toast message
        - Refresh applications list
        - Close details modal
      database_operations:
        - UPDATE applications SET status='rejected', rejected_by=?, rejected_at=NOW(), rejection_reason=? WHERE application_id=?
        - INSERT INTO notifications (user_id, type, title, message) VALUES (?, 'error', 'Application Rejected', 'Your application {application_id} has been rejected')
        - INSERT INTO activity_logs (admin_id, action, target_id, description)
      notifications:
        - User notification: "Application BP-2025-XXXX Rejected - Please review requirements"
        - Email: "Application Update Required" with rejection reasons
        - Admin activity log: "Rejected application BP-2025-XXXX"
      ui_components:
        - Toast notification with error styling
        - Status badge update
        - Rejection reason textarea (future enhancement)

    # -------------------- MANAGE RENEWALS --------------------
    - id: ADMIN-007
      scene_title: "View All Renewal Applications"
      description: "Admin views and manages business permit renewal requests"
      visuals:
        - Green header banner: "Manage Renewals"
        - "Review, approve, or reject business permit renewal applications" subtitle
        - Three overview cards with border-left accent:
          - Pending Renewals: 23 (orange #FFB703)
          - Approved Renewals: 67 (teal #3EB489)
          - Rejected Renewals: 5 (red #E63946)
        - Four summary cards showing counts:
          - Pending (with Clock icon, orange)
          - Approved (with Check icon, teal)
          - Rejected (with X icon, red)
          - Expiring Soon (with Calendar icon, orange)
        - Search bar and status filter
        - Renewals table with green header
        - Table columns: Renewal ID, Business Name, Applicant, Renewal Type, Date Submitted, Status, Expiry, Actions
        - Status badges for renewal status AND payment status (stacked)
        - Expiry date with urgency color coding (red if <=7 days, yellow if <=30 days)
      user_actions:
        - Navigate to "Manage Renewals" from sidebar
        - View renewal statistics
        - Search for renewals by business name, applicant, or renewal ID
        - Filter by status (All, Pending, Approved, Rejected)
        - Review expiry dates and urgency
        - Click Eye icon to view renewal details
        - Click Check icon to approve renewal
        - Click X icon to reject renewal
      system_response:
        - Fetch all renewal applications
        - Calculate counts by status
        - Count permits expiring within 30 days
        - Apply search and filter criteria
        - Calculate days until expiry for each renewal
        - Color code expiry dates by urgency
        - Display dual status badges (renewal status + payment status)
      database_operations:
        - SELECT COUNT(*) FROM renewals WHERE status='pending'
        - SELECT COUNT(*) FROM renewals WHERE status='approved'
        - SELECT COUNT(*) FROM renewals WHERE status='rejected'
        - SELECT COUNT(*) FROM renewals WHERE DATEDIFF(expiry_date, CURRENT_DATE) <= 30
        - SELECT r.*, a.expiry_date FROM renewals r JOIN applications a ON r.original_permit_id = a.application_id
      business_logic:
        - Days until expiry calculation: DATEDIFF(expiry_date, CURRENT_DATE)
        - Color coding:
          - >30 days: gray text
          - 8-30 days: yellow text
          - <=7 days: red text
          - Expired: red text "Expired"
      ui_components:
        - ManageRenewals.tsx component
        - Dual Badge display (status + payment)
        - Expiry date with color coding
        - RefreshCw icon for renewal theme

    - id: ADMIN-008
      scene_title: "View Renewal Application Details"
      description: "Admin reviews detailed renewal application information"
      visuals:
        - Modal dialog: "Renewal Details - {renewalId}"
        - RefreshCw icon in title
        - Two-column grid:
          - Left: Business name, Applicant, Original Permit ID, Expiry Date
          - Right: Renewal Type, Address, Contact, Renewal Fee (₱ formatted)
        - Submitted documents section (renewal-specific documents)
        - Action buttons:
          - "Reject Renewal" (red outline)
          - "Approve Renewal" (green #0B8457) - only for pending
      user_actions:
        - Click Eye icon on renewal row
        - Review renewal information
        - Verify original permit ID
        - Check expiry date urgency
        - Review updated documents
        - Click "Approve Renewal" if documents valid
        - Click "Reject Renewal" if incomplete
      system_response:
        - Fetch renewal details by renewal_id
        - Load original permit information
        - Display renewal-specific documents
        - Show renewal fee calculation
        - Enable actions based on status
        - On approve: Update renewal status, extend permit validity
        - On reject: Update renewal status, notify user
      database_operations:
        - SELECT * FROM renewals WHERE renewal_id=?
        - SELECT * FROM applications WHERE application_id = (SELECT original_permit_id FROM renewals WHERE renewal_id=?)
        - SELECT * FROM documents WHERE renewal_id=?
      ui_components:
        - Dialog with renewal-specific layout
        - Font-mono for permit IDs
        - Currency formatting for renewal fee
        - Document list with icons

    - id: ADMIN-009
      scene_title: "Approve Renewal Application"
      description: "Admin approves a renewal and extends permit validity"
      visuals:
        - Renewal details modal
        - Green "Approve Renewal" button
        - Success confirmation
        - Updated status badge to teal "Approved"
      user_actions:
        - Verify renewal documents are current
        - Confirm payment status is 'paid'
        - Click "Approve Renewal" button
        - Confirm action
      system_response:
        - Update renewal status to 'approved'
        - Record admin approval details
        - Extend original permit expiry date (+1 year)
        - Generate new permit version
        - Create notification for user
        - Send approval email
        - Log activity
        - Display success message
      database_operations:
        - UPDATE renewals SET status='approved', approved_by=?, approved_at=NOW() WHERE renewal_id=?
        - UPDATE applications SET expiry_date=DATE_ADD(expiry_date, INTERVAL 1 YEAR), permit_version=permit_version+1 WHERE application_id=?
        - INSERT INTO permit_history (permit_id, renewal_id, old_expiry, new_expiry, renewed_at)
        - INSERT INTO notifications (user_id, type, title, message)
      business_logic:
        - Expiry extension: current_expiry + 1 year
        - Permit version increment
        - Renewal fee typically 80% of original fee
      notifications:
        - User: "Renewal RN-2024-XXXX approved - Permit extended"
        - Email: "Your business permit has been renewed"
        - Admin log: "Approved renewal RN-2024-XXXX"

    - id: ADMIN-010
      scene_title: "Reject Renewal Application"
      description: "Admin rejects a renewal due to incomplete/invalid documents"
      visuals:
        - Renewal details modal
        - Red "Reject Renewal" button
        - Rejection confirmation
        - Status badge updates to red "Rejected"
      user_actions:
        - Identify issues with renewal documents
        - Click "Reject Renewal"
        - Optionally provide rejection reason
        - Confirm rejection
      system_response:
        - Update renewal status to 'rejected'
        - Record rejection details
        - Create notification for user
        - Send rejection email with reasons
        - Log activity
        - Display success message
      database_operations:
        - UPDATE renewals SET status='rejected', rejected_by=?, rejected_at=NOW(), rejection_reason=? WHERE renewal_id=?
        - INSERT INTO notifications (user_id, type, title, message)
      notifications:
        - User: "Renewal RN-2024-XXXX requires attention"
        - Email: Rejection reasons and resubmission instructions

    # -------------------- PAYMENT OVERSIGHT --------------------
    - id: ADMIN-011
      scene_title: "View Payment Overview"
      description: "Admin monitors all payment transactions for applications and renewals"
      visuals:
        - "Payment Oversight" page header
        - "Monitor and verify business permit and renewal payment transactions" subtitle
        - Four summary cards with colored left borders:
          - Total Collected: ₱XX,XXX (green #0B8457, DollarSign icon)
          - Pending Payments: ₱X,XXX (yellow, Clock icon)
          - Application Payments: XX count (teal #3EB489, Receipt icon)
          - Renewal Payments: XX count (green #0B8457, RefreshCw icon)
        - Tabs: "Application Payments" and "Renewal Payments"
        - Search bar and status filter (All, Paid, Pending, Failed)
        - Payment records table
        - Columns: Payment ID, Applicant, Business Name, Amount, Payment Method, Status, Payment Date, Actions
        - Status badges with icons (CheckCircle for paid, Clock for pending, XCircle for failed)
        - "Verify Payment" button for pending payments
      user_actions:
        - Navigate to "Payment Oversight" from sidebar
        - View total payment statistics
        - Switch between Application and Renewal payment tabs
        - Search for payments by applicant, business, or payment ID
        - Filter by payment status
        - Click "Verify Payment" for pending transactions
        - Review payment method and reference numbers
      system_response:
        - Fetch all payment records from database
        - Calculate total collected (sum of paid payments)
        - Calculate total pending (sum of pending payments)
        - Count application vs renewal payments
        - Separate payments into tabs
        - Display payment method and reference numbers
        - Show payment proof file links
      database_operations:
        - SELECT SUM(amount) FROM payments WHERE status='paid'
        - SELECT SUM(amount) FROM payments WHERE status='pending'
        - SELECT COUNT(*) FROM payments WHERE type='application'
        - SELECT COUNT(*) FROM payments WHERE type='renewal'
        - SELECT p.*, COALESCE(a.business_name, r.business_name) as business_name FROM payments p LEFT JOIN applications a ON p.application_id=a.application_id LEFT JOIN renewals r ON p.renewal_id=r.renewal_id
      ui_components:
        - PaymentOversight.tsx component
        - Tabs component for payment type separation
        - Table with payment details
        - Toast notifications for actions

    - id: ADMIN-012
      scene_title: "Verify Payment"
      description: "Admin verifies and confirms a pending payment transaction"
      visuals:
        - Payment record row highlighted
        - "Verify Payment" button with CheckCircle icon
        - Payment details: reference number, payment method, proof image/file
        - Verification confirmation
        - Status badge updates from yellow "Pending" to green "Paid"
        - Payment date populated with current date
      user_actions:
        - Locate pending payment in table
        - Review payment reference number
        - Optionally view payment proof file
        - For PayMongo/GCash payments: verify transaction in payment gateway
        - For bank transfer: verify bank deposit
        - For OTC: verify official receipt
        - Click "Verify Payment" button
        - Confirm verification
      system_response:
        - Update payment status to 'paid'
        - Set payment_date to current timestamp
        - Update related application/renewal payment_status
        - Trigger application/renewal processing if all requirements met
        - Create notification for user
        - Send payment confirmation email
        - Log admin action
        - Display success toast
        - Refresh payment list
      database_operations:
        - UPDATE payments SET status='paid', payment_date=NOW(), verified_by=? WHERE payment_id=?
        - UPDATE applications SET payment_status='paid' WHERE application_id=?
        - UPDATE renewals SET payment_status='paid' WHERE renewal_id=?
        - INSERT INTO notifications (user_id, type, title, message) VALUES (?, 'success', 'Payment Confirmed', 'Your payment has been verified')
        - INSERT INTO activity_logs (admin_id, action, target_id)
      payment_integration:
        - For PayMongo: Verify via webhook or API check
        - Match reference_number with payment gateway transaction ID
        - Validate amount matches
      notifications:
        - User: "Payment verified for {application_id/renewal_id}"
        - Email: "Payment Confirmation - ₱{amount} received"
        - Admin log: "Verified payment {payment_id}"
      ui_components:
        - Toast notification (sonner)
        - Status badge animation
        - Table row update

    - id: ADMIN-013
      scene_title: "Handle Failed Payment"
      description: "Admin attempts to retry verification for failed payments"
      visuals:
        - Payment record with red "Failed" status badge
        - "Retry Verification" button
        - Payment details review
        - Status update interface
      user_actions:
        - Identify failed payment
        - Review failure reason
        - Contact user if necessary to resolve issue
        - Click "Retry Verification"
        - Update status after resolution
      system_response:
        - Allow retry verification attempt
        - Update status based on new verification
        - Notify user of status change
        - Log retry attempt
      database_operations:
        - UPDATE payments SET status='paid' WHERE payment_id=? (if retry successful)
        - INSERT INTO payment_retry_logs (payment_id, attempted_by, result, attempted_at)

    # -------------------- NOTIFICATIONS CENTER --------------------
    - id: ADMIN-014
      scene_title: "View Notifications Center"
      description: "Admin manages system notifications and sends alerts to users"
      visuals:
        - "Notifications Center" page with Bell icon
        - Unread notification count badge (red with count)
        - "Send Notification" button (green #0B8457)
        - Send notification form (collapsible card with green border):
          - Recipient field
          - Title field
          - Message textarea
          - Send button
        - Notification list cards with:
          - Color-coded icons (CheckCircle green, X red, AlertTriangle yellow, Info green)
          - Title and message
          - Recipient name
          - Timestamp
          - Type badge (success, error, warning, info)
          - "NEW" badge for unread (green)
          - "Mark as Read" button
          - Delete button (X icon, red)
        - Quick Actions section:
          - "Mark All as Read" button
          - "Clear Read Notifications" button
      user_actions:
        - Navigate to "Notifications" from sidebar
        - View unread notification count
        - Click "Send Notification" to compose new
        - Fill in recipient, title, message
        - Click "Send Notification" to deliver
        - Review notification list
        - Click notification to view details
        - Click "Mark as Read" for unread items
        - Click X to delete notification
        - Use quick actions to manage multiple notifications
      system_response:
        - Fetch all system notifications
        - Count unread notifications
        - Display in chronological order (newest first)
        - On send: Validate form fields, create notification record
        - On mark as read: Update is_read flag
        - On delete: Remove notification from database
        - Display success toasts for actions
      database_operations:
        - SELECT * FROM notifications ORDER BY created_at DESC
        - SELECT COUNT(*) FROM notifications WHERE is_read=false
        - INSERT INTO notifications (user_id, type, title, message, created_at, is_read) VALUES (?, 'info', ?, ?, NOW(), false)
        - UPDATE notifications SET is_read=true WHERE notification_id=?
        - DELETE FROM notifications WHERE notification_id=?
        - UPDATE notifications SET is_read=true WHERE is_read=false (mark all as read)
        - DELETE FROM notifications WHERE is_read=true (clear read)
      notification_types:
        - success: Green checkmark, green background
        - error: Red X, red background
        - warning: Yellow triangle, yellow background
        - info: Green info circle, green background
      ui_components:
        - NotificationsCenter.tsx component
        - Badge for unread count
        - Collapsible form card
        - Notification cards with color coding
        - Toast notifications (sonner)
        - Quick action buttons

    - id: ADMIN-015
      scene_title: "Send Custom Notification to User"
      description: "Admin composes and sends notification to specific user"
      visuals:
        - Expanded notification form card with green theme
        - "Send New Notification" title with Send icon
        - Recipient input field
        - Title input field
        - Message textarea (4 rows)
        - Send and Cancel buttons
      user_actions:
        - Click "+ Send Notification" button
        - Enter recipient name or email
        - Enter notification title
        - Type notification message
        - Click "Send Notification"
        - View success confirmation
      system_response:
        - Validate all fields are filled
        - Look up user by name/email
        - Create notification record with type='info'
        - Set is_read=false
        - Display success toast
        - Clear form fields
        - Collapse form
        - Add new notification to top of list
      database_operations:
        - SELECT user_id FROM users WHERE name LIKE ? OR email LIKE ?
        - INSERT INTO notifications (user_id, type, title, message, is_read, created_at) VALUES (?, 'info', ?, ?, false, NOW())
      notifications:
        - User receives in-app notification
        - Optional: Send email notification as well
        - Toast: "Notification sent successfully"
      ui_components:
        - Form validation
        - Toast notifications
        - State management for form visibility

    # -------------------- ARCHIVING --------------------
    - id: ADMIN-016
      scene_title: "View Archived Records"
      description: "Admin accesses archived applications and renewals"
      visuals:
        - Green header: "Archiving" with Archive icon
        - "Manage archived business permit applications, renewals, and documents" subtitle
        - Four summary cards:
          - Total Archived count (Archive icon, gray)
          - Applications count (FileText icon, blue)
          - Renewals count (RefreshCw icon, teal)
          - Expired count (Calendar icon, orange)
        - Tabs: "Archived Applications" and "Archived Renewals"
        - Search bar and dual filters:
          - Status filter (All Status, Expired, Withdrawn, Cancelled, Completed)
          - Year filter (All Years, 2024, 2023, etc.)
        - Archive table columns:
          - Application/Renewal ID
          - Business Name
          - Applicant
          - Status (badge)
          - Date Archived
          - Archive Reason
          - Actions (Eye to view, Download icon)
        - Status badges:
          - Expired: orange
          - Withdrawn: blue
          - Cancelled: red
          - Completed: green
      user_actions:
        - Navigate to "Archiving" from sidebar
        - View archive statistics
        - Switch between Applications and Renewals tabs
        - Search archived records
        - Filter by status and year
        - Click Eye to view archived record details
        - Click Download to get archived documents
      system_response:
        - Fetch all archived applications and renewals
        - Count totals and categorize by type
        - Filter by search term, status, and year
        - Display in data table
        - Enable document downloads
      database_operations:
        - SELECT COUNT(*) FROM archived_applications
        - SELECT COUNT(*) FROM archived_renewals
        - SELECT COUNT(*) FROM archived_applications WHERE status='expired' UNION SELECT COUNT(*) FROM archived_renewals WHERE status='expired'
        - SELECT * FROM archived_applications WHERE (business_name LIKE ? OR applicant LIKE ?) AND (status=? OR 'all'=?) AND YEAR(date_archived)=?
      archive_reasons:
        - "Permit expired"
        - "Permit expired - no renewal application"
        - "Application withdrawn by applicant"
        - "Business ceased operations"
        - "Renewal completed and new permit issued"
        - "Renewal period expired - permit lapsed"
      ui_components:
        - ArchivingPage.tsx component
        - Tabs for record types
        - Multi-filter interface
        - Archive badge color coding

    - id: ADMIN-017
      scene_title: "View Archived Record Details"
      description: "Admin reviews complete details of archived application or renewal"
      visuals:
        - Modal dialog: "Archived Application/Renewal - {recordId}"
        - Archive icon or RefreshCw icon in title
        - Two-column information grid:
          - Business details
          - Original submission and archive dates
          - Archive reason prominently displayed
          - Original permit ID (for renewals)
        - Archived documents list with download buttons
        - "Download All Documents" button at bottom
      user_actions:
        - Click Eye icon on archived record
        - Review all archived information
        - View archive reason
        - Click individual document downloads
        - Click "Download All Documents" for complete package
      system_response:
        - Fetch archived record details
        - Display all historical data
        - Provide access to archived documents
        - Generate download package if requested
        - Log download activity
      database_operations:
        - SELECT * FROM archived_applications WHERE id=?
        - SELECT * FROM archived_documents WHERE archived_application_id=?
      ui_components:
        - Dialog component for details
        - Document list with download buttons
        - Toast for download confirmations

    # -------------------- ADMIN PROFILE & USER MANAGEMENT --------------------
    - id: ADMIN-018
      scene_title: "View Admin Profile"
      description: "Admin views and manages their own profile"
      visuals:
        - "Admin Profile" page with User icon
        - Two-column layout:
          - Left: Profile card with avatar (initials), name, position, status badges
          - Right: Profile information and settings
        - Profile overview card:
          - Large avatar circle with initials
          - Admin name (e.g., "Maria Elena Santos")
          - Position: "BPLO Head"
          - "Administrator" badge with Shield icon
          - Email, phone, date hired with icons
          - Last login timestamp
          - "Manage Users" button
        - Profile Information card:
          - Editable fields for name, email, phone, position
          - Department (read-only)
          - Employee ID (read-only)
          - "Edit Profile" / "Save Changes" / "Cancel" buttons
        - Account Security card:
          - "Change Password" button
        - Account Permissions card:
          - List of permissions with Shield icons (green background)
          - View Applications, Approve Permits, Manage Users, Generate Reports
      user_actions:
        - Navigate to "Admin Profile" from sidebar
        - View profile information
        - Click "Edit Profile" to enable editing
        - Update personal information
        - Click "Save Changes" to commit
        - Click "Change Password" to update password
        - View assigned permissions
        - Click "Manage Users" to navigate to user management
      system_response:
        - Fetch admin user details
        - Display in profile layout
        - Enable edit mode when requested
        - Validate changes before saving
        - Update database on save
        - Display success message
      database_operations:
        - SELECT * FROM admin_users WHERE admin_id=?
        - UPDATE admin_users SET name=?, email=?, phone=?, position=? WHERE admin_id=?
      ui_components:
        - AdminProfile.tsx component
        - Avatar with initials generation
        - Edit mode toggle
        - Permission display with icons

    - id: ADMIN-019
      scene_title: "Change Admin Password"
      description: "Admin updates their account password"
      visuals:
        - "Change Password" dialog modal
        - Three password input fields:
          - Current Password
          - New Password
          - Confirm New Password
        - Cancel and "Change Password" buttons
        - Password strength indicator (future enhancement)
      user_actions:
        - Click "Change Password" in Account Security
        - Enter current password
        - Enter new password (min 8 characters)
        - Confirm new password
        - Click "Change Password" button
      system_response:
        - Verify current password is correct
        - Validate new password meets requirements (min 8 chars)
        - Check new password and confirmation match
        - Hash new password
        - Update password in database
        - Log password change activity
        - Display success message
        - Close dialog
      database_operations:
        - SELECT password_hash FROM admin_users WHERE admin_id=?
        - UPDATE admin_users SET password_hash=?, password_updated_at=NOW() WHERE admin_id=?
        - INSERT INTO admin_activity_logs (admin_id, action, description)
      security:
        - Bcrypt password hashing
        - Password strength validation
        - Activity logging
      ui_components:
        - Dialog component
        - Password input fields
        - Validation logic
        - Toast notifications

    - id: ADMIN-020
      scene_title: "View User Management"
      description: "Admin oversees all registered user accounts"
      visuals:
        - "User Management" page with Users icon
        - "Manage registered user accounts and their permissions" subtitle
        - Four summary cards:
          - Total Users count
          - Active Users (green UserCheck icon)
          - Inactive Users (gray UserX icon)
          - Pending Verification (yellow AlertTriangle icon)
        - Search bar and dual filters:
          - Status filter (All, Active, Inactive, Suspended)
          - Verification filter (All, Verified, Pending, Flagged)
        - Users table columns:
          - User ID
          - Name (with registration date below)
          - Business (name and type)
          - Contact (email and phone with icons)
          - Status (badge with icon)
          - Verification (badge)
          - Last Login (date and time)
          - Actions (Eye, UserX/UserCheck toggle, RotateCcw for password reset)
        - Status badges:
          - Active: green with UserCheck icon
          - Inactive: gray with UserX icon
          - Suspended: red with AlertTriangle icon
        - Verification badges:
          - Verified: blue
          - Pending: yellow
          - Flagged: red
      user_actions:
        - Navigate to "User Management" from sidebar
        - View user statistics
        - Search for users by name, email, business, or user ID
        - Filter by status and verification
        - Click Eye to view detailed user account
        - Click UserX to deactivate active user
        - Click UserCheck to activate inactive user
        - Click RotateCcw to send password reset email
      system_response:
        - Fetch all registered users from database
        - Calculate statistics by status and verification
        - Apply search and filter criteria
        - Display in data table
        - On deactivate: Update status to 'inactive'
        - On activate: Update status to 'active'
        - On password reset: Send reset email
        - Display confirmation toasts
      database_operations:
        - SELECT COUNT(*) FROM users WHERE status='active'
        - SELECT COUNT(*) FROM users WHERE status='inactive'
        - SELECT COUNT(*) FROM users WHERE verification_status='pending'
        - SELECT * FROM users WHERE (name LIKE ? OR email LIKE ? OR business_name LIKE ?) AND (status=? OR 'all'=?) AND (verification_status=? OR 'all'=?)
        - UPDATE users SET status='inactive' WHERE user_id=?
        - UPDATE users SET status='active' WHERE user_id=?
      ui_components:
        - UserManagement.tsx component
        - Dual filter interface
        - Icon-based action buttons
        - Status toggle functionality
        - Toast notifications

    - id: ADMIN-021
      scene_title: "View User Account Details"
      description: "Admin accesses comprehensive user account information and activity"
      visuals:
        - "User Account Details" page
        - "Back" button to return to user management
        - Action buttons: "Reset Password" (green), "Flag Account" (red)
        - Two-column layout:
          - Left: User profile card
            - Avatar with initials (green theme)
            - User name and ID
            - Status and verification badges
            - Email, phone, address with icons
            - Last login timestamp
          - Left: Primary Business card
            - Business name, type, address
          - Right: Activity History card (larger)
            - Tabs: "Applications" and "Renewals"
            - Data tables for each with:
              - Application/Renewal ID
              - Business name and type
              - Date submitted
              - Status badge
              - Amount (₱)
              - Actions (Eye to view, Download icon)
      user_actions:
        - Click Eye icon from User Management table
        - View complete user profile
        - Review business information
        - Switch between Applications and Renewals tabs
        - View user's application history
        - Click Eye to view specific application/renewal
        - Click Download to get documents
        - Click "Reset Password" to send reset email
        - Click "Flag Account" to mark for review
        - Click "Back" to return
      system_response:
        - Fetch user details by user_id
        - Load user's applications and renewals
        - Display in organized layout
        - Enable document downloads
        - On password reset: Generate reset token, send email
        - On flag: Update user status, notify admins
      database_operations:
        - SELECT * FROM users WHERE user_id=?
        - SELECT * FROM applications WHERE user_id=? ORDER BY created_at DESC
        - SELECT * FROM renewals WHERE user_id=? ORDER BY created_at DESC
      ui_components:
        - UserAccountDetails.tsx component
        - Tabs for activity types
        - Avatar with initials
        - Dual-column layout
        - Action button group

    - id: ADMIN-022
      scene_title: "Reset User Password"
      description: "Admin sends password reset email to user account"
      visuals:
        - "Reset User Password" confirmation dialog
        - Green information box with Shield icon
        - "Password Reset Confirmation" message
        - User details: name, email, user ID
        - Cancel and "Send Reset Email" buttons (green)
      user_actions:
        - Click RotateCcw icon or "Reset Password" button
        - Review user information in confirmation dialog
        - Click "Send Reset Email" to confirm
      system_response:
        - Generate secure password reset token
        - Create password reset record with expiry (30 minutes)
        - Send password reset email to user
        - Display success toast
        - Close dialog
        - Log admin action
      database_operations:
        - INSERT INTO password_resets (user_id, token, expires_at, requested_by_admin) VALUES (?, UUID(), DATE_ADD(NOW(), INTERVAL 30 MINUTE), ?)
        - INSERT INTO admin_activity_logs (admin_id, action, target_user_id)
      email:
        - Subject: "Password Reset Request - BPLO GenSan"
        - Body: Reset link with token, valid for 30 minutes
        - Note: Reset requested by administrator
      ui_components:
        - Dialog confirmation
        - Green theme for security action
        - Toast notification

    - id: ADMIN-023
      scene_title: "Flag User Account"
      description: "Admin flags suspicious user account for investigation"
      visuals:
        - "Flag User Account" warning dialog
        - Red warning box with AlertTriangle icon
        - "Account Flagging Warning" message
        - Explanation of flagging consequences
        - User details: name, email, business name
        - Cancel and "Flag Account" buttons (red destructive style)
      user_actions:
        - Click "Flag Account" button
        - Review flagging warning
        - Confirm flagging action
      system_response:
        - Update user verification_status to 'flagged'
        - Optionally restrict account access
        - Create notification for user
        - Alert other administrators
        - Log flagging action
        - Display success toast
      database_operations:
        - UPDATE users SET verification_status='flagged', flagged_at=NOW(), flagged_by=? WHERE user_id=?
        - INSERT INTO user_flags (user_id, flagged_by, reason, created_at)
        - INSERT INTO notifications (user_id, type, title, message) VALUES (?, 'warning', 'Account Under Review', 'Your account has been flagged for administrative review')
      ui_components:
        - Warning dialog with red theme
        - Destructive button styling
        - Confirmation flow

    # -------------------- SIDEBAR NAVIGATION --------------------
    - id: ADMIN-024
      scene_title: "Sidebar Navigation"
      description: "Admin uses sidebar to navigate between system sections"
      visuals:
        - Left sidebar with green background (#0B8457)
        - BPLO header:
          - Building2 icon in teal circle (#3EB489)
          - "GenSan BPLO" title
          - "Admin Portal" subtitle
        - Navigation sections with separators:
          - Homepage section:
            - Homepage (LayoutDashboard icon)
          - Applications section:
            - Manage Applications (FileText icon)
          - Renewals section:
            - Manage Renewals (RefreshCw icon)
          - Main Menu section:
            - Payment Oversight (CreditCard icon)
            - Notifications (Bell icon)
            - Archiving (Archive icon)
          - Administration section:
            - Admin Profile (User icon)
            - User Management (Users icon)
        - Active page highlighted with teal background (#3EB489)
        - Inactive pages with semi-transparent white text
        - Hover effects on menu items
        - Logout button at bottom (red theme)
      user_actions:
        - Click any menu item to navigate
        - View active page highlight
        - Hover over items to see hover state
        - Click "Logout" to end session
      system_response:
        - Update currentPage state
        - Render corresponding component in main area
        - Update active menu highlighting
        - Maintain green color theme throughout
      ui_components:
        - Sidebar.tsx component
        - Button components for menu items
        - Separator components
        - Icon components from lucide-react
        - Active state styling with #3EB489

    - id: ADMIN-025
      scene_title: "Admin Logout"
      description: "Admin ends their session and returns to login"
      visuals:
        - Logout button in sidebar footer (red text with LogOut icon)
        - Optional confirmation dialog
        - Redirect to login page
      user_actions:
        - Click "Logout" button in sidebar
        - Confirm logout if prompted
      system_response:
        - Clear admin session
        - Destroy authentication tokens
        - Clear application state
        - Redirect to admin login page
        - Display "Successfully logged out" message
      session_management:
        - Delete admin session from sessions table
        - Clear JWT cookie
        - Reset application state
      database_operations:
        - DELETE FROM admin_sessions WHERE admin_id=? AND session_token=?
      ui_components:
        - App.tsx: handleLogout function
        - State reset: isLoggedIn=false, currentPage='login'
        - LoginPage displayed after logout

# ========================================
# TECHNICAL STACK & INTEGRATIONS
# ========================================
technical_details:
  frontend:
    framework: "React 18 with TypeScript"
    styling: "Tailwind CSS v4.0"
    ui_library: "shadcn/ui components"
    state_management: "React useState hooks"
    routing: "Client-side routing with state"
    charts: "Recharts library"
    icons: "lucide-react"
    notifications: "sonner toast library"
    
  color_theme:
    primary_green: "#0B8457"
    accent_green: "#3EB489"
    secondary_neutral: "#F2F7F5"
    warning_orange: "#FFB703"
    error_red: "#E63946"
    success_green: "#10B981"
    
  database_schema:
    tables:
      - users
      - admin_users
      - applications
      - renewals
      - payments
      - documents
      - notifications
      - activity_logs
      - password_resets
      - sessions
      - archived_applications
      - archived_renewals
      - permit_history
      
  external_services:
    payment_gateway: "PayMongo API"
    email_service: "SMTP / SendGrid / AWS SES"
    file_storage: "Local storage or AWS S3"
    
  api_endpoints:
    authentication:
      - POST /api/auth/login
      - POST /api/auth/logout
      - POST /api/auth/register
      - POST /api/auth/verify-email
      - POST /api/auth/forgot-password
      - POST /api/auth/reset-password
    applications:
      - GET /api/applications
      - POST /api/applications
      - GET /api/applications/:id
      - PUT /api/applications/:id/approve
      - PUT /api/applications/:id/reject
      - GET /api/applications/:id/documents
    renewals:
      - GET /api/renewals
      - POST /api/renewals
      - GET /api/renewals/:id
      - PUT /api/renewals/:id/approve
      - PUT /api/renewals/:id/reject
    payments:
      - GET /api/payments
      - POST /api/payments
      - PUT /api/payments/:id/verify
      - POST /api/webhooks/paymongo
    admin:
      - GET /api/admin/users
      - GET /api/admin/users/:id
      - PUT /api/admin/users/:id/activate
      - PUT /api/admin/users/:id/deactivate
      - POST /api/admin/users/:id/reset-password
      - PUT /api/admin/users/:id/flag
    notifications:
      - GET /api/notifications
      - POST /api/notifications
      - PUT /api/notifications/:id/read
      - DELETE /api/notifications/:id
      
  security_features:
    - "Bcrypt password hashing"
    - "JWT authentication with httpOnly cookies"
    - "Email verification required"
    - "Role-based access control (user, admin)"
    - "Session timeout management"
    - "CSRF protection"
    - "Input validation and sanitization"
    - "SQL injection prevention (prepared statements)"
    - "XSS protection"
    - "Secure file upload validation"
    
  file_upload:
    allowed_types:
      - "application/pdf"
      - "image/jpeg"
      - "image/png"
    max_file_size: "5MB per file"
    storage_path: "/uploads/{type}/{id}/"
    
  notification_channels:
    - "In-app notifications"
    - "Email notifications"
    - "Optional: SMS for critical updates"
    
  workflow_automation:
    - "Auto-archive expired permits after 90 days"
    - "Send renewal reminders 60 days before expiry"
    - "Email notifications on status changes"
    - "Webhook processing for payment confirmations"
    
  reporting:
    - "Application statistics by month/year"
    - "Revenue reports from payments"
    - "User activity reports"
    - "Permit issuance analytics"

# ========================================
# END OF STORYBOARD
# ========================================
