# Smart Governance: Business Permit Processing and Renewal System
# ADMIN MODULE - Comprehensive UI/UX System Analysis
# Generated: 2025-01-22
# Tech Stack: React + Vite, Node.js + Express, PostgreSQL 16+

storyboard:
  - screen_id: "admin_login"
    title: "Admin Login"
    purpose: "Secure authentication entry point for BPLO admin personnel"
    user_actions:
      - "Enter username/email credentials"
      - "Enter password"
      - "Submit login form"
    system_response:
      - "Validate credentials against admin database"
      - "Generate session token on successful authentication"
      - "Redirect to admin dashboard"
      - "Display error message for invalid credentials"
    next_screens:
      - "admin_dashboard"
  
  - screen_id: "admin_dashboard"
    title: "Admin Dashboard (Homepage)"
    purpose: "Central overview of permit applications, renewals, and system metrics"
    user_actions:
      - "View BPLO official banner with city branding"
      - "Review statistics in overview cards (Pending, Approved, Rejected, Archived)"
      - "Analyze monthly applications and renewals bar chart"
      - "Check renewal status distribution pie chart"
      - "Monitor recent activity feed"
    system_response:
      - "Fetch and display aggregated statistics"
      - "Render interactive charts with real-time data"
      - "Stream recent activity updates"
    next_screens:
      - "manage_applications"
      - "manage_renewals"
      - "payment_oversight"
      - "notifications_center"
      - "archiving_page"
      - "admin_profile"
      - "user_management"
  
  - screen_id: "manage_applications"
    title: "Manage Applications"
    purpose: "Review, approve, reject business permit applications with detailed oversight"
    user_actions:
      - "Search applications by business name, applicant, or ID"
      - "Filter applications by status (all, pending, approved, rejected)"
      - "View application details in modal dialog"
      - "Approve pending applications"
      - "Reject pending applications with reason"
      - "Review submitted documents"
    system_response:
      - "Fetch applications from database with pagination"
      - "Filter and search results in real-time"
      - "Update application status in database"
      - "Send notification to applicant on status change"
      - "Log admin action in audit trail"
    next_screens:
      - "admin_dashboard"
  
  - screen_id: "manage_renewals"
    title: "Manage Renewals"
    purpose: "Handle business permit renewal requests with dedicated workflow"
    user_actions:
      - "Search renewals by business name, applicant, or renewal ID"
      - "Filter renewals by status (all, pending, approved, rejected)"
      - "View renewal details including original permit ID and expiry date"
      - "Check renewal payment status"
      - "Approve or reject renewal requests"
      - "Review updated compliance documents"
    system_response:
      - "Fetch renewal applications from database"
      - "Cross-reference with original permit records"
      - "Validate payment status before approval"
      - "Update renewal status and generate new permit"
      - "Send renewal confirmation to business owner"
    next_screens:
      - "admin_dashboard"
  
  - screen_id: "payment_oversight"
    title: "Payment Oversight"
    purpose: "Track and verify payments for applications and renewals"
    user_actions:
      - "View application payments and renewal payments in separate tabs"
      - "Search payments by applicant, business name, or payment ID"
      - "Filter payments by status (all, paid, pending, failed)"
      - "Verify payment reference numbers"
      - "Manually mark payments as verified or failed"
      - "Review payment method (GCash, Bank Transfer, OTC, Credit Card, PayMaya)"
    system_response:
      - "Fetch payment records from database"
      - "Integrate with payment gateway APIs for status updates"
      - "Update payment status in real-time"
      - "Generate payment receipts"
      - "Trigger application/renewal workflow on payment confirmation"
    next_screens:
      - "admin_dashboard"
      - "manage_applications"
      - "manage_renewals"
  
  - screen_id: "notifications_center"
    title: "Notifications Center"
    purpose: "Manage system alerts, user notifications, and administrative announcements"
    user_actions:
      - "View all system notifications (applications, renewals, payments)"
      - "Filter notifications by type and priority"
      - "Mark notifications as read"
      - "Create broadcast announcements to users"
      - "Schedule notification delivery"
    system_response:
      - "Fetch notifications from database"
      - "Mark notifications as read in database"
      - "Send push notifications to users"
      - "Log notification delivery status"
    next_screens:
      - "admin_dashboard"
  
  - screen_id: "archiving_page"
    title: "Archiving Page"
    purpose: "Access and manage completed, expired, or archived applications"
    user_actions:
      - "Search archived records"
      - "Filter by date range, business type, or status"
      - "View archived application details"
      - "Export archived records to CSV/PDF"
      - "Restore archived records if needed"
    system_response:
      - "Fetch archived records from database"
      - "Generate export files"
      - "Update archive status"
    next_screens:
      - "admin_dashboard"
  
  - screen_id: "admin_profile"
    title: "Admin Profile"
    purpose: "Manage admin account settings, credentials, and profile information"
    user_actions:
      - "View profile overview with avatar and role"
      - "Edit profile information (name, email, phone)"
      - "Change password with validation"
      - "View permissions and access levels"
      - "Review last login and activity history"
      - "Update notification preferences"
    system_response:
      - "Fetch admin profile from database"
      - "Validate profile updates"
      - "Hash and store new password"
      - "Update session with new profile data"
      - "Send confirmation email on profile changes"
    next_screens:
      - "admin_dashboard"
  
  - screen_id: "user_management"
    title: "User Management"
    purpose: "Oversee user accounts, business profiles, and system access"
    user_actions:
      - "View all registered users in data table"
      - "Search users by name, email, or business"
      - "Filter users by status (active, inactive, suspended)"
      - "View user account details"
      - "Suspend or reactivate user accounts"
      - "Reset user passwords"
      - "View user application history"
    system_response:
      - "Fetch user accounts from database"
      - "Update user account status"
      - "Generate password reset tokens"
      - "Send email notifications to users"
      - "Log admin actions in audit trail"
    next_screens:
      - "user_account_details"
      - "admin_dashboard"
  
  - screen_id: "user_account_details"
    title: "User Account Details"
    purpose: "Deep dive into specific user account with full application history"
    user_actions:
      - "View user profile information"
      - "Review business profiles linked to user"
      - "Check application history"
      - "View payment records"
      - "Suspend or activate account"
      - "Send direct message to user"
    system_response:
      - "Fetch comprehensive user data"
      - "Display linked applications and payments"
      - "Update account status"
      - "Send notifications to user"
    next_screens:
      - "user_management"

screens:
  - id: "admin_login"
    title: "Admin Login"
    route: "/admin/login"
    description: "Secure login page with GenSan BPLO branding, featuring gradient background and centered card layout"
    components:
      - type: "Card"
        name: "login_card"
        properties: "shadow-2xl, border-0, custom shadow with green tint"
        hierarchy: "Container > Card > CardHeader + CardContent"
      - type: "ImageWithFallback"
        name: "city_logo"
        properties: "w-16 h-16, rounded-full, inside green circular background"
        hierarchy: "CardHeader > Logo Container > ImageWithFallback"
      - type: "CardTitle"
        name: "portal_title"
        properties: "text-xl, color #2E2E2E"
        hierarchy: "CardHeader > Title"
      - type: "Form"
        name: "login_form"
        properties: "space-y-6, onSubmit handler"
        hierarchy: "CardContent > Form > FormFields"
    inputs:
      - name: "username"
        type: "text"
        validation: "required"
        placeholder: "Enter your username or email"
      - name: "password"
        type: "password"
        validation: "required"
        placeholder: "Enter your password"
    buttons:
      - label: "Sign In"
        action: "Submit form and authenticate admin"
        variant: "primary"
        state: "default, loading on submit"
    api_needed:
      - method: "POST"
        endpoint: "/api/admin/auth/login"
        purpose: "Authenticate admin credentials"
        request_body: "{ username: string, password: string }"
        response: "{ success: boolean, token: string, admin: { id, name, role, permissions } }"

  - id: "admin_dashboard"
    title: "Admin Dashboard (Homepage)"
    route: "/admin/dashboard"
    description: "Main dashboard with BPLO banner, statistics cards, charts, and recent activity feed"
    components:
      - type: "Banner"
        name: "bplo_banner"
        properties: "full-width, rounded-xl, gradient background with image overlay"
        hierarchy: "Container > Banner > Image + Overlay + Caption"
      - type: "GradientHeader"
        name: "page_header"
        properties: "green gradient background (#0B8457 to #3EB489), white text"
        hierarchy: "Container > Header > Title + Description"
      - type: "StatsCard"
        name: "overview_cards"
        properties: "Grid of 4 cards with left border accent, icons, and trend indicators"
        hierarchy: "Container > Grid > Cards (Pending, Approved, Rejected, Archived)"
      - type: "BarChart"
        name: "monthly_chart"
        properties: "Recharts BarChart, dual bars (applications + renewals), green colors"
        hierarchy: "Card > CardContent > ResponsiveContainer > BarChart"
      - type: "PieChart"
        name: "renewal_distribution"
        properties: "Recharts PieChart, percentage labels, status colors"
        hierarchy: "Card > CardContent > ResponsiveContainer > PieChart"
      - type: "ActivityFeed"
        name: "recent_activity"
        properties: "List of activity items with colored status dots, timestamps"
        hierarchy: "Card > CardContent > ActivityList"
    inputs: []
    buttons: []
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/dashboard/stats"
        purpose: "Fetch overview statistics (pending, approved, rejected, archived counts)"
        request_body: "none"
        response: "{ pending: number, approved: number, rejected: number, archived: number }"
      - method: "GET"
        endpoint: "/api/admin/dashboard/monthly-data"
        purpose: "Fetch monthly applications and renewals data for chart"
        request_body: "none"
        response: "{ months: [{ month, applications, renewals, payments }] }"
      - method: "GET"
        endpoint: "/api/admin/dashboard/recent-activity"
        purpose: "Fetch recent system activity feed"
        request_body: "none"
        response: "{ activities: [{ action, time, type }] }"

  - id: "manage_applications"
    title: "Manage Applications"
    route: "/admin/applications"
    description: "Comprehensive application management with search, filters, data table, and detail modal"
    components:
      - type: "GradientHeader"
        name: "page_header"
        properties: "green gradient, white text"
        hierarchy: "Container > Header"
      - type: "StatsOverview"
        name: "stats_cards"
        properties: "Grid of 4 cards with colored left borders matching status"
        hierarchy: "Container > Grid > Cards"
      - type: "SearchFilter"
        name: "filter_section"
        properties: "Card containing search input + status select"
        hierarchy: "Card > CardContent > SearchInput + SelectFilter"
      - type: "DataTable"
        name: "applications_table"
        properties: "shadcn/ui Table with green header row, hover states"
        hierarchy: "Card > CardContent > Table > TableHeader + TableBody"
      - type: "Dialog"
        name: "application_details_modal"
        properties: "max-w-2xl, displays full application details"
        hierarchy: "Dialog > DialogContent > Details Grid + Documents + Actions"
    inputs:
      - name: "search"
        type: "text"
        validation: "none"
        placeholder: "Search by business name, applicant, or ID..."
      - name: "status_filter"
        type: "select"
        validation: "none"
        placeholder: "Filter by status"
    buttons:
      - label: "View Details (Eye Icon)"
        action: "Open application details modal"
        variant: "outline"
        state: "default"
      - label: "Approve (Check Icon)"
        action: "Approve pending application"
        variant: "outline (green)"
        state: "visible only for pending applications"
      - label: "Reject (X Icon)"
        action: "Reject pending application"
        variant: "outline (red)"
        state: "visible only for pending applications"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/applications"
        purpose: "Fetch all applications with pagination and filters"
        request_body: "?page=1&limit=20&status=pending&search=keyword"
        response: "{ applications: [], total: number, page: number }"
      - method: "GET"
        endpoint: "/api/admin/applications/:id"
        purpose: "Fetch detailed application data"
        request_body: "none"
        response: "{ application: { id, businessName, applicant, documents, status, ... } }"
      - method: "PUT"
        endpoint: "/api/admin/applications/:id/approve"
        purpose: "Approve application"
        request_body: "{ adminId: string, notes: string }"
        response: "{ success: boolean, message: string }"
      - method: "PUT"
        endpoint: "/api/admin/applications/:id/reject"
        purpose: "Reject application"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"

  - id: "manage_renewals"
    title: "Manage Renewals"
    route: "/admin/renewals"
    description: "Renewal-specific management with payment status, original permit tracking"
    components:
      - type: "GradientHeader"
        name: "page_header"
        properties: "green gradient, white text, RefreshCw icon"
        hierarchy: "Container > Header"
      - type: "StatsOverview"
        name: "renewal_stats"
        properties: "Grid of cards showing renewal metrics"
        hierarchy: "Container > Grid > Cards"
      - type: "SearchFilter"
        name: "filter_section"
        properties: "Search + status filter + payment filter"
        hierarchy: "Card > CardContent > Filters"
      - type: "DataTable"
        name: "renewals_table"
        properties: "Table with renewal-specific columns (renewal ID, original permit, expiry date, payment status)"
        hierarchy: "Card > CardContent > Table"
      - type: "Dialog"
        name: "renewal_details_modal"
        properties: "Displays renewal info, original permit, payment status, documents"
        hierarchy: "Dialog > DialogContent > Details"
    inputs:
      - name: "search"
        type: "text"
        validation: "none"
        placeholder: "Search renewals..."
      - name: "status_filter"
        type: "select"
        validation: "none"
        placeholder: "Filter by status"
      - name: "payment_filter"
        type: "select"
        validation: "none"
        placeholder: "Filter by payment status"
    buttons:
      - label: "View Details"
        action: "Open renewal details modal"
        variant: "outline"
        state: "default"
      - label: "Approve Renewal"
        action: "Approve renewal and generate new permit"
        variant: "primary (green)"
        state: "enabled for pending renewals with paid status"
      - label: "Reject Renewal"
        action: "Reject renewal with reason"
        variant: "destructive (red)"
        state: "enabled for pending renewals"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/renewals"
        purpose: "Fetch all renewal applications"
        request_body: "?page=1&limit=20&status=pending&paymentStatus=paid"
        response: "{ renewals: [], total: number }"
      - method: "GET"
        endpoint: "/api/admin/renewals/:id"
        purpose: "Fetch detailed renewal data with original permit"
        request_body: "none"
        response: "{ renewal: { id, originalPermitId, expiryDate, paymentStatus, ... } }"
      - method: "PUT"
        endpoint: "/api/admin/renewals/:id/approve"
        purpose: "Approve renewal and generate new permit"
        request_body: "{ adminId: string, newExpiryDate: string }"
        response: "{ success: boolean, newPermitId: string }"
      - method: "PUT"
        endpoint: "/api/admin/renewals/:id/reject"
        purpose: "Reject renewal"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"

  - id: "payment_oversight"
    title: "Payment Oversight"
    route: "/admin/payments"
    description: "Payment tracking system with tabs for applications and renewals"
    components:
      - type: "GradientHeader"
        name: "page_header"
        properties: "green gradient, CreditCard icon"
        hierarchy: "Container > Header"
      - type: "Tabs"
        name: "payment_tabs"
        properties: "shadcn/ui Tabs, two tabs (Application Payments, Renewal Payments)"
        hierarchy: "Container > Tabs > TabsList + TabsContent"
      - type: "StatsCards"
        name: "payment_stats"
        properties: "Revenue, pending, failed payment counts"
        hierarchy: "TabsContent > Grid > Cards"
      - type: "SearchFilter"
        name: "payment_filters"
        properties: "Search + status filter"
        hierarchy: "Card > Filters"
      - type: "DataTable"
        name: "payments_table"
        properties: "Table showing payment ID, applicant, amount, status, method, reference number"
        hierarchy: "Card > Table"
    inputs:
      - name: "search"
        type: "text"
        validation: "none"
        placeholder: "Search payments..."
      - name: "status_filter"
        type: "select"
        validation: "none"
        placeholder: "Filter by payment status"
    buttons:
      - label: "Verify Payment"
        action: "Manually verify payment and update status"
        variant: "primary (green)"
        state: "enabled for pending payments"
      - label: "Mark as Failed"
        action: "Mark payment as failed"
        variant: "destructive"
        state: "enabled for pending payments"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/payments/applications"
        purpose: "Fetch application payments"
        request_body: "?status=pending"
        response: "{ payments: [], totalRevenue: number }"
      - method: "GET"
        endpoint: "/api/admin/payments/renewals"
        purpose: "Fetch renewal payments"
        request_body: "?status=paid"
        response: "{ payments: [], totalRevenue: number }"
      - method: "PUT"
        endpoint: "/api/admin/payments/:id/verify"
        purpose: "Verify payment manually"
        request_body: "{ adminId: string, verificationNotes: string }"
        response: "{ success: boolean, message: string }"
      - method: "PUT"
        endpoint: "/api/admin/payments/:id/mark-failed"
        purpose: "Mark payment as failed"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"

  - id: "notifications_center"
    title: "Notifications Center"
    route: "/admin/notifications"
    description: "System notification hub with filtering and broadcast capabilities"
    components:
      - type: "GradientHeader"
        name: "page_header"
        properties: "green gradient, Bell icon"
        hierarchy: "Container > Header"
      - type: "NotificationList"
        name: "notification_feed"
        properties: "List of notification cards with priority badges"
        hierarchy: "Container > NotificationList > NotificationCard"
      - type: "FilterSection"
        name: "notification_filters"
        properties: "Filter by type, priority, read/unread status"
        hierarchy: "Card > Filters"
      - type: "Dialog"
        name: "broadcast_dialog"
        properties: "Create broadcast announcement form"
        hierarchy: "Dialog > Form"
    inputs:
      - name: "type_filter"
        type: "select"
        validation: "none"
        placeholder: "Filter by type"
      - name: "priority_filter"
        type: "select"
        validation: "none"
        placeholder: "Filter by priority"
    buttons:
      - label: "Mark as Read"
        action: "Mark selected notifications as read"
        variant: "outline"
        state: "default"
      - label: "Create Broadcast"
        action: "Open broadcast announcement dialog"
        variant: "primary (green)"
        state: "default"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/notifications"
        purpose: "Fetch all system notifications"
        request_body: "?type=application&priority=high&unread=true"
        response: "{ notifications: [], unreadCount: number }"
      - method: "PUT"
        endpoint: "/api/admin/notifications/mark-read"
        purpose: "Mark notifications as read"
        request_body: "{ notificationIds: string[] }"
        response: "{ success: boolean }"
      - method: "POST"
        endpoint: "/api/admin/notifications/broadcast"
        purpose: "Create broadcast announcement"
        request_body: "{ title: string, message: string, priority: string, targetUsers: string[] }"
        response: "{ success: boolean, broadcastId: string }"

  - id: "archiving_page"
    title: "Archiving Page"
    route: "/admin/archive"
    description: "Archive management for completed and expired applications"
    components:
      - type: "GradientHeader"
        name: "page_header"
        properties: "green gradient, Archive icon"
        hierarchy: "Container > Header"
      - type: "SearchFilter"
        name: "archive_filters"
        properties: "Search, date range picker, business type filter"
        hierarchy: "Card > Filters"
      - type: "DataTable"
        name: "archive_table"
        properties: "Table with archived application records"
        hierarchy: "Card > Table"
    inputs:
      - name: "search"
        type: "text"
        validation: "none"
        placeholder: "Search archived records..."
      - name: "date_from"
        type: "date"
        validation: "none"
        placeholder: "From date"
      - name: "date_to"
        type: "date"
        validation: "none"
        placeholder: "To date"
      - name: "business_type"
        type: "select"
        validation: "none"
        placeholder: "Business type"
    buttons:
      - label: "Export to CSV"
        action: "Export filtered archive records to CSV"
        variant: "outline"
        state: "default"
      - label: "Export to PDF"
        action: "Export filtered archive records to PDF"
        variant: "outline"
        state: "default"
      - label: "Restore"
        action: "Restore archived record to active status"
        variant: "primary"
        state: "enabled for selected records"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/archive"
        purpose: "Fetch archived records with filters"
        request_body: "?dateFrom=2024-01-01&dateTo=2024-12-31&businessType=Restaurant"
        response: "{ archivedRecords: [], total: number }"
      - method: "GET"
        endpoint: "/api/admin/archive/export"
        purpose: "Export archive records"
        request_body: "?format=csv&filters={...}"
        response: "{ downloadUrl: string }"
      - method: "PUT"
        endpoint: "/api/admin/archive/:id/restore"
        purpose: "Restore archived record"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean }"

  - id: "admin_profile"
    title: "Admin Profile"
    route: "/admin/profile"
    description: "Admin account management with profile editing and password change"
    components:
      - type: "PageHeader"
        name: "page_header"
        properties: "User icon, title, description"
        hierarchy: "Container > Header"
      - type: "ProfileCard"
        name: "profile_overview"
        properties: "Avatar, name, role, badge, quick stats"
        hierarchy: "Container > ProfileCard"
      - type: "Form"
        name: "profile_edit_form"
        properties: "Editable profile fields with save/cancel buttons"
        hierarchy: "Card > Form > FormFields"
      - type: "Dialog"
        name: "change_password_dialog"
        properties: "Password change form with validation"
        hierarchy: "Dialog > Form"
      - type: "PermissionsSection"
        name: "permissions_list"
        properties: "Display admin permissions and access levels"
        hierarchy: "Card > PermissionsList"
    inputs:
      - name: "name"
        type: "text"
        validation: "required, min: 3"
        placeholder: "Full name"
      - name: "email"
        type: "email"
        validation: "required, valid email format"
        placeholder: "Email address"
      - name: "phone"
        type: "tel"
        validation: "required, phone format"
        placeholder: "Contact number"
      - name: "current_password"
        type: "password"
        validation: "required"
        placeholder: "Current password"
      - name: "new_password"
        type: "password"
        validation: "required, min: 8, password strength"
        placeholder: "New password"
      - name: "confirm_password"
        type: "password"
        validation: "required, must match new_password"
        placeholder: "Confirm new password"
    buttons:
      - label: "Edit Profile"
        action: "Enable profile editing mode"
        variant: "outline"
        state: "default"
      - label: "Save Changes"
        action: "Save updated profile information"
        variant: "primary (green)"
        state: "visible in edit mode"
      - label: "Cancel"
        action: "Cancel profile editing"
        variant: "outline"
        state: "visible in edit mode"
      - label: "Change Password"
        action: "Open password change dialog"
        variant: "outline"
        state: "default"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/profile"
        purpose: "Fetch current admin profile data"
        request_body: "none"
        response: "{ admin: { id, name, email, phone, position, permissions, ... } }"
      - method: "PUT"
        endpoint: "/api/admin/profile"
        purpose: "Update admin profile information"
        request_body: "{ name: string, email: string, phone: string }"
        response: "{ success: boolean, message: string }"
      - method: "PUT"
        endpoint: "/api/admin/profile/change-password"
        purpose: "Change admin password"
        request_body: "{ currentPassword: string, newPassword: string }"
        response: "{ success: boolean, message: string }"

  - id: "user_management"
    title: "User Management"
    route: "/admin/users"
    description: "User account oversight with search, filtering, and account actions"
    components:
      - type: "GradientHeader"
        name: "page_header"
        properties: "green gradient, Users icon"
        hierarchy: "Container > Header"
      - type: "StatsOverview"
        name: "user_stats"
        properties: "Total users, active, suspended counts"
        hierarchy: "Container > Grid > Cards"
      - type: "SearchFilter"
        name: "user_filters"
        properties: "Search by name/email, status filter"
        hierarchy: "Card > Filters"
      - type: "DataTable"
        name: "users_table"
        properties: "Table showing user ID, name, email, business, status, join date"
        hierarchy: "Card > Table"
    inputs:
      - name: "search"
        type: "text"
        validation: "none"
        placeholder: "Search by name, email, or business..."
      - name: "status_filter"
        type: "select"
        validation: "none"
        placeholder: "Filter by status"
    buttons:
      - label: "View Details"
        action: "Navigate to user account details page"
        variant: "outline (green)"
        state: "default"
      - label: "Suspend"
        action: "Suspend user account"
        variant: "destructive"
        state: "enabled for active users"
      - label: "Reactivate"
        action: "Reactivate suspended account"
        variant: "primary (green)"
        state: "enabled for suspended users"
      - label: "Reset Password"
        action: "Send password reset email to user"
        variant: "outline"
        state: "default"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/users"
        purpose: "Fetch all user accounts"
        request_body: "?page=1&limit=20&status=active&search=keyword"
        response: "{ users: [], total: number, page: number }"
      - method: "GET"
        endpoint: "/api/admin/users/:id"
        purpose: "Fetch specific user details"
        request_body: "none"
        response: "{ user: { id, name, email, businesses, applications, status, ... } }"
      - method: "PUT"
        endpoint: "/api/admin/users/:id/suspend"
        purpose: "Suspend user account"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"
      - method: "PUT"
        endpoint: "/api/admin/users/:id/reactivate"
        purpose: "Reactivate suspended user account"
        request_body: "{ adminId: string }"
        response: "{ success: boolean, message: string }"
      - method: "POST"
        endpoint: "/api/admin/users/:id/reset-password"
        purpose: "Send password reset email"
        request_body: "{ adminId: string }"
        response: "{ success: boolean, resetTokenSent: boolean }"

  - id: "user_account_details"
    title: "User Account Details"
    route: "/admin/users/:id"
    description: "Detailed view of specific user with full application and payment history"
    components:
      - type: "BackButton"
        name: "back_to_list"
        properties: "Navigate back to user management"
        hierarchy: "Container > BackButton"
      - type: "UserProfileCard"
        name: "user_profile"
        properties: "Avatar, name, email, status badge, account info"
        hierarchy: "Container > ProfileCard"
      - type: "BusinessProfilesList"
        name: "linked_businesses"
        properties: "List of businesses owned by user"
        hierarchy: "Card > BusinessList"
      - type: "ApplicationHistory"
        name: "application_history"
        properties: "Table of all applications submitted by user"
        hierarchy: "Card > Table"
      - type: "PaymentHistory"
        name: "payment_history"
        properties: "Table of all payments made by user"
        hierarchy: "Card > Table"
    inputs: []
    buttons:
      - label: "Send Message"
        action: "Send direct message to user"
        variant: "outline"
        state: "default"
      - label: "Suspend Account"
        action: "Suspend user account with reason"
        variant: "destructive"
        state: "enabled for active users"
      - label: "Activate Account"
        action: "Activate suspended account"
        variant: "primary (green)"
        state: "enabled for suspended users"
    api_needed:
      - method: "GET"
        endpoint: "/api/admin/users/:id/details"
        purpose: "Fetch comprehensive user data"
        request_body: "none"
        response: "{ user: {...}, businesses: [], applications: [], payments: [] }"
      - method: "POST"
        endpoint: "/api/admin/users/:id/message"
        purpose: "Send direct message to user"
        request_body: "{ adminId: string, subject: string, message: string }"
        response: "{ success: boolean, messageSent: boolean }"

colors:
  palette:
    primary: "#0B8457 - Primary green (GenSan BPLO brand color)"
    secondary: "#F2F7F5 - Secondary neutral (light green background)"
    accent: "#3EB489 - Accent green (highlights and active states)"
    green_light: "#66CBA1 - Light green (tertiary accent)"
    green_dark: "#1A9A6B - Dark green (borders and sidebar accents)"
    background: "#ffffff - Main background"
    surface: "#F2F7F5 - Card/surface background"
    border: "rgba(11, 132, 87, 0.2) - Border with green tint"
    text_primary: "#2E2E2E - Primary text"
    text_secondary: "#717182 - Secondary text (muted)"
    error: "#E63946 - Error state (rejections, failed payments)"
    success: "#0B8457 - Success state (approvals, primary green)"
    warning: "#FFB703 - Warning state (pending status)"
    info: "#3EB489 - Info state (accent green)"
    destructive: "#E63946 - Destructive actions"
    sidebar: "#0B8457 - Sidebar background"
    sidebar_accent: "#3EB489 - Sidebar active state"
    input_background: "#F2F7F5 - Input field background"
  
  raw_hex_values:
    - "#0B8457 - Primary green: sidebar, buttons, headers, success badges"
    - "#3EB489 - Accent green: active nav items, highlights, secondary buttons"
    - "#F2F7F5 - Light green bg: page background, card surfaces, input backgrounds"
    - "#66CBA1 - Light green: chart colors, tertiary elements"
    - "#1A9A6B - Dark green: sidebar borders, hover states"
    - "#2E2E2E - Dark text: primary text color"
    - "#717182 - Muted text: secondary text, captions"
    - "#FFB703 - Yellow/amber: pending status badges"
    - "#10B981 - Bright green: approved status badges (Tailwind green-500)"
    - "#EF4444 - Red: rejected status badges (Tailwind red-500)"
    - "#E63946 - Destructive red: error states, reject buttons"
    - "#6B7280 - Gray: archived status, neutral elements"
    - "#F59E0B - Orange: warning indicators (Tailwind amber-500)"
    - "#ffffff - White: card backgrounds, text on green backgrounds"

components:
  atoms:
    - "Button (primary: green #0B8457, outline: green border, destructive: red #E63946, ghost: transparent)"
    - "Input (text, email, password, tel with #F2F7F5 background and green focus ring)"
    - "Badge (status variants: yellow pending, green approved, red rejected, gray archived)"
    - "Icon (Lucide React: LayoutDashboard, FileText, RefreshCw, CreditCard, Bell, Archive, User, Users, Building2, LogOut, Check, X, Eye, Search, Filter, Calendar, Phone, Mail, Lock, Shield, Settings, Save, Edit)"
    - "Label (form labels with medium weight)"
    - "Avatar (with fallback showing initials)"
    - "Separator (rgba(255,255,255,0.2) for sidebar, default for content)"
  
  molecules:
    - "FormField (Label + Input + Error message)"
    - "StatusBadge (Badge + text, color-coded by status)"
    - "StatCard (Card + Icon + Number + Trend indicator + Left border accent)"
    - "SearchInput (Input with Search icon prefix)"
    - "SelectFilter (Select with Filter icon + options)"
    - "ActionButtons (Group of icon buttons: View, Approve, Reject)"
    - "NotificationCard (Card + priority badge + timestamp + message)"
    - "ActivityItem (Colored dot + action text + timestamp)"
  
  organisms:
    - "Sidebar (Logo + Navigation sections + Logout button - green #0B8457 background)"
    - "GradientHeader (Gradient background #0B8457 to #3EB489 + Title + Description)"
    - "StatsOverview (Grid of StatCards with icons and left border colors)"
    - "DataTable (shadcn/ui Table + green header row + hover states + action buttons)"
    - "ApplicationDetailsDialog (Dialog + form fields + document list + action buttons)"
    - "ChartCard (Card + Recharts BarChart/PieChart + title)"
    - "ActivityFeed (List of activity items with status indicators)"
    - "FilterBar (Card with search input + multiple select filters)"
    - "ProfileCard (Avatar + name + role badge + info fields + edit button)"
    - "BPLOBanner (Image + overlay + caption + official badge)"
  
  reused_from_design_system:
    - "shadcn/ui: Button, Input, Card, Select, Badge, Dialog, Table, Tabs, Avatar, Label, Separator, Tooltip"
    - "Recharts: BarChart, PieChart, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer"
    - "Lucide React: All icons"
    - "Sonner: Toast notifications (toast.success, toast.error)"

layout:
  grid: "CSS Grid and Flexbox - Grid for card layouts, Flexbox for sidebar and navigation"
  container_max_width: "No max width constraint on main container, sidebar fixed at 256px (w-64)"
  spacing_scale: "Tailwind default scale - 0, 1 (4px), 2 (8px), 3 (12px), 4 (16px), 6 (24px), 8 (32px), 12 (48px)"
  border_radius: "10px (0.625rem) base radius, sm: 6px, md: 8px, lg: 10px, xl: 14px"
  shadows: 
    - "sm: standard Tailwind shadow-sm"
    - "md: standard Tailwind shadow-md"
    - "custom: 0 4px 6px rgba(11, 132, 87, 0.1) - green-tinted shadow for cards"
    - "custom-2xl: 0 20px 25px rgba(11, 132, 87, 0.3) - login card shadow"
  typography:
    font_family: "System default font stack (not explicitly defined, inherits from browser)"
    headings: "h1: 1.5rem (24px) medium weight, h2: 1.25rem (20px) medium weight, h3: 1.125rem (18px) medium weight, h4: 1rem (16px) medium weight"
    body: "1rem (16px) normal weight, line-height: 1.5"
    labels: "1rem (16px) medium weight, line-height: 1.5"
    buttons: "1rem (16px) medium weight, line-height: 1.5"
    line_height: "1.5 for all text elements"
  responsive_breakpoints:
    - "sm: 640px"
    - "md: 768px"
    - "lg: 1024px"
    - "xl: 1280px"

user_flow:
  start: "admin_login"
  steps:
    - step: 1
      screen: "admin_login"
      action: "Admin enters username/email and password"
      validation: "Required fields validation on blur and submit"
      api_call: "POST /api/admin/auth/login"
      result: "On success, store JWT token and redirect to admin_dashboard"
      error_handling: "Display toast error for invalid credentials or server errors"
    
    - step: 2
      screen: "admin_dashboard"
      action: "Admin views overview statistics, charts, and recent activity"
      validation: "None (read-only view)"
      api_call: "GET /api/admin/dashboard/stats, GET /api/admin/dashboard/monthly-data, GET /api/admin/dashboard/recent-activity"
      result: "Display aggregated data in cards, charts, and activity feed"
      error_handling: "Show skeleton loaders during fetch, display error message if API fails"
    
    - step: 3
      screen: "manage_applications"
      action: "Admin clicks 'Manage Applications' in sidebar"
      validation: "None"
      api_call: "GET /api/admin/applications?status=all&page=1&limit=20"
      result: "Navigate to applications page, display list of applications in table"
      error_handling: "Show loading state, handle empty state, API error toast"
    
    - step: 4
      screen: "manage_applications"
      action: "Admin searches for application by business name or ID"
      validation: "None (client-side search or debounced API call)"
      api_call: "GET /api/admin/applications?search=keyword"
      result: "Filter applications list in real-time"
      error_handling: "Show 'No results found' message if no matches"
    
    - step: 5
      screen: "manage_applications"
      action: "Admin clicks 'View Details' (Eye icon) on an application"
      validation: "None"
      api_call: "GET /api/admin/applications/:id"
      result: "Open modal dialog with detailed application information"
      error_handling: "Show loading spinner in modal, error toast if fetch fails"
    
    - step: 6
      screen: "manage_applications"
      action: "Admin clicks 'Approve' (Check icon) for pending application"
      validation: "Confirm action (optional)"
      api_call: "PUT /api/admin/applications/:id/approve { adminId, notes }"
      result: "Update application status to 'approved', close modal, refresh table, show success toast"
      error_handling: "Show error toast if approval fails, revert UI state"
    
    - step: 7
      screen: "manage_applications"
      action: "Admin clicks 'Reject' (X icon) for pending application"
      validation: "Require rejection reason (optional)"
      api_call: "PUT /api/admin/applications/:id/reject { adminId, reason }"
      result: "Update application status to 'rejected', close modal, refresh table, show success toast"
      error_handling: "Show error toast if rejection fails, revert UI state"
    
    - step: 8
      screen: "manage_renewals"
      action: "Admin navigates to 'Manage Renewals' from sidebar"
      validation: "None"
      api_call: "GET /api/admin/renewals?status=all&page=1&limit=20"
      result: "Display renewal applications in table with payment status"
      error_handling: "Loading skeleton, error toast on API failure"
    
    - step: 9
      screen: "manage_renewals"
      action: "Admin filters renewals by payment status (paid/pending)"
      validation: "None"
      api_call: "GET /api/admin/renewals?paymentStatus=paid"
      result: "Filter renewals table based on payment status"
      error_handling: "Handle empty state if no results"
    
    - step: 10
      screen: "manage_renewals"
      action: "Admin approves renewal with paid status"
      validation: "Check payment status is 'paid' before allowing approval"
      api_call: "PUT /api/admin/renewals/:id/approve { adminId, newExpiryDate }"
      result: "Generate new permit, update renewal status, show success toast"
      error_handling: "Block approval if payment not verified, show error toast on failure"
    
    - step: 11
      screen: "payment_oversight"
      action: "Admin navigates to 'Payment Oversight' from sidebar"
      validation: "None"
      api_call: "GET /api/admin/payments/applications, GET /api/admin/payments/renewals"
      result: "Display payments in tabs (Application Payments, Renewal Payments)"
      error_handling: "Loading state for each tab, error handling per tab"
    
    - step: 12
      screen: "payment_oversight"
      action: "Admin verifies pending payment manually"
      validation: "Confirm verification action"
      api_call: "PUT /api/admin/payments/:id/verify { adminId, verificationNotes }"
      result: "Update payment status to 'paid', trigger application/renewal workflow, show success toast"
      error_handling: "Show error toast if verification fails"
    
    - step: 13
      screen: "notifications_center"
      action: "Admin navigates to 'Notifications Center'"
      validation: "None"
      api_call: "GET /api/admin/notifications?unread=true"
      result: "Display list of system notifications with unread count"
      error_handling: "Loading state, error toast on fetch failure"
    
    - step: 14
      screen: "notifications_center"
      action: "Admin marks notifications as read"
      validation: "None"
      api_call: "PUT /api/admin/notifications/mark-read { notificationIds }"
      result: "Update notification read status, decrease unread count"
      error_handling: "Optimistic UI update, revert on error"
    
    - step: 15
      screen: "notifications_center"
      action: "Admin creates broadcast announcement"
      validation: "Required fields: title, message, priority"
      api_call: "POST /api/admin/notifications/broadcast { title, message, priority, targetUsers }"
      result: "Send broadcast to target users, show success toast"
      error_handling: "Validate form fields, show error toast on API failure"
    
    - step: 16
      screen: "archiving_page"
      action: "Admin navigates to 'Archiving' from sidebar"
      validation: "None"
      api_call: "GET /api/admin/archive"
      result: "Display archived applications in table"
      error_handling: "Loading state, handle empty archive"
    
    - step: 17
      screen: "archiving_page"
      action: "Admin exports archive to CSV"
      validation: "None"
      api_call: "GET /api/admin/archive/export?format=csv&filters={...}"
      result: "Generate CSV file, trigger download"
      error_handling: "Show loading spinner, error toast if export fails"
    
    - step: 18
      screen: "admin_profile"
      action: "Admin navigates to 'Admin Profile' from sidebar"
      validation: "None"
      api_call: "GET /api/admin/profile"
      result: "Display admin profile information"
      error_handling: "Loading skeleton, error toast on fetch failure"
    
    - step: 19
      screen: "admin_profile"
      action: "Admin clicks 'Edit Profile' button"
      validation: "None"
      api_call: "None"
      result: "Enable edit mode for profile fields"
      error_handling: "N/A"
    
    - step: 20
      screen: "admin_profile"
      action: "Admin updates profile information and clicks 'Save Changes'"
      validation: "Required fields: name, email (valid format), phone (valid format)"
      api_call: "PUT /api/admin/profile { name, email, phone }"
      result: "Update profile in database, disable edit mode, show success toast"
      error_handling: "Validate fields on blur and submit, show error toast on API failure"
    
    - step: 21
      screen: "admin_profile"
      action: "Admin clicks 'Change Password'"
      validation: "None"
      api_call: "None"
      result: "Open password change dialog"
      error_handling: "N/A"
    
    - step: 22
      screen: "admin_profile"
      action: "Admin enters current password, new password, and confirms new password"
      validation: "Required: current password, new password (min 8 chars, password strength), confirm password (must match)"
      api_call: "PUT /api/admin/profile/change-password { currentPassword, newPassword }"
      result: "Update password in database (hashed), close dialog, show success toast"
      error_handling: "Validate password strength, match confirmation, show error if current password is incorrect"
    
    - step: 23
      screen: "user_management"
      action: "Admin navigates to 'User Management' from sidebar"
      validation: "None"
      api_call: "GET /api/admin/users?page=1&limit=20&status=all"
      result: "Display list of user accounts in table"
      error_handling: "Loading skeleton, error toast on fetch failure"
    
    - step: 24
      screen: "user_management"
      action: "Admin searches for user by name or email"
      validation: "None"
      api_call: "GET /api/admin/users?search=keyword"
      result: "Filter user list based on search query"
      error_handling: "Show 'No users found' if no matches"
    
    - step: 25
      screen: "user_management"
      action: "Admin clicks 'View Details' for a user"
      validation: "None"
      api_call: "GET /api/admin/users/:id"
      result: "Navigate to user_account_details page"
      error_handling: "Show loading spinner, error toast if fetch fails"
    
    - step: 26
      screen: "user_account_details"
      action: "Admin views comprehensive user details (profile, businesses, applications, payments)"
      validation: "None"
      api_call: "GET /api/admin/users/:id/details"
      result: "Display user profile, linked businesses, application history, payment history"
      error_handling: "Loading states for each section, handle empty states"
    
    - step: 27
      screen: "user_account_details"
      action: "Admin suspends user account"
      validation: "Confirm suspension action, optional reason"
      api_call: "PUT /api/admin/users/:id/suspend { adminId, reason }"
      result: "Update user account status to 'suspended', disable user access, show success toast"
      error_handling: "Show error toast if suspension fails"
    
    - step: 28
      screen: "user_account_details"
      action: "Admin sends direct message to user"
      validation: "Required: subject, message"
      api_call: "POST /api/admin/users/:id/message { adminId, subject, message }"
      result: "Send email notification to user, show success toast"
      error_handling: "Validate form fields, show error toast on send failure"
    
    - step: 29
      screen: "sidebar"
      action: "Admin clicks 'Logout' button in sidebar"
      validation: "Confirm logout (optional)"
      api_call: "POST /api/admin/auth/logout (optional)"
      result: "Clear session/JWT token, redirect to admin_login"
      error_handling: "Always redirect to login even if API call fails"

backend_requirements:
  - screen: "admin_login"
    needed_endpoints:
      - method: "POST"
        endpoint: "/api/admin/auth/login"
        description: "Authenticate admin user with username/email and password"
        request_body: "{ username: string, password: string }"
        response: "{ success: boolean, token: string, admin: { id, name, role, email, permissions } }"
        validation: "Username/email required, password required (min 8 chars), verify against hashed password in database"
        error_codes: "400 (missing fields), 401 (invalid credentials), 500 (server error)"
  
  - screen: "admin_dashboard"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/dashboard/stats"
        description: "Fetch overview statistics for dashboard cards"
        request_body: "none"
        response: "{ pending: number, approved: number, rejected: number, archived: number, pendingRenewals: number }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 500 (server error)"
      - method: "GET"
        endpoint: "/api/admin/dashboard/monthly-data"
        description: "Fetch monthly aggregated data for bar chart"
        request_body: "none"
        response: "{ months: [{ month: string, applications: number, renewals: number, payments: number }] }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 500 (server error)"
      - method: "GET"
        endpoint: "/api/admin/dashboard/recent-activity"
        description: "Fetch recent system activity feed"
        request_body: "none"
        response: "{ activities: [{ action: string, time: string, type: string }] }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 500 (server error)"
  
  - screen: "manage_applications"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/applications"
        description: "Fetch all applications with pagination and filters"
        request_body: "Query params: ?page=1&limit=20&status=pending&search=keyword"
        response: "{ applications: [{ id, businessName, applicant, dateSubmitted, status, businessType, address, contact }], total: number, page: number, limit: number }"
        validation: "Verify JWT token, admin permissions, validate query params"
        error_codes: "401 (unauthorized), 400 (invalid query params), 500 (server error)"
      - method: "GET"
        endpoint: "/api/admin/applications/:id"
        description: "Fetch detailed application data by ID"
        request_body: "none"
        response: "{ application: { id, businessName, applicant, dateSubmitted, status, businessType, address, contact, documents: [] } }"
        validation: "Verify JWT token, admin permissions, validate application ID exists"
        error_codes: "401 (unauthorized), 404 (application not found), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/applications/:id/approve"
        description: "Approve pending application"
        request_body: "{ adminId: string, notes: string }"
        response: "{ success: boolean, message: string, permitId: string }"
        validation: "Verify JWT token, admin permissions, validate application exists and is pending, check all required documents uploaded"
        error_codes: "401 (unauthorized), 404 (not found), 400 (invalid status or missing documents), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/applications/:id/reject"
        description: "Reject pending application with reason"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate application exists and is pending, reason required"
        error_codes: "401 (unauthorized), 404 (not found), 400 (invalid status or missing reason), 500 (server error)"
  
  - screen: "manage_renewals"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/renewals"
        description: "Fetch all renewal applications with filters"
        request_body: "Query params: ?page=1&limit=20&status=pending&paymentStatus=paid&search=keyword"
        response: "{ renewals: [{ id, businessName, applicant, renewalType, dateSubmitted, status, originalPermitId, expiryDate, paymentStatus, renewalFee }], total: number }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 400 (invalid query params), 500 (server error)"
      - method: "GET"
        endpoint: "/api/admin/renewals/:id"
        description: "Fetch detailed renewal data with original permit info"
        request_body: "none"
        response: "{ renewal: { id, businessName, applicant, renewalType, originalPermitId, expiryDate, paymentStatus, renewalFee, documents: [], status } }"
        validation: "Verify JWT token, admin permissions, validate renewal ID exists"
        error_codes: "401 (unauthorized), 404 (renewal not found), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/renewals/:id/approve"
        description: "Approve renewal and generate new permit with extended expiry"
        request_body: "{ adminId: string, newExpiryDate: string, notes: string }"
        response: "{ success: boolean, message: string, newPermitId: string, newExpiryDate: string }"
        validation: "Verify JWT token, admin permissions, validate renewal exists and is pending, check payment status is 'paid', validate new expiry date is in future"
        error_codes: "401 (unauthorized), 404 (not found), 400 (invalid status, payment not verified, or invalid expiry date), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/renewals/:id/reject"
        description: "Reject renewal request with reason"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate renewal exists and is pending, reason required"
        error_codes: "401 (unauthorized), 404 (not found), 400 (invalid status or missing reason), 500 (server error)"
  
  - screen: "payment_oversight"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/payments/applications"
        description: "Fetch all application payments with filters"
        request_body: "Query params: ?status=paid&search=keyword&page=1&limit=20"
        response: "{ payments: [{ id, applicant, businessName, amount, status, paymentDate, applicationId, paymentMethod, referenceNumber }], total: number, totalRevenue: number }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 400 (invalid query params), 500 (server error)"
      - method: "GET"
        endpoint: "/api/admin/payments/renewals"
        description: "Fetch all renewal payments with filters"
        request_body: "Query params: ?status=pending&search=keyword&page=1&limit=20"
        response: "{ payments: [{ id, applicant, businessName, amount, status, paymentDate, applicationId, paymentMethod, referenceNumber }], total: number, totalRevenue: number }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 400 (invalid query params), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/payments/:id/verify"
        description: "Manually verify payment and trigger workflow"
        request_body: "{ adminId: string, verificationNotes: string }"
        response: "{ success: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate payment exists and is pending, trigger application/renewal workflow on verification"
        error_codes: "401 (unauthorized), 404 (payment not found), 400 (payment already verified), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/payments/:id/mark-failed"
        description: "Mark payment as failed with reason"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate payment exists, reason required, send notification to user"
        error_codes: "401 (unauthorized), 404 (payment not found), 400 (missing reason), 500 (server error)"
  
  - screen: "notifications_center"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/notifications"
        description: "Fetch all system notifications with filters"
        request_body: "Query params: ?type=application&priority=high&unread=true&page=1&limit=20"
        response: "{ notifications: [{ id, title, message, type, priority, timestamp, read, targetUser }], unreadCount: number, total: number }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 400 (invalid query params), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/notifications/mark-read"
        description: "Mark multiple notifications as read"
        request_body: "{ notificationIds: string[] }"
        response: "{ success: boolean, markedCount: number }"
        validation: "Verify JWT token, admin permissions, validate notification IDs exist"
        error_codes: "401 (unauthorized), 400 (invalid notification IDs), 500 (server error)"
      - method: "POST"
        endpoint: "/api/admin/notifications/broadcast"
        description: "Create and send broadcast announcement to users"
        request_body: "{ adminId: string, title: string, message: string, priority: string, targetUsers: string[] | 'all', scheduledTime: string }"
        response: "{ success: boolean, broadcastId: string, recipientCount: number }"
        validation: "Verify JWT token, admin permissions, validate required fields (title, message, priority), validate targetUsers exist if specific, schedule or send immediately"
        error_codes: "401 (unauthorized), 400 (missing fields or invalid users), 500 (server error)"
  
  - screen: "archiving_page"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/archive"
        description: "Fetch archived applications with date and type filters"
        request_body: "Query params: ?dateFrom=2024-01-01&dateTo=2024-12-31&businessType=Restaurant&search=keyword&page=1&limit=20"
        response: "{ archivedRecords: [{ id, businessName, applicant, archiveDate, businessType, originalStatus, reason }], total: number }"
        validation: "Verify JWT token, admin permissions, validate date range format"
        error_codes: "401 (unauthorized), 400 (invalid date format), 500 (server error)"
      - method: "GET"
        endpoint: "/api/admin/archive/export"
        description: "Export archived records to CSV or PDF"
        request_body: "Query params: ?format=csv&filters={dateFrom,dateTo,businessType}"
        response: "{ downloadUrl: string, fileSize: number, expiresAt: string }"
        validation: "Verify JWT token, admin permissions, validate export format (csv or pdf), generate file with applied filters"
        error_codes: "401 (unauthorized), 400 (invalid format), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/archive/:id/restore"
        description: "Restore archived record to active status"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string, restoredApplicationId: string }"
        validation: "Verify JWT token, admin permissions, validate archived record exists, reason required, restore to previous status or pending"
        error_codes: "401 (unauthorized), 404 (archived record not found), 400 (missing reason), 500 (server error)"
  
  - screen: "admin_profile"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/profile"
        description: "Fetch current admin profile data"
        request_body: "none"
        response: "{ admin: { id, name, email, phone, position, department, employeeId, dateHired, lastLogin, permissions: [], avatar: string } }"
        validation: "Verify JWT token, extract admin ID from token"
        error_codes: "401 (unauthorized), 404 (admin not found), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/profile"
        description: "Update admin profile information"
        request_body: "{ name: string, email: string, phone: string }"
        response: "{ success: boolean, message: string, updatedAdmin: { name, email, phone } }"
        validation: "Verify JWT token, validate email format, validate phone format, check email uniqueness if changed, send confirmation email on email change"
        error_codes: "401 (unauthorized), 400 (invalid format or duplicate email), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/profile/change-password"
        description: "Change admin password"
        request_body: "{ currentPassword: string, newPassword: string }"
        response: "{ success: boolean, message: string }"
        validation: "Verify JWT token, validate current password against hashed password in DB, validate new password strength (min 8 chars, uppercase, lowercase, number, special char), hash new password with bcrypt, update password in DB, invalidate all existing sessions except current"
        error_codes: "401 (unauthorized or incorrect current password), 400 (weak new password), 500 (server error)"
  
  - screen: "user_management"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/users"
        description: "Fetch all user accounts with pagination and filters"
        request_body: "Query params: ?page=1&limit=20&status=active&search=keyword"
        response: "{ users: [{ id, name, email, businessName, status, joinDate, lastLogin, applicationCount }], total: number, page: number }"
        validation: "Verify JWT token, admin permissions"
        error_codes: "401 (unauthorized), 400 (invalid query params), 500 (server error)"
      - method: "GET"
        endpoint: "/api/admin/users/:id"
        description: "Fetch specific user account details"
        request_body: "none"
        response: "{ user: { id, name, email, phone, status, joinDate, lastLogin, businesses: [], applications: [], payments: [] } }"
        validation: "Verify JWT token, admin permissions, validate user ID exists"
        error_codes: "401 (unauthorized), 404 (user not found), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/users/:id/suspend"
        description: "Suspend user account and revoke access"
        request_body: "{ adminId: string, reason: string }"
        response: "{ success: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate user exists and is active, reason required, invalidate all user sessions, send suspension email to user"
        error_codes: "401 (unauthorized), 404 (user not found), 400 (user already suspended or missing reason), 500 (server error)"
      - method: "PUT"
        endpoint: "/api/admin/users/:id/reactivate"
        description: "Reactivate suspended user account"
        request_body: "{ adminId: string, notes: string }"
        response: "{ success: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate user exists and is suspended, send reactivation email to user"
        error_codes: "401 (unauthorized), 404 (user not found), 400 (user not suspended), 500 (server error)"
      - method: "POST"
        endpoint: "/api/admin/users/:id/reset-password"
        description: "Generate password reset token and send email to user"
        request_body: "{ adminId: string }"
        response: "{ success: boolean, resetTokenSent: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate user exists, generate secure reset token, store token in DB with expiry, send password reset email to user"
        error_codes: "401 (unauthorized), 404 (user not found), 500 (server error or email send failure)"
  
  - screen: "user_account_details"
    needed_endpoints:
      - method: "GET"
        endpoint: "/api/admin/users/:id/details"
        description: "Fetch comprehensive user data including profile, businesses, applications, payments"
        request_body: "none"
        response: "{ user: { id, name, email, phone, status, joinDate }, businesses: [{ id, businessName, type, address }], applications: [{ id, status, dateSubmitted }], payments: [{ id, amount, status, date }] }"
        validation: "Verify JWT token, admin permissions, validate user ID exists"
        error_codes: "401 (unauthorized), 404 (user not found), 500 (server error)"
      - method: "POST"
        endpoint: "/api/admin/users/:id/message"
        description: "Send direct message/email to user"
        request_body: "{ adminId: string, subject: string, message: string }"
        response: "{ success: boolean, messageSent: boolean, message: string }"
        validation: "Verify JWT token, admin permissions, validate user exists, validate required fields (subject, message), send email to user's email address"
        error_codes: "401 (unauthorized), 404 (user not found), 400 (missing fields), 500 (server error or email send failure)"

ux_notes:
  - "Accessibility: All buttons and icons have proper ARIA labels, keyboard navigation supported throughout, screen reader friendly, focus states clearly visible with green ring (#3EB489)"
  - "Loading states: Skeleton loaders for data tables during initial fetch, spinner indicators for buttons during submission, shimmer effect for cards, loading text with animated ellipsis"
  - "Error handling: Toast notifications (Sonner) for all errors with auto-dismiss, inline validation messages for forms, error states for failed API calls with retry buttons, clear error messages in user-friendly language"
  - "Success feedback: Green toast notifications for successful actions (approvals, updates, deletions), visual confirmation with icon animation, status badge color updates, table row updates without full page refresh"
  - "Responsive design: Mobile-first approach with breakpoints at 640px (sm), 768px (md), 1024px (lg). Sidebar collapses to hamburger menu on mobile, data tables scroll horizontally on small screens, cards stack vertically on mobile, flexible grid layouts adjust column count based on viewport"
  - "Form UX: Real-time validation on blur for all input fields, clear error messages below fields, required field indicators with asterisk, password strength meter for password changes, confirm password matching validation, disable submit button until form is valid, clear visual distinction between edit and view modes"
  - "Data table UX: Sortable columns (future enhancement), pagination controls, results count display, empty state with helpful message and call-to-action, loading skeleton for rows, hover states for rows (light green background), sticky header row on scroll, responsive overflow with horizontal scroll"
  - "Filter and search UX: Real-time search with debounce (300ms delay to prevent excessive API calls), clear filter button to reset all filters, filter pills showing active filters, results count updates dynamically, search icon in input field, dropdown filters with clear labels"
  - "Modal dialogs: Backdrop click to close (optional), ESC key to close, focus trap within modal, scroll within modal if content exceeds viewport, clear close button (X icon) in top-right, action buttons (Cancel, Save, Approve, Reject) in footer with proper hierarchy"
  - "Status badges: Color-coded badges matching status (Yellow: pending, Green: approved, Red: rejected, Gray: archived), consistent badge styling throughout app, icon + text for clarity, hover states with tooltip for additional info (future enhancement)"
  - "Charts and visualizations: Interactive tooltips on hover for Recharts, responsive charts that resize with viewport, color consistency with brand palette (greens for positive metrics, yellows for pending, reds for negative), legend for multi-series charts, loading state for chart container"
  - "Navigation: Sidebar with grouped menu items by category (Homepage, Applications, Renewals, Main Menu, Administration), active page highlighted with green accent background (#3EB489), hover states for nav items (lighter green), smooth page transitions, breadcrumbs for deep navigation (future enhancement)"
  - "Sidebar UX: Fixed width (256px) on desktop, collapsible on mobile, white text on green background for high contrast, grouped navigation with separators, logout button in footer with red hover state, official logo and branding at top"
  - "Action buttons: Icon-only buttons for table actions (View, Approve, Reject) to save space, color-coded action buttons (green for approve, red for reject, neutral for view), hover states with background color change, disabled state for unavailable actions, loading state with spinner during API call"
  - "Toast notifications: Positioned at top-right corner, auto-dismiss after 5 seconds, swipe to dismiss on mobile, stacked toasts if multiple, color-coded by type (green: success, red: error, blue: info, yellow: warning), icon + message + close button"
  - "Empty states: Friendly illustrations or icons, clear message explaining why empty, call-to-action button when applicable (e.g., 'No applications found. Check back later.'), helpful tips for users"
  - "Animations: Smooth page transitions with fade-in effect (optional Framer Motion), button click feedback with scale animation, badge color transitions on status change, toast slide-in animation, modal fade-in and scale animation, loading spinner rotation, shimmer effect for skeleton loaders"
  - "Typography: Consistent font sizing throughout (16px base), clear hierarchy with headings (24px, 20px, 18px, 16px), medium weight for headings and labels (500), normal weight for body text (400), line-height 1.5 for readability, monospace font for IDs and reference numbers"
  - "Color usage: Green color scheme (#0B8457 primary, #3EB489 accent) consistently used for primary actions, success states, and branding. Red (#E63946) for destructive actions and errors. Yellow (#FFB703) for warnings and pending states. Gray for neutral and archived states. White backgrounds for cards on light green page background (#F2F7F5)."
  - "Data freshness: Auto-refresh for dashboard stats every 30 seconds (optional), manual refresh button in header, last updated timestamp display (future enhancement), optimistic UI updates for instant feedback, background sync for notifications"
  - "Confirmation dialogs: Destructive actions (delete, reject, suspend) require confirmation dialog, clear explanation of consequences, 'Cancel' and 'Confirm' buttons with proper colors, confirmation checkbox for critical actions (future enhancement)"
  - "Permissions: UI adapts based on admin role and permissions, disabled buttons for actions user doesn't have permission for, tooltip explaining why action is unavailable, graceful degradation if permissions change mid-session"
  - "Performance: Pagination for large datasets (20 items per page default), lazy loading for images and charts, debounced search to reduce API calls, memoized components to prevent unnecessary re-renders, optimistic UI updates for instant feedback, skeleton loaders instead of blank screens"
  - "Security: JWT token stored in httpOnly cookie or secure localStorage, token expiry handling with automatic refresh or re-login, session timeout after 30 minutes of inactivity, CSRF protection for all POST/PUT/DELETE requests, sensitive actions (password change, user suspension) require re-authentication"
