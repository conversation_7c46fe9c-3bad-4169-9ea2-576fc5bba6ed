// Email Service for Smart Governance System
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 19, 2025
// Purpose: Send OTP emails for user verification
// Security: Secure email configuration, input validation
// Standards: Nodemailer best practices

const nodemailer = require('nodemailer');

// Create transporter
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: process.env.SMTP_PORT || 587,
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

// Send OTP email
const sendOTP = async (email, otp) => {
  const mailOptions = {
    from: `"Smart Governance System" <${process.env.SMTP_USER}>`,
    to: email,
    subject: 'Your OTP for Smart Governance Portal',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Smart Governance Portal</h2>
        <p>Dear User,</p>
        <p>Your One-Time Password (OTP) for account verification is:</p>
        <div style="background-color: #f3f4f6; padding: 20px; text-align: center; margin: 20px 0;">
          <h1 style="color: #dc2626; font-size: 32px; margin: 0;">${otp}</h1>
        </div>
        <p>This OTP will expire in 10 minutes.</p>
        <p>If you didn't request this OTP, please ignore this email.</p>
        <br>
        <p>Best regards,<br>Smart Governance Team</p>
      </div>
    `,
  };

  await transporter.sendMail(mailOptions);
};

module.exports = {
  sendOTP,
};
