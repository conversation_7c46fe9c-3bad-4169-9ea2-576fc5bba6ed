# Diagram 1: Entity Relationship Diagram (ERD)

## Overview
This Entity Relationship Diagram shows all 7 PostgreSQL tables in the Smart Governance User Module database, including their columns, data types, primary keys (UUID), foreign key relationships, and CASCADE constraints.

---

## Database: smart_governance_auth
**Database System:** PostgreSQL 16+  
**Primary Key Type:** UUID (UUIDV4)  
**Timestamp Type:** TIMESTAMP WITH TIME ZONE  
**JSON Storage:** JSONB for metadata fields

---

## Mermaid ERD

```mermaid
erDiagram
    CLIENT_ACCOUNTS ||--o{ OTP_VERIFICATION : "has"
    CLIENT_ACCOUNTS ||--o{ BUSINESS_PROFILES : "owns"
    CLIENT_ACCOUNTS ||--o{ NOTIFICATIONS : "receives"
    CLIENT_ACCOUNTS ||--o{ FEEDBACK_SUBMISSIONS : "submits"
    BUSINESS_PROFILES ||--o{ BUSINESS_APPLICATIONS : "creates"
    BUSINESS_APPLICATIONS ||--o{ DOCUMENT_UPLOADS : "contains"
    BUSINESS_APPLICATIONS ||--o{ FEEDBACK_SUBMISSIONS : "receives"

    CLIENTS {
        UUID id PK "Primary Key (UUIDV4)"
        VARCHAR_255 email UK "Unique, NOT NULL"
        VARCHAR_255 password_hash "bcrypt hashed, NOT NULL"
        VARCHAR_255 name "Client organization name, NOT NULL"
        VARCHAR_50 contact_number "Required contact number"
        TEXT address "Optional address"
        JSONB metadata "Flexible JSON storage"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }

    CLIENT_ACCOUNTS {
        UUID id PK "Primary Key (UUIDV4)"
        UUID client_id FK "REFERENCES clients(id) ON DELETE CASCADE"
        VARCHAR_255 email UK "Unique, NOT NULL"
        VARCHAR_255 password_hash "bcrypt hashed, NOT NULL"
        VARCHAR_255 full_name "User's full name"
        VARCHAR_20 contact_number "Phone number"
        BOOLEAN is_verified "Email verified, DEFAULT FALSE"
        JSONB metadata "Flexible JSON storage"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }

    OTP_VERIFICATION {
        UUID id PK "Primary Key (UUIDV4)"
        UUID client_account_id FK "REFERENCES client_accounts(id) ON DELETE CASCADE"
        VARCHAR_6 otp_code "6-digit OTP, NOT NULL"
        ENUM purpose "registration or password_reset"
        TIMESTAMPTZ expires_at "OTP expiry (10 minutes), NOT NULL"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }

    BUSINESS_PROFILES {
        UUID id PK "Primary Key (UUIDV4)"
        UUID client_account_id FK "REFERENCES client_accounts(id) ON DELETE CASCADE"
        VARCHAR_255 business_name "Business name, NOT NULL"
        VARCHAR_100 business_type "Type of business"
        TEXT address "Business address"
        VARCHAR_20 contact_number "Business phone"
        VARCHAR_50 tin_number "Tax Identification Number"
        JSONB metadata "Flexible JSON storage"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }

    BUSINESS_APPLICATIONS {
        UUID id PK "Primary Key (UUIDV4)"
        UUID business_profile_id FK "REFERENCES business_profiles(id) ON DELETE CASCADE"
        ENUM application_type "new or renewal, NOT NULL"
        ENUM status "draft, submitted, under_review, approved, rejected"
        TIMESTAMPTZ submitted_at "Submission timestamp"
        TIMESTAMPTZ reviewed_at "Review timestamp (admin)"
        TEXT notes "Admin notes or rejection reason"
        JSONB metadata "Flexible JSON storage"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }

    DOCUMENT_UPLOADS {
        UUID id PK "Primary Key (UUIDV4)"
        UUID application_id FK "REFERENCES business_applications(id) ON DELETE CASCADE"
        VARCHAR_100 document_type "DTI, Barangay Clearance, etc., NOT NULL"
        VARCHAR_500 file_path "Local file path, NOT NULL"
        VARCHAR_255 file_name "Original filename, NOT NULL"
        INTEGER file_size "File size in bytes, NOT NULL"
        VARCHAR_100 mime_type "MIME type (PDF, JPG, PNG), NOT NULL"
        JSONB metadata "Flexible JSON storage"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }

    NOTIFICATIONS {
        UUID id PK "Primary Key (UUIDV4)"
        UUID client_account_id FK "REFERENCES client_accounts(id) ON DELETE CASCADE"
        ENUM type "info, success, warning, error, DEFAULT info"
        VARCHAR_255 title "Notification title, NOT NULL"
        TEXT message "Notification message, NOT NULL"
        BOOLEAN is_read "Read status, DEFAULT FALSE"
        JSONB metadata "Flexible JSON storage"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }

    FEEDBACK_SUBMISSIONS {
        UUID id PK "Primary Key (UUIDV4)"
        UUID client_account_id FK "REFERENCES client_accounts(id) ON DELETE CASCADE"
        UUID application_id FK "REFERENCES business_applications(id) ON DELETE SET NULL"
        INTEGER rating "1-5 stars, NOT NULL, CHECK (rating >= 1 AND rating <= 5)"
        TEXT comments "Optional feedback comments"
        JSONB metadata "Flexible JSON storage"
        TIMESTAMPTZ created_at "Creation timestamp"
        TIMESTAMPTZ updated_at "Last update timestamp"
    }
```

---

## Table Relationships

### 1. CLIENT_ACCOUNTS (Root Entity)
- **One-to-Many** with OTP_VERIFICATION (1 user → many OTPs)
- **One-to-Many** with BUSINESS_PROFILES (1 user → many businesses)
- **One-to-Many** with NOTIFICATIONS (1 user → many notifications)
- **One-to-Many** with FEEDBACK_SUBMISSIONS (1 user → many feedback)

### 2. BUSINESS_PROFILES
- **Many-to-One** with CLIENT_ACCOUNTS (many businesses → 1 user)
- **One-to-Many** with BUSINESS_APPLICATIONS (1 business → many applications)

### 3. BUSINESS_APPLICATIONS
- **Many-to-One** with BUSINESS_PROFILES (many applications → 1 business)
- **One-to-Many** with DOCUMENT_UPLOADS (1 application → many documents)
- **One-to-Many** with FEEDBACK_SUBMISSIONS (1 application → many feedback)

### 4. DOCUMENT_UPLOADS
- **Many-to-One** with BUSINESS_APPLICATIONS (many documents → 1 application)

### 5. NOTIFICATIONS
- **Many-to-One** with CLIENT_ACCOUNTS (many notifications → 1 user)

### 6. FEEDBACK_SUBMISSIONS
- **Many-to-One** with CLIENT_ACCOUNTS (many feedback → 1 user)
- **Many-to-One** with BUSINESS_APPLICATIONS (many feedback → 1 application, NULLABLE)

### 7. OTP_VERIFICATION
- **Many-to-One** with CLIENT_ACCOUNTS (many OTPs → 1 user)

---

## Foreign Key Constraints

| Child Table | Foreign Key Column | Parent Table | Parent Column | On Delete Action |
|-------------|-------------------|--------------|---------------|------------------|
| client_accounts | client_id | clients | id | CASCADE |
| otp_verification | client_account_id | client_accounts | id | CASCADE |
| business_profiles | client_id | clients | id | CASCADE |
| business_profiles | client_account_id | client_accounts | id | CASCADE |
| notifications | client_account_id | client_accounts | id | CASCADE |
| feedback_submissions | client_account_id | client_accounts | id | CASCADE |
| business_applications | business_profile_id | business_profiles | id | CASCADE |
| document_uploads | application_id | business_applications | id | CASCADE |
| feedback_submissions | application_id | business_applications | id | SET NULL |

---

## PostgreSQL-Specific Features

### UUID Primary Keys
- All tables use `UUID` (UUIDV4) as primary keys
- Generated using `uuid_generate_v4()` function
- Better for distributed systems and security

### ENUM Types (4 total)
1. **otp_verification.purpose:** `'registration'`, `'password_reset'`
2. **business_applications.application_type:** `'new'`, `'renewal'`
3. **business_applications.status:** `'draft'`, `'submitted'`, `'under_review'`, `'approved'`, `'rejected'`
4. **notifications.type:** `'info'`, `'success'`, `'warning'`, `'error'`

### JSONB Metadata
- All tables have `metadata JSONB` column
- Allows flexible, schema-less data storage
- Indexed with GIN for fast queries

### Timestamps with Timezone
- All tables have `created_at` and `updated_at`
- Type: `TIMESTAMP WITH TIME ZONE`
- Automatically managed by Sequelize

---

## Indexes

### client_accounts
- `btree` on `email` (unique constraint)
- `gin` on `metadata`

### otp_verification
- `btree` on `client_account_id`
- `btree` on `expires_at`

### business_profiles
- `btree` on `client_account_id`
- `btree` on `business_name`
- `gin` on `metadata`

### business_applications
- `btree` on `business_profile_id`
- `btree` on `status`
- `btree` on `submitted_at`
- `gin` on `metadata`

### document_uploads
- `btree` on `application_id`
- `btree` on `document_type`
- `gin` on `metadata`

### notifications
- `btree` on `client_account_id`
- `btree` on `is_read`
- `btree` on `created_at`
- `gin` on `metadata`

### feedback_submissions
- `btree` on `client_account_id`
- `btree` on `application_id`
- `btree` on `rating`
- `gin` on `metadata`

---

**Diagram Status:** ✅ Complete  
**Total Tables:** 8  
**Total Relationships:** 9  
**Total Foreign Keys:** 9  
**Last Updated:** November 21, 2025

