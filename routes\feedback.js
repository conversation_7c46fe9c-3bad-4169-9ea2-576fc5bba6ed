// Feedback Routes - Feedback Submission Management API
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define API routes for feedback submission operations

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const {
  submitFeedback,
  getFeedback,
  getFeedbackStats,
} = require('../controllers/feedbackController');

/**
 * @route   POST /api/feedback
 * @desc    Submit feedback
 * @access  Private
 */
router.post('/', authenticateToken, submitFeedback);

/**
 * @route   GET /api/feedback
 * @desc    Get feedback submissions for authenticated user
 * @access  Private
 */
router.get('/', authenticateToken, getFeedback);

/**
 * @route   GET /api/feedback/stats
 * @desc    Get feedback statistics
 * @access  Private
 */
router.get('/stats', authenticateToken, getFeedbackStats);

export default router;

