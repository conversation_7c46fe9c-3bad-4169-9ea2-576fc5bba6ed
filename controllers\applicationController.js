// Application Controller - Business Application Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Handle business application operations (GET, POST, PUT)

const { BusinessApplication, BusinessProfile, DocumentUpload, Notification } = require('../models');

/**
 * Get all applications for authenticated user
 * @route GET /api/applications
 * @access Private
 */
const getApplications = async (req, res) => {
  try {
    // Get all business profiles for the user
    const profiles = await BusinessProfile.findAll({
      where: { client_id: req.user.userId },
      attributes: ['id'],
    });

    const profileIds = profiles.map(p => p.id);

    // Get all applications for these profiles
    const applications = await BusinessApplication.findAll({
      where: { business_profile_id: profileIds },
      include: [
        {
          model: BusinessProfile,
          as: 'businessProfile',
          attributes: ['id', 'business_name', 'business_type'],
        },
        {
          model: DocumentUpload,
          as: 'documents',
          attributes: ['id', 'document_type', 'file_name', 'uploaded_at'],
        },
      ],
      order: [['created_at', 'DESC']],
    });

    res.status(200).json({
      success: true,
      data: applications,
    });
  } catch (error) {
    console.error('Get applications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve applications.',
    });
  }
};

/**
 * Get single application by ID
 * @route GET /api/applications/:id
 * @access Private
 */
const getApplicationById = async (req, res) => {
  try {
    const { id } = req.params;

    const application = await BusinessApplication.findByPk(id, {
      include: [
        {
          model: BusinessProfile,
          as: 'businessProfile',
          where: { client_id: req.user.userId },
        },
        {
          model: DocumentUpload,
          as: 'documents',
        },
      ],
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found.',
      });
    }

    res.status(200).json({
      success: true,
      data: application,
    });
  } catch (error) {
    console.error('Get application error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve application.',
    });
  }
};

/**
 * Create new application
 * @route POST /api/applications
 * @access Private
 */
const createApplication = async (req, res) => {
  try {
    const { business_profile_id, application_type } = req.body;

    // Validation
    if (!business_profile_id) {
      return res.status(400).json({
        success: false,
        message: 'Business profile ID is required.',
      });
    }

    if (!application_type || !['new', 'renewal'].includes(application_type)) {
      return res.status(400).json({
        success: false,
        message: 'Application type must be either "new" or "renewal".',
      });
    }

    // Verify business profile belongs to user
    const profile = await BusinessProfile.findOne({
      where: {
        id: business_profile_id,
        client_id: req.user.userId,
      },
    });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Business profile not found.',
      });
    }

    // Create application
    const application = await BusinessApplication.create({
      business_profile_id: business_profile_id,
      application_type: application_type,
      status: 'draft',
      metadata: {},
    });

    // Create notification
    await Notification.createNotification(
      req.user.userId,
      'Application Created',
      `Your ${application_type} permit application has been created as a draft.`,
      'info',
      { application_id: application.id }
    );

    res.status(201).json({
      success: true,
      message: 'Application created successfully.',
      data: application,
    });
  } catch (error) {
    console.error('Create application error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create application.',
    });
  }
};

/**
 * Submit application for review
 * @route PUT /api/applications/:id/submit
 * @access Private
 */
const submitApplication = async (req, res) => {
  try {
    const { id } = req.params;

    const application = await BusinessApplication.findOne({
      where: { id: id },
      include: [
        {
          model: BusinessProfile,
          as: 'businessProfile',
          where: { client_id: req.user.userId },
        },
        {
          model: DocumentUpload,
          as: 'documents',
        },
      ],
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found.',
      });
    }

    if (application.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Only draft applications can be submitted.',
      });
    }

    // Check if required documents are uploaded (at least one document)
    if (!application.documents || application.documents.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Please upload at least one document before submitting.',
      });
    }

    // Submit application
    await application.update({
      status: 'submitted',
      submitted_at: new Date(),
    });

    // Create notification
    await Notification.createNotification(
      req.user.userId,
      'Application Submitted',
      `Your ${application.application_type} permit application has been submitted for review.`,
      'success',
      { application_id: application.id }
    );

    res.status(200).json({
      success: true,
      message: 'Application submitted successfully.',
      data: application,
    });
  } catch (error) {
    console.error('Submit application error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit application.',
    });
  }
};

/**
 * Update application (draft only)
 * @route PUT /api/applications/:id
 * @access Private
 */
const updateApplication = async (req, res) => {
  try {
    const { id } = req.params;
    const { application_type } = req.body;

    const application = await BusinessApplication.findOne({
      where: { id: id },
      include: [
        {
          model: BusinessProfile,
          as: 'businessProfile',
          where: { client_id: req.user.userId },
        },
      ],
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found.',
      });
    }

    if (application.status !== 'draft') {
      return res.status(400).json({
        success: false,
        message: 'Only draft applications can be updated.',
      });
    }

    // Update application type if provided
    if (application_type && ['new', 'renewal'].includes(application_type)) {
      application.application_type = application_type;
    }

    await application.save();

    res.status(200).json({
      success: true,
      message: 'Application updated successfully.',
      data: application,
    });
  } catch (error) {
    console.error('Update application error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update application.',
    });
  }
};

module.exports = {
  getApplications,
  getApplicationById,
  createApplication,
  submitApplication,
  updateApplication,
};

