# Algorithm 7: Application Status Tracking

## Overview
This algorithm retrieves and displays business permit application status information, including filtering, sorting, and detailed status history for user tracking.

---

## Algorithm Complexity
- **Time Complexity:** O(n) where n = number of applications (with JOIN operations)
- **Space Complexity:** O(n) for storing application list
- **Database Operations:** 1-2 SELECT queries with JOINs

---

## Input Parameters
```typescript
interface StatusTrackingInput {
  client_account_id: UUID;       // From JWT token
  status_filter?: string;        // Optional: 'draft', 'submitted', 'approved', 'rejected'
  application_id?: UUID;         // Optional: for single application details
}
```

## Output
```typescript
interface StatusTrackingOutput {
  success: boolean;
  applications: Array<{
    id: UUID;
    business_name: string;
    application_type: string;
    status: string;
    created_at: Date;
    submitted_at?: Date;
    reviewed_at?: Date;
    notes?: string;
  }>;
}
```

---

## Pseudocode

```
ALGORITHM GetApplicationStatus(client_account_id, status_filter, application_id)
BEGIN
  // Step 1: Build Base Query
  query ← "SELECT a.*, bp.business_name, bp.business_type
           FROM business_applications a
           JOIN business_profiles bp ON a.business_profile_id = bp.id
           WHERE bp.client_account_id = ?"
  
  params ← [client_account_id]
  
  // Step 2: Apply Status Filter (if provided)
  IF status_filter is NOT empty THEN
    IF status_filter NOT IN ['draft', 'submitted', 'under_review', 'approved', 'rejected'] THEN
      RETURN error("Invalid status filter")
    END IF
    
    query ← query + " AND a.status = ?"
    params.push(status_filter)
  END IF
  
  // Step 3: Apply Application ID Filter (if provided)
  IF application_id is NOT empty THEN
    query ← query + " AND a.id = ?"
    params.push(application_id)
  END IF
  
  // Step 4: Add Sorting
  query ← query + " ORDER BY a.created_at DESC"
  
  // Step 5: Execute Query
  applications ← DATABASE.query(query, params)
  
  // Step 6: Enrich with Document Count
  FOR EACH application IN applications DO
    document_count ← DATABASE.count(
      "SELECT COUNT(*) FROM document_uploads WHERE application_id = ?",
      application.id
    )
    application.document_count ← document_count
  END FOR
  
  // Step 7: Format Status Information
  FOR EACH application IN applications DO
    application.status_info ← GET_STATUS_INFO(application.status)
    application.timeline ← BUILD_TIMELINE(application)
  END FOR
  
  // Step 8: Return Results
  RETURN success({
    applications: applications,
    total_count: applications.length
  })
END

FUNCTION GET_STATUS_INFO(status)
BEGIN
  status_map ← {
    'draft': {
      label: 'Draft',
      color: 'gray',
      icon: 'Clock',
      description: 'Application in progress'
    },
    'submitted': {
      label: 'Submitted',
      color: 'blue',
      icon: 'AlertCircle',
      description: 'Awaiting review'
    },
    'under_review': {
      label: 'Under Review',
      color: 'yellow',
      icon: 'AlertCircle',
      description: 'Being reviewed by admin'
    },
    'approved': {
      label: 'Approved',
      color: 'green',
      icon: 'CheckCircle',
      description: 'Application approved'
    },
    'rejected': {
      label: 'Rejected',
      color: 'red',
      icon: 'XCircle',
      description: 'Application rejected'
    }
  }
  
  RETURN status_map[status]
END

FUNCTION BUILD_TIMELINE(application)
BEGIN
  timeline ← []
  
  // Created
  timeline.push({
    event: 'Application Created',
    timestamp: application.created_at,
    status: 'completed'
  })
  
  // Submitted
  IF application.submitted_at is NOT NULL THEN
    timeline.push({
      event: 'Application Submitted',
      timestamp: application.submitted_at,
      status: 'completed'
    })
  END IF
  
  // Reviewed
  IF application.reviewed_at is NOT NULL THEN
    timeline.push({
      event: 'Application Reviewed',
      timestamp: application.reviewed_at,
      status: 'completed'
    })
  END IF
  
  // Final Status
  IF application.status = 'approved' THEN
    timeline.push({
      event: 'Permit Approved',
      timestamp: application.reviewed_at,
      status: 'completed'
    })
  ELSE IF application.status = 'rejected' THEN
    timeline.push({
      event: 'Application Rejected',
      timestamp: application.reviewed_at,
      status: 'completed',
      notes: application.notes
    })
  END IF
  
  RETURN timeline
END
```

---

## Flowchart

```mermaid
flowchart TD
    Start([Start: Get Application Status]) --> Auth{User<br/>Authenticated?}
    Auth -->|No| ErrorAuth[Return Error 401]
    Auth -->|Yes| BuildQuery[Build base query:<br/>JOIN applications with<br/>business profiles]
    BuildQuery --> HasStatusFilter{Status Filter<br/>Provided?}
    HasStatusFilter -->|Yes| ValidateStatus{Valid<br/>Status?}
    ValidateStatus -->|No| ErrorStatus[Return Error:<br/>Invalid status]
    ValidateStatus -->|Yes| AddStatusFilter[Add WHERE clause:<br/>status = ?]
    HasStatusFilter -->|No| HasIDFilter{Application ID<br/>Provided?}
    AddStatusFilter --> HasIDFilter
    HasIDFilter -->|Yes| AddIDFilter[Add WHERE clause:<br/>id = ?]
    HasIDFilter -->|No| AddSorting
    AddIDFilter --> AddSorting[Add ORDER BY:<br/>created_at DESC]
    AddSorting --> ExecuteQuery[Execute database query]
    ExecuteQuery --> EnrichData[For each application:<br/>Count documents]
    EnrichData --> FormatStatus[For each application:<br/>Get status info + timeline]
    FormatStatus --> Success[Return Success:<br/>Applications list]
    Success --> End([End])
    ErrorAuth --> End
    ErrorStatus --> End
```

---

## Status Badge Colors

| Status | Color | Icon | Description |
|--------|-------|------|-------------|
| draft | Gray | Clock | Application in progress, documents being uploaded |
| submitted | Blue | AlertCircle | Submitted for review, awaiting admin action |
| under_review | Yellow | AlertCircle | Being reviewed by admin (admin feature) |
| approved | Green | CheckCircle | Application approved, permit issued |
| rejected | Red | XCircle | Application rejected, see notes for reason |

---

## Timeline Events

### Event Sequence
1. **Application Created** - `created_at` timestamp
2. **Documents Uploaded** - Count of uploaded documents
3. **Application Submitted** - `submitted_at` timestamp
4. **Under Review** - Admin started review (admin feature)
5. **Application Reviewed** - `reviewed_at` timestamp
6. **Final Decision** - Approved or Rejected with notes

---

## Database Tables Involved

### business_applications
- **Operations:** SELECT
- **Fields:** id, business_profile_id, application_type, status, created_at, submitted_at, reviewed_at, notes

### business_profiles
- **Operations:** SELECT (JOIN)
- **Fields:** id, client_account_id, business_name, business_type

### document_uploads
- **Operations:** COUNT
- **Fields:** application_id

---

## Frontend Display

### Application Card
```typescript
<Card>
  <Badge variant={status}>{statusLabel}</Badge>
  <h3>{business_name}</h3>
  <p>Type: {application_type}</p>
  <p>Created: {formatDate(created_at)}</p>
  {submitted_at && <p>Submitted: {formatDate(submitted_at)}</p>}
  {reviewed_at && <p>Reviewed: {formatDate(reviewed_at)}</p>}
  {notes && <p>Notes: {notes}</p>}
  <p>Documents: {document_count}</p>
</Card>
```

### Status Icons
- **Clock:** Draft status
- **AlertCircle:** Submitted/Under Review
- **CheckCircle:** Approved
- **XCircle:** Rejected

---

## Filtering Options

### By Status
- All Applications
- Draft Only
- Submitted Only
- Under Review Only
- Approved Only
- Rejected Only

### By Date
- Newest First (default)
- Oldest First
- Recently Submitted
- Recently Reviewed

---

## Implementation Files

- **Controller:** `controllers/applicationController.js` - `getApplications()`, `getApplicationById()`
- **Model:** `models/BusinessApplication.js`, `models/BusinessProfile.js`
- **Routes:** `routes/applications.js` - `GET /api/applications`, `GET /api/applications/:id`
- **Frontend:** `src/pages/ApplicationStatusPage.tsx`
- **Service:** `src/services/applicationService.ts`
- **Components:** `src/components/ui/Badge.tsx`

---

## Real-time Updates (Future Enhancement)

### WebSocket Integration
```
ON application_status_change(application_id, new_status)
BEGIN
  EMIT to client_account_id: {
    event: 'status_update',
    application_id: application_id,
    new_status: new_status,
    timestamp: CURRENT_TIMESTAMP
  }
END
```

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

