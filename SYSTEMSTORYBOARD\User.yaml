storyboard:
  system_name: "Smart Governance: Business Permit Processing and Renewal System"
  version: "Complete System Storyboard - LGU General Santos City"
  generated_date: "2025-01-XX"
  coverage: "USER perspective (CLIENT-side implementation)"
  
  note: |
    This storyboard represents the COMPLETE USER-SIDE implementation currently in the codebase.
    ADMIN functionality is NOT YET IMPLEMENTED in this version.
    The storyboard is based on actual code analysis of all components, UI flows, state management,
    and interactions present in the system.

  # ═══════════════════════════════════════════════════════════════════════════
  # USER STORYBOARD - Complete Client-Side Journey
  # ═══════════════════════════════════════════════════════════════════════════

  user_scenes:
    # ─────────────────────────────────────────────────────────────────────────
    # AUTHENTICATION & ONBOARDING
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-001"
      title: "Pre-Login Landing Page"
      description: |
        User visits the Smart Governance system and sees the login/registration page.
        The page displays LGU General Santos City branding with blue (#1E40AF) and green (#059669) colors.
      visuals:
        - "LGU logo (blue circular badge with 'LGU' text)"
        - "Header: 'General Santos City' - 'Smart Governance Portal'"
        - "Subtitle: 'Business Permit Processing & Renewal System'"
        - "Card with tabs: Login and Register"
        - "Footer with contact information and copyright"
      actions:
        - "User can switch between Login and Register tabs"
        - "User can enter email and password in Login tab"
        - "User can create account in Register tab with email, password, and confirm password"
        - "User clicks 'Sign In' (blue button) or 'Create Account' (green button)"
      system_response:
        - "Form validation: checks for valid email format"
        - "Login: checks if email and password are filled"
        - "Register: validates password match, minimum length requirements"
        - "On success: sets isLoggedIn state to true"
        - "On success: stores user email in state"
        - "On success: redirects to Dashboard"
        - "On success: initializes notification system with sample notifications"
      files_involved:
        - "App.tsx (main state management)"
        - "components/LoginPage.tsx"
      state_changes:
        - "isLoggedIn: false → true"
        - "userEmail: '' → user's email"
        - "currentPage: 'dashboard' → 'dashboard'"
        - "notifications: [] → initial notification array"

    - id: "USER-002"
      title: "OTP Verification (Conceptual - Not Implemented)"
      description: |
        NOTE: This flow is described in the original storyboard but NOT IMPLEMENTED in current code.
        In the current system, registration happens immediately without OTP verification.
      visuals: []
      actions: []
      system_response:
        - "NOT IMPLEMENTED - Direct login after registration"
      implementation_status: "PLANNED_NOT_IMPLEMENTED"

    # ─────────────────────────────────────────────────────────────────────────
    # MAIN DASHBOARD & NAVIGATION
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-003"
      title: "User Dashboard - Main View"
      description: |
        After successful login, user sees personalized dashboard with application summaries,
        quick actions, and renewal notices.
      visuals:
        - "Top Navigation Bar:"
          - "LGU logo and 'Smart Governance' branding"
          - "Navigation items: Home, Applications, Renewals, Track Status, Feedback"
          - "Notification bell icon with unread count badge (red)"
          - "User profile dropdown (desktop) or profile button (mobile)"
        - "Welcome message: 'Welcome back, {userName}!'"
        - "Renewal Notice card (amber/yellow) if permit expiring soon"
        - "Quick Actions section with 3 buttons:"
          - "Submit New Application (blue)"
          - "Renew Permit (green)"
          - "Track Application Status (outline)"
        - "Application Status Overview - 6 status cards:"
          - "New Applications (blue, FileText icon)"
          - "Renewals Pending (purple, RefreshCw icon)"
          - "Under Review (yellow, Clock icon)"
          - "Payment Pending (orange, DollarSign icon)"
          - "Approved (green, CheckCircle icon)"
          - "Archived (gray, Archive icon)"
      actions:
        - "Click 'Submit New Application' → navigates to 4-step application form"
        - "Click 'Renew Permit' → navigates to 4-step renewal form"
        - "Click 'Track Application Status' → navigates to status tracking page"
        - "Click notification bell → opens notification panel"
        - "Click navigation items → switches page view"
        - "Click user profile → opens dropdown menu (desktop)"
        - "Click on status cards → visual feedback (hover effects)"
      system_response:
        - "Updates currentPage state based on navigation"
        - "Renders appropriate component based on currentPage"
        - "Notification panel toggles isNotificationPanelOpen state"
        - "Real-time calculation of unread notification count"
        - "Mobile responsive: switches to bottom navigation on small screens"
      files_involved:
        - "components/Dashboard.tsx"
        - "components/Navigation.tsx"
        - "App.tsx (page routing)"
      state_changes:
        - "currentPage: changes based on user action"
        - "isNotificationPanelOpen: toggles on bell click"

    - id: "USER-004"
      title: "Navigation Bar - Desktop & Mobile"
      description: |
        Persistent navigation system with responsive design for desktop and mobile views.
      visuals:
        - "Desktop Navigation (top bar):"
          - "Logo + title on left"
          - "Horizontal menu items in center"
          - "Notification bell + user menu on right"
        - "Mobile Navigation:"
          - "Logo + title on top"
          - "Bottom navigation bar with 6 icons"
          - "Notification bell in top bar"
          - "Simplified logout button"
        - "User dropdown menu (desktop):"
          - "My Profile"
          - "Permit History"
          - "Logout (red text)"
      actions:
        - "Click navigation items → page switches"
        - "Click notification bell → opens panel (desktop dropdown, mobile bottom sheet)"
        - "Click user dropdown → shows menu options"
        - "Click 'My Profile' → navigates to profile page"
        - "Click 'Permit History' → navigates to history page"
        - "Click 'Logout' → logs user out"
      system_response:
        - "Page navigation updates currentPage state"
        - "Notification panel opens with animation (desktop: dropdown, mobile: bottom sheet)"
        - "Logout: resets all state (isLoggedIn, userEmail, notifications, currentPage)"
        - "Logout: returns to login page"
        - "Active page highlighted in navigation"
      files_involved:
        - "components/Navigation.tsx"
        - "components/NotificationPanel.tsx"
      state_changes:
        - "On logout: isLoggedIn: true → false, userEmail cleared, notifications cleared"

    # ─────────────────────────────────────────────────────────────────────────
    # NEW APPLICATION SUBMISSION FLOW (4 STEPS)
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-005"
      title: "Application Step 1 - Business Information"
      description: |
        User fills out comprehensive business details in a guided multi-step form.
        This is the first of 4 steps in the application process.
      visuals:
        - "Page header: 'Submit New Application'"
        - "Step progress indicator showing 4 steps (Step 1 highlighted)"
        - "Progress bar showing 25% completion"
        - "Step title: 'Business Information'"
        - "Form fields:"
          - "Business Name* (text input)"
          - "Business Type* (dropdown select)"
          - "Business Address* (textarea)"
          - "Contact Number* (tel input)"
          - "Business Description (textarea, optional)"
        - "Navigation: Previous (disabled) and Next buttons"
      actions:
        - "User types business name"
        - "User selects from business type dropdown (10 options: Retail, Restaurant, Professional Services, etc.)"
        - "User enters complete business address"
        - "User enters contact number in format +63 XXX XXX XXXX"
        - "User optionally adds business description"
        - "User clicks 'Next' to proceed"
      system_response:
        - "Real-time form validation on input"
        - "Next button disabled until all required fields filled"
        - "Updates formData state with user inputs"
        - "On Next: advances to Step 2 (currentStep: 1 → 2)"
        - "Progress bar updates to 50%"
        - "Form data persists across steps"
      files_involved:
        - "components/SubmitApplicationForm.tsx"
      state_changes:
        - "formData: updates with business information"
        - "currentStep: 1 → 2"
      validation_rules:
        - "Business Name: required, non-empty"
        - "Business Type: required, must select from dropdown"
        - "Address: required, non-empty"
        - "Contact Number: required, non-empty"

    - id: "USER-006"
      title: "Application Step 2 - Upload Required Documents"
      description: |
        User uploads all required documents with real-time upload progress tracking.
        Advanced document management with validation and progress indicators.
      visuals:
        - "Step progress indicator (Step 2 highlighted)"
        - "Progress bar showing 50% completion"
        - "Header: 'Upload Required Documents'"
        - "Upload Progress Summary card (blue/green gradient):"
          - "Shows X/6 documents completed"
          - "Progress bar for overall completion"
        - "Document upload fields (6 required documents):"
          - "Business Permit Form"
          - "Barangay Clearance"
          - "DTI/SEC Registration"
          - "Fire Safety Certificate"
          - "Zoning Clearance"
          - "Sanitary Permit"
        - "Each document field shows:"
          - "Document name and description"
          - "Info tooltip with requirements"
          - "Upload button or file preview"
          - "Status badge (missing/uploading/uploaded/error)"
          - "Upload progress bar (when uploading)"
          - "Remove button (when uploaded)"
        - "Important notes alert (blue)"
        - "Action required alert (yellow) if documents incomplete"
      actions:
        - "Click 'Choose File' for each document"
        - "Select file from file picker (PDF, JPG, PNG)"
        - "Click info icon to see document requirements tooltip"
        - "Click 'X' to remove uploaded file"
        - "Click 'Previous' to go back to Step 1"
        - "Click 'Next' to proceed (enabled only when all 6 documents uploaded)"
      system_response:
        - "File validation:"
          - "Checks file type (PDF, JPG, PNG only)"
          - "Checks file size (max 5MB)"
          - "Shows error message if validation fails"
        - "Upload simulation:"
          - "Sets status to 'uploading'"
          - "Animates progress bar 0% → 100%"
          - "Updates status to 'uploaded' on completion"
        - "Stores file reference in documents state"
        - "Real-time progress calculation (X/6 completed)"
        - "Next button disabled until all documents status = 'uploaded'"
        - "On Next: advances to Step 3"
      files_involved:
        - "components/SubmitApplicationForm.tsx"
      state_changes:
        - "documents state: updates for each file upload"
        - "Each document has: { file, status, uploadProgress, errorMessage }"
        - "currentStep: 2 → 3 (when all uploaded and Next clicked)"
      validation_rules:
        - "File type: must be PDF, JPG, or PNG"
        - "File size: maximum 5MB per file"
        - "All 6 documents must have status 'uploaded'"

    - id: "USER-007"
      title: "Application Step 3 - Fee Summary & Payment"
      description: |
        System calculates fees, displays breakdown, and processes GCash payment via PayMongo integration mock.
      visuals:
        - "Step progress indicator (Step 3 highlighted)"
        - "Progress bar showing 75% completion"
        - "Payment Summary card (gray background):"
          - "Application Fee: ₱1,500.00"
          - "Service Charge: ₱50.00"
          - "Total Amount: ₱1,550.00 (blue, bold)"
        - "GCash Payment section:"
          - "GCash icon (green)"
          - "Mobile number input field"
          - "'Pay Now' button (green) with total amount"
        - "Payment status screens:"
          - "Processing: Loader animation + 'Processing Payment...'"
          - "Success: Green checkmark + reference number display"
          - "Failed: Red X + 'Try Again' button"
        - "Demo notice (blue alert)"
      actions:
        - "User enters GCash mobile number (11 digits)"
        - "User clicks 'Pay Now - ₱1,550.00'"
        - "On payment failure: click 'Try Again' to reset form"
        - "Click 'Previous' to go back to Step 2"
        - "Click 'Next' after successful payment to proceed to Step 4"
      system_response:
        - "Input validation: enables Pay button only when number is 11+ digits"
        - "On Pay Now click:"
          - "Sets paymentStatus: '' → 'processing'"
          - "Shows loading animation"
          - "Simulates 2-second API call delay"
          - "90% success rate (random)"
          - "On success: generates reference number (GC + timestamp)"
          - "On success: sets paymentStatus to 'success'"
          - "On failure: sets paymentStatus to 'failed'"
        - "Next button enabled only when paymentStatus === 'success'"
        - "On Next: advances to Step 4 (confirmation)"
      files_involved:
        - "components/SubmitApplicationForm.tsx"
      state_changes:
        - "paymentData.gcashNumber: user input"
        - "paymentData.paymentStatus: '' → 'processing' → 'success'/'failed'"
        - "paymentData.referenceNumber: generated on success"
        - "currentStep: 3 → 4 (after successful payment and Next click)"
      mock_api:
        - "Payment processing: 2-second delay simulation"
        - "Reference number format: GC{8-digit timestamp}"

    - id: "USER-008"
      title: "Application Step 4 - Submission Confirmation"
      description: |
        Application successfully submitted. User sees confirmation with summary and next steps.
      visuals:
        - "Step progress indicator (Step 4 highlighted)"
        - "Progress bar showing 100% completion"
        - "Success message:"
          - "Large green checkmark icon"
          - "Heading: 'Application Submitted Successfully!'"
          - "Subtext about application being in the system"
        - "Application Summary card (gray background):"
          - "Application ID: BP2024-XXX (generated)"
          - "Status: Submitted (blue badge)"
          - "Business Name"
          - "Business Type"
          - "Submitted Date"
        - "Payment Receipt card:"
          - "Payment Method: GCash"
          - "Reference Number"
          - "Amount Paid: ₱1,550.00"
          - "Payment Date"
        - "What's Next? section (green card):"
          - "Review within 5-7 business days"
          - "Email updates on status"
          - "Track in 'Track Status' section"
          - "Keep reference number"
        - "Action buttons:"
          - "'Track Application Status' (blue)"
          - "'Submit Another Application' (outline)"
          - "'Return to Dashboard' (outline)"
      actions:
        - "Click 'Track Application Status' → navigates to Track Status page"
        - "Click 'Submit Another Application' → resets form to Step 1"
        - "Click 'Return to Dashboard' → navigates to dashboard"
      system_response:
        - "Calls onSubmit callback (adds notification)"
        - "Generates application ID: BP2024-{last 3 digits of timestamp}"
        - "Creates new notification:"
          - "Type: success"
          - "Message: 'Your application has been submitted successfully'"
          - "Time: 'Just now'"
          - "isRead: false"
        - "Adds notification to notifications array (prepends to list)"
        - "On dashboard navigation: calls onNavigate('dashboard')"
        - "Form state persists until user leaves or submits another"
      files_involved:
        - "components/SubmitApplicationForm.tsx"
        - "App.tsx (notification management)"
      state_changes:
        - "notifications: new notification added to beginning of array"
        - "currentPage: changes based on action button clicked"

    # ─────────────────────────────────────────────────────────────────────────
    # PERMIT RENEWAL FLOW (4 STEPS)
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-009"
      title: "Renewal Step 1 - Pre-filled Renewal Information"
      description: |
        User starts permit renewal process. Existing permit data is pre-filled.
        System checks for late renewal and calculates penalties.
      visuals:
        - "Page header: 'Renew Business Permit'"
        - "Current Permit Information card (blue background):"
          - "Permit Number: BP2024-001"
          - "Business Name: Sunrise Bakery"
          - "Expiry Date: 2025-03-15 (red if expired)"
        - "Step progress indicator (Step 1 highlighted)"
        - "Progress bar showing 25%"
        - "Form fields:"
          - "Business Name (disabled, pre-filled, gray)"
          - "Renewal Type* (dropdown)"
          - "Business Address* (pre-filled, editable)"
          - "Contact Number* (pre-filled, editable)"
        - "Late Renewal Notice (red alert) if expired:"
          - "Shows expiry date"
          - "Shows penalty fee: ₱300.00"
      actions:
        - "User selects renewal type from dropdown (Annual, Quarterly, Emergency, etc.)"
        - "User updates address if needed"
        - "User updates contact number if needed"
        - "User clicks 'Next' to proceed"
      system_response:
        - "Loads existing permit data from mock state"
        - "Pre-fills: businessName, permitNumber, expiryDate, address, contactNumber"
        - "Calculates if late: compares current date with expiryDate"
        - "If late: calculatedPenalty = ₱300, displays red warning"
        - "If on-time: calculatedPenalty = ₱0, no warning"
        - "Business name field is disabled (cannot change during renewal)"
        - "Next button enabled when renewalType, address, contactNumber all filled"
        - "On Next: advances to Step 2"
      files_involved:
        - "components/RenewalForm.tsx"
      state_changes:
        - "formData: pre-populated with existing permit data"
        - "formData.renewalType: updated when user selects"
        - "calculatedPenalty: computed based on expiry date"
        - "currentStep: 1 → 2"

    - id: "USER-010"
      title: "Renewal Step 2 - Upload Updated Documents"
      description: |
        User uploads updated renewal documents. System allows proceeding without uploads for demo.
      visuals:
        - "Step progress indicator (Step 2 highlighted)"
        - "Progress bar showing 50%"
        - "Upload area (dashed border):"
          - "Upload icon (gray)"
          - "Heading: 'Upload Renewal Documents'"
          - "Description text"
          - "'Choose Files' button"
        - "Uploaded files list (if files selected)"
        - "Required Documents info card (blue):"
          - "Updated Barangay Business Clearance"
          - "Updated Fire Safety Inspection Certificate"
          - "Updated Sanitary Permit"
          - "Proof of Tax Payment (BIR Form 2303)"
          - "Updated business registration documents"
        - "Note card (yellow): Can proceed without documents, submit later"
      actions:
        - "Click 'Choose Files' → opens file picker"
        - "Select multiple files (PDF, JPG, PNG)"
        - "Files appear in uploaded list with FileText icons"
        - "Click 'Previous' to go back"
        - "Click 'Next' to proceed (enabled even without uploads)"
      system_response:
        - "Stores selected files in formData.documents as File[] array"
        - "Displays file names and count"
        - "Next button always enabled (documents optional for demo)"
        - "On Next: advances to Step 3"
      files_involved:
        - "components/RenewalForm.tsx"
      state_changes:
        - "formData.documents: null → File[] array when files selected"
        - "currentStep: 2 → 3"

    - id: "USER-011"
      title: "Renewal Step 3 - Renewal Payment with Penalties"
      description: |
        System calculates renewal fee including late penalties. User pays via GCash.
      visuals:
        - "Step progress indicator (Step 3 highlighted)"
        - "Progress bar showing 75%"
        - "Renewal Payment Summary card:"
          - "Renewal Fee: ₱1,200.00"
          - "Late Renewal Penalty: ₱300.00 (red, if applicable)"
          - "Service Charge: ₱30.00"
          - "Total Amount: ₱1,530.00 (blue, bold)"
        - "GCash Payment section (identical to application payment)"
        - "Payment states: processing/success/failed"
        - "Demo notice (blue)"
      actions:
        - "Enter GCash number"
        - "Click 'Pay Now - ₱1,530.00'"
        - "Handle success/failure states"
      system_response:
        - "Calculates total: renewalFee (1200) + penalty (300 if late) + service (30)"
        - "Payment processing: 2-second simulation, 95% success rate"
        - "Generates reference: RN{8-digit timestamp}"
        - "Same payment flow as application"
        - "Next enabled only after successful payment"
      files_involved:
        - "components/RenewalForm.tsx"
      state_changes:
        - "paymentData.gcashNumber: user input"
        - "paymentData.paymentStatus: '' → 'processing' → 'success'/'failed'"
        - "paymentData.referenceNumber: generated"
        - "currentStep: 3 → 4"

    - id: "USER-012"
      title: "Renewal Step 4 - Renewal Confirmation"
      description: |
        Renewal successfully submitted. Confirmation with summary and next steps.
      visuals:
        - "Step progress indicator (Step 4 highlighted)"
        - "Progress bar showing 100%"
        - "Success message: 'Renewal Submitted Successfully!'"
        - "Renewal Summary card:"
          - "Renewal ID: RN2025-XXX"
          - "Status: Submitted (blue)"
          - "Original Permit: BP2024-001"
          - "Renewal Type"
          - "Submitted Date"
        - "Payment Receipt card (same structure as application)"
        - "What's Next? (green card):"
          - "Review within 3-5 business days"
          - "Email updates"
          - "Track in Track Status"
          - "New permit downloadable once approved"
        - "Action buttons:"
          - "'Track Renewal Status' (blue)"
          - "'Return to Dashboard' (outline)"
      actions:
        - "Click 'Track Renewal Status' → Track Status page"
        - "Click 'Return to Dashboard' → Dashboard"
      system_response:
        - "Generates renewal ID: RN2025-{timestamp}"
        - "No notification added (can be enhanced)"
        - "Navigation to selected page"
      files_involved:
        - "components/RenewalForm.tsx"
      state_changes:
        - "currentPage: changes based on button clicked"

    # ─────────────────────────────────────────────────────────────────────────
    # APPLICATION TRACKING & MONITORING
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-013"
      title: "Track Application Status Page"
      description: |
        User monitors application progress with visual timeline and status tracking.
      visuals:
        - "Page header: 'Track Application Status'"
        - "Search Applications card:"
          - "Search input: 'Enter application ID or business name'"
          - "Search button (blue, magnifying glass icon)"
        - "Applications list (cards for each application):"
          - "Card header (gray background):"
            - "Business name + Application ID badge"
            - "Application type + submitted date"
            - "Status badge with icon (colored)"
          - "Application Progress section:"
            - "Visual timeline with 5 steps:"
              - "1. Submitted (green check if completed)"
              - "2. Under Review (yellow clock if active)"
              - "3. Payment Pending (orange $ if active)"
              - "4. Approved (green check if completed)"
              - "5. Archived (gray if completed)"
            - "Each step shows: name, description, completion indicator"
            - "Vertical connecting lines (green if completed, gray if pending)"
          - "Additional Information (3 columns):"
            - "Estimated Completion date"
            - "Last Updated date"
            - "Progress (X/5 steps)"
        - "No results message if search yields nothing"
      actions:
        - "Enter search term in search box"
        - "Click search button"
        - "View application progress in timeline"
        - "Read status descriptions"
      system_response:
        - "Real-time search filtering by ID or business name (case-insensitive)"
        - "Displays mock applications (3 default):"
          - "BP2025-001: Sunrise Bakery (approved)"
          - "BP2025-002: Tech Solutions Inc. (payment-pending)"
          - "BP2025-003: Green Garden Restaurant (under-review)"
        - "Timeline dynamically shows progress based on status"
        - "Status icons and colors match current state"
      files_involved:
        - "components/TrackStatusPage.tsx"
      state_changes:
        - "searchTerm: updates on input change"
        - "filteredApplications: computed from search term"

    # ─────────────────────────────────────────────────────────────────────────
    # PERMIT HISTORY & RECORDS
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-014"
      title: "Permit History - Complete Records View"
      description: |
        Comprehensive view of all applications and renewals with advanced filtering,
        search, and pagination.
      visuals:
        - "Page header: 'Permit History'"
        - "Summary cards (4 cards):"
          - "Total Applications: 23"
          - "Approved: 8"
          - "Pending: 2"
          - "Rejected: 1"
        - "Filter & Search card:"
          - "Search field (with magnifying glass icon)"
          - "Type dropdown (All / New Applications / Renewals)"
          - "Status dropdown (6 status options)"
          - "Year dropdown (All / 2025 / 2024 / 2023)"
          - "'Clear Filters' button"
        - "Application History table:"
          - "Columns: ID, Type, Business Name, Date Submitted, Status, Amount, Payment Ref, Permit #, Actions"
          - "Type icons: Building2 (blue) for applications, RefreshCw (purple) for renewals"
          - "Status badges (colored)"
          - "Action buttons:"
            - "'View' (eye icon) for all records"
            - "'Download' (download icon, green) for approved permits"
        - "Pagination controls:"
          - "Shows 'X to Y of Z entries'"
          - "Previous/Next buttons"
          - "Page number buttons"
        - "No results message if filters yield nothing"
      actions:
        - "Enter search term"
        - "Select filters from dropdowns"
        - "Click 'Clear Filters' to reset all"
        - "Click 'View' button → navigate to Application Details page"
        - "Click 'Download' button → download permit (simulated)"
        - "Click pagination buttons"
      system_response:
        - "Real-time filtering: combines search + type + status + year filters"
        - "Displays 8 mock records (mix of applications and renewals from 2023-2025)"
        - "Pagination: 10 items per page"
        - "Download simulation: 2-second delay, console log"
        - "On View click: navigates to application-details page with record data"
        - "Filter combinations update filteredRecords array"
      files_involved:
        - "components/PermitHistory.tsx"
      state_changes:
        - "searchTerm, statusFilter, yearFilter, typeFilter: update on user selection"
        - "currentPage: updates on pagination button clicks"
        - "selectedApplicationData: set on View click before navigation"
        - "currentPage (routing): 'history' → 'application-details'"

    - id: "USER-015"
      title: "Application Details - Deep Dive View"
      description: |
        Comprehensive detailed view of a single application with tabs for different information sections.
      visuals:
        - "'Back to History' button with arrow"
        - "Header:"
          - "Title: 'Application Details'"
          - "Type badge (blue for application, purple for renewal)"
          - "Status badge (colored)"
          - "Download buttons (if approved):"
            - "'Download Permit' (blue)"
            - "'Download Receipt' (outline)"
        - "Application Overview card (3-column grid):"
          - "Application ID, Business Name, Category"
          - "Date Submitted, Status, Application Fee"
          - "Permit Number (if approved), Expiry Date, Payment Reference"
        - "Tabbed content area (4 tabs):"
          - "Tab 1: Business Info"
          - "Tab 2: Documents"
          - "Tab 3: Status Timeline"
          - "Tab 4: Payment Details"
      actions:
        - "Click 'Back to History' → return to history page"
        - "Click tab headers to switch views"
        - "Click 'Download Permit' → download PDF (simulated)"
        - "Click 'Download Receipt' → download receipt (simulated)"
        - "Click 'View' on document → view document (simulated)"
        - "Click 'Pay Now' if payment pending"
      system_response:
        - "Loads recordData passed from history page"
        - "Generates additional mock details (owner info, reviewers, documents, timeline)"
        - "Download simulation: 2-second delay, sets downloadingFile state"
        - "Tab switching: controlled component state"
        - "Timeline dynamically shows progress based on status"
      files_involved:
        - "components/ApplicationDetails.tsx"
      state_changes:
        - "downloadingFile: null → 'permit'/'receipt' → null"

    - id: "USER-015-TAB1"
      title: "Application Details Tab 1 - Business Information"
      description: "Detailed business and processing information view."
      visuals:
        - "Business Information section (2-column grid):"
          - "Business Owner: Juan Carlos Dela Cruz"
          - "Email Address: <EMAIL>"
          - "Contact Number: +63 912 345 6789"
          - "Business Address: 123 Pioneer Avenue, General Santos City, 9500"
          - "Submitted By: Juan Carlos Dela Cruz"
        - "Processing Information section (3-column grid):"
          - "Reviewed By: Maria Santos (LGU Officer) - if reviewed"
          - "Approved By: Roberto Cruz (City Administrator) - if approved"
          - "Current Status: with icon and badge"
      actions:
        - "View information only (read-only)"
      system_response:
        - "Displays mock user and reviewer data"
        - "Processing info only shown if applicable to status"

    - id: "USER-015-TAB2"
      title: "Application Details Tab 2 - Submitted Documents"
      description: "List of all submitted documents with verification status."
      visuals:
        - "Document list (5 documents):"
          - "Each row shows:"
            - "FileText icon"
            - "Document name"
            - "Upload date"
            - "Status badge (Verified/Pending)"
            - "'View' button"
        - "Documents:"
          - "Business Registration Certificate"
          - "Barangay Business Clearance"
          - "Fire Safety Inspection Certificate"
          - "Sanitary Permit"
          - "Tax Identification Number (TIN)"
      actions:
        - "Click 'View' button to view document (simulated)"
      system_response:
        - "Displays mock document list with verified status"
        - "View button: placeholder for future implementation"

    - id: "USER-015-TAB3"
      title: "Application Details Tab 3 - Status Timeline"
      description: "Detailed timeline of application progress with dates and actors."
      visuals:
        - "Timeline with 5 major milestones:"
          - "Each milestone shows:"
            - "Colored circle icon (green check/blue clock/red X)"
            - "Step name"
            - "Date (if completed)"
            - "Description"
            - "Actor/Responsible person"
            - "Vertical connecting line"
        - "Milestones:"
          - "1. Application Submitted (completed, green)"
          - "2. Document Review (completed if not 'submitted', yellow/green)"
          - "3. Payment Processing (active if payment-pending, orange/green)"
          - "4. Final Approval (completed if approved, failed if rejected)"
          - "5. Permit Issued (completed if permitNumber exists, gray)"
      actions:
        - "View timeline progress (read-only)"
      system_response:
        - "Dynamically builds timeline based on application status"
        - "Shows dates for completed steps"
        - "Shows actor names for completed actions"
        - "Color codes: green (completed), blue (active), red (failed), gray (pending)"

    - id: "USER-015-TAB4"
      title: "Application Details Tab 4 - Payment Information"
      description: "Payment details, receipt, and payment options if pending."
      visuals:
        - "If payment completed:"
          - "Success alert (green): 'Payment Completed'"
          - "Payment details (2-column grid):"
            - "Payment Reference"
            - "Payment Method: GCash"
            - "Amount Paid: ₱X,XXX.00"
            - "Payment Date"
            - "Payment Status badge: Confirmed (green)"
        - "If payment pending:"
          - "Warning card (orange):"
            - "DollarSign icon"
            - "Heading: 'Payment Pending'"
            - "Amount Due (large, bold)"
            - "'Pay Now' button (green)"
        - "If no payment required yet:"
          - "Info card (gray):"
            - "CreditCard icon"
            - "'No Payment Required Yet'"
            - "Explanation text"
      actions:
        - "If payment completed: view details only"
        - "If payment pending: click 'Pay Now' to initiate payment"
        - "If no payment: wait for approval"
      system_response:
        - "Conditionally renders based on paymentReference and status"
        - "'Pay Now' would open payment flow (future implementation)"

    # ─────────────────────────────────────────────────────────────────────────
    # USER PROFILE & ACCOUNT MANAGEMENT
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-016"
      title: "User Profile - Account Information & Settings"
      description: |
        Comprehensive profile management with editable information and security settings.
      visuals:
        - "Page header: 'My Profile'"
        - "Account Stats cards (4 cards):"
          - "Total Applications: 23"
          - "Active Permits: 5"
          - "Renewals Done: 8"
          - "Account Age: 1 Year"
        - "Main profile card:"
          - "Header: 'Account Information' with Verified badge (green)"
          - "Two tabs: 'Profile Information' and 'Security Settings'"
        - "Quick action cards (2):"
          - "Permit History (blue icon)"
          - "Active Permits (green icon)"
      actions:
        - "Switch between Profile and Security tabs"
        - "Edit profile information"
        - "Change password"
        - "Navigate to Permit History or Dashboard"
      system_response:
        - "Loads mock user data from state"
        - "Tab switching: controlled component"
        - "Profile editing: toggles isEditing state"
        - "Password change: validates and shows alerts"
      files_involved:
        - "components/UserProfile.tsx"
      state_changes:
        - "isEditing: toggles between view and edit mode"
        - "userInfo: updates on save"
        - "passwordData: updates on input change"

    - id: "USER-016-TAB1"
      title: "Profile Tab - Personal & Business Information"
      description: "View and edit personal, business, and account details."
      visuals:
        - "'Edit Profile' button (top right) or 'Cancel'/'Save Changes' when editing"
        - "Personal Information section (2-column grid):"
          - "Full Name (editable with User icon)"
          - "Email Address (disabled, grayed out)"
          - "Contact Number (editable with Phone icon)"
          - "Business Type (editable with Building2 icon)"
          - "Note: 'Email cannot be changed for security reasons'"
        - "Business Information section:"
          - "Business Name (editable with Building2 icon)"
          - "Business Address (editable with MapPin icon)"
        - "Account Details section (2-column grid):"
          - "Date Joined (disabled, Calendar icon)"
          - "Last Login (disabled, Calendar icon)"
      actions:
        - "Click 'Edit Profile' → enables editing mode"
        - "Update editable fields"
        - "Click 'Save Changes' → saves and exits edit mode"
        - "Click 'Cancel' → reverts changes and exits edit mode"
      system_response:
        - "Edit mode: enables input fields, shows Save/Cancel buttons"
        - "Save: updates userInfo state, sets isEditing to false"
        - "Cancel: resets fields to original values, sets isEditing to false"
        - "Email field always disabled for security"
        - "Date fields always disabled (read-only)"
      state_changes:
        - "isEditing: false ↔ true"
        - "userInfo fields: updated on save"

    - id: "USER-016-TAB2"
      title: "Security Tab - Change Password"
      description: "Secure password change with validation and show/hide toggles."
      visuals:
        - "Lock icon + heading: 'Change Password'"
        - "Three password fields:"
          - "Current Password (with eye icon toggle)"
          - "New Password (with eye icon toggle)"
          - "Confirm New Password (with eye icon toggle)"
        - "Password Requirements card (blue):"
          - "At least 8 characters long"
          - "Contains uppercase and lowercase"
          - "Contains at least one number"
          - "Contains at least one special character"
        - "'Update Password' button (blue, with Lock icon)"
      actions:
        - "Enter current password"
        - "Enter new password"
        - "Confirm new password"
        - "Click eye icon to toggle password visibility"
        - "Click 'Update Password' to submit"
      system_response:
        - "Show/hide password: toggles input type between 'password' and 'text'"
        - "Button enabled only when all 3 fields filled AND new passwords match"
        - "On submit validation:"
          - "Checks if new passwords match → alert if not"
          - "Checks if new password >= 8 chars → alert if not"
          - "On success: shows alert 'Password changed successfully'"
          - "Resets all password fields to empty"
        - "Mock implementation (no actual password change in backend)"
      files_involved:
        - "components/UserProfile.tsx"
      state_changes:
        - "showPassword, showNewPassword, showConfirmPassword: toggle true/false"
        - "passwordData: updates on input, resets after success"

    # ─────────────────────────────────────────────────────────────────────────
    # FEEDBACK & COMMUNICATION
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-017"
      title: "Submit Feedback Form"
      description: |
        User provides feedback on system experience with ratings, categories, and recommendations.
      visuals:
        - "Page header: 'Share Your Feedback'"
        - "Feedback Form card:"
          - "Feedback Category dropdown (7 options):"
            - "System Performance, User Experience, Process Improvement, etc."
          - "Overall Experience Rating (1-5 stars with radio buttons)"
          - "Your Feedback textarea (required, min 10 chars)"
          - "Recommendation radio group (Yes/Maybe/No)"
          - "Privacy Notice (blue card)"
          - "Action buttons: 'Clear Form' (outline), 'Submit Feedback' (green)"
        - "Contact Information and Office Hours cards (2 columns)"
      actions:
        - "Select feedback category"
        - "Rate experience (1-5 stars)"
        - "Type feedback (minimum 10 characters)"
        - "Select recommendation option"
        - "Click 'Clear Form' to reset"
        - "Click 'Submit Feedback' to submit"
      system_response:
        - "Real-time validation: submit button disabled if feedback < 10 chars"
        - "Star rating: visual highlighting based on selected rating"
        - "Clear form: resets all fields to empty/default"
        - "On submit:"
          - "Sets submitted state to true"
          - "Generates reference ID: FB-{6-digit timestamp}"
          - "Shows success screen"
      files_involved:
        - "components/FeedbackForm.tsx"
      state_changes:
        - "formData: updates on user input"
        - "submitted: false → true on submit"

    - id: "USER-018"
      title: "Feedback Submitted - Success Confirmation"
      description: "Confirmation screen after successful feedback submission."
      visuals:
        - "Success card (centered):"
          - "Large green checkmark"
          - "Heading: 'Thank You for Your Feedback!'"
          - "Thank you message"
          - "Reference ID display (green card): FB-XXXXXX"
          - "Response time note: '2-3 business days'"
          - "'Submit Another Feedback' button (blue)"
      actions:
        - "Click 'Submit Another Feedback' → resets form and returns to form view"
      system_response:
        - "On button click: calls resetForm()"
        - "Resets submitted state to false"
        - "Clears all form data"
        - "Returns to feedback form view"
      state_changes:
        - "submitted: true → false"
        - "formData: all fields reset to empty"

    # ─────────────────────────────────────────────────────────────────────────
    # NOTIFICATIONS SYSTEM
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-019"
      title: "Notification System - Bell Icon & Badge"
      description: |
        Persistent notification indicator in navigation bar with unread count.
      visuals:
        - "Bell icon in navigation bar (top right)"
        - "Red badge with unread count (if > 0)"
        - "Badge shows '99+' if count > 99"
        - "Icon highlighted (blue) when panel is open"
      actions:
        - "Click bell icon → toggles notification panel"
      system_response:
        - "Calculates unreadCount: notifications.filter(n => !n.isRead).length"
        - "Toggles isNotificationPanelOpen state"
        - "Badge visibility: only shows if unreadCount > 0"
      files_involved:
        - "components/Navigation.tsx"
        - "App.tsx (notification state)"
      state_changes:
        - "isNotificationPanelOpen: toggles true/false"

    - id: "USER-020"
      title: "Notification Panel - Desktop Dropdown"
      description: |
        Dropdown panel for desktop view with notification list and actions.
      visuals:
        - "Dropdown panel (top-right, below bell icon):"
          - "Width: 384px (w-96)"
          - "Max height: 80vh"
          - "White background, shadow, rounded corners"
        - "Header section:"
          - "'Notifications' heading"
          - "Unread count badge (red)"
          - "'Mark all read' button (if unread > 0)"
          - "Close button (X icon)"
        - "Scrollable notification list (shows latest 5):"
          - "Each notification shows:"
            - "Colored icon based on type (success/warning/info/error)"
            - "Message text"
            - "Relative time (e.g., '1h ago')"
            - "Blue dot if unread"
            - "Hover effect and click to mark as read"
        - "Footer:"
          - "'View all notifications' link with chevron"
        - "Empty state (if no notifications):"
          - "Info icon (gray)"
          - "'No notifications yet' message"
      actions:
        - "Click 'Mark all read' → marks all as read"
        - "Click individual notification → marks that one as read"
        - "Click 'View all notifications' → navigates to full notifications page"
        - "Click X or press Escape → closes panel"
        - "Click outside panel → closes panel"
      system_response:
        - "Shows only latest 5 notifications (slice)"
        - "Relative time formatting: minutes/hours/days ago"
        - "Mark all read: updates all notifications.isRead to true"
        - "Mark as read: updates specific notification.isRead to true"
        - "View all: navigates to 'notifications' page and closes panel"
        - "Close: sets isNotificationPanelOpen to false"
        - "Outside click: detects with ref and event listener"
        - "Keyboard: Escape key closes panel"
      files_involved:
        - "components/NotificationPanel.tsx"
        - "components/NotificationItem.tsx"
      state_changes:
        - "notifications[].isRead: false → true when marked"
        - "isNotificationPanelOpen: true → false on close"
        - "currentPage: 'notifications' when 'View all' clicked"

    - id: "USER-021"
      title: "Notification Panel - Mobile Bottom Sheet"
      description: |
        Mobile-optimized bottom sheet for notifications with swipe gesture support.
      visuals:
        - "Black backdrop overlay (bg-opacity-20)"
        - "Bottom sheet sliding from bottom:"
          - "Rounded top corners"
          - "Max height: 85vh"
          - "White background"
        - "Pull handle (gray bar at top)"
        - "Header (sticky):"
          - "'Notifications' heading + unread badge"
          - "'Mark all read' button (if unread)"
          - "Close button (X)"
        - "Scrollable content area (same notification items as desktop)"
        - "Footer (sticky):"
          - "'View all notifications' button"
      actions:
        - "Same actions as desktop version"
        - "Click backdrop → closes panel"
        - "Swipe down on handle → closes panel (future enhancement)"
      system_response:
        - "Mobile detection: window.innerWidth < 768"
        - "Body scroll lock when open (overflow: hidden)"
        - "Backdrop click closes panel"
        - "Same notification logic as desktop"
      files_involved:
        - "components/NotificationPanel.tsx"
      state_changes:
        - "isMobile: computed from window width"
        - "Same state changes as desktop version"

    - id: "USER-022"
      title: "Notifications Page - Full View"
      description: |
        Dedicated page for viewing all notifications with grouping and filtering.
      visuals:
        - "Page header:"
          - "'Back' button with arrow"
          - "'Notifications' title"
          - "Subtitle: 'Stay updated with your application status'"
          - "'Mark all as read' button (if unread > 0)"
          - "Settings button (gear icon)"
        - "Unread summary banner (blue, if unread > 0):"
          - "Badge with unread count"
          - "Text: 'You have X unread notification(s)'"
        - "Notification groups (if notifications exist):"
          - "Today"
          - "Yesterday"
          - "This Week"
          - "Older"
          - "Each group has heading and notification items"
        - "Empty state (if no notifications):"
          - "Info icon (gray, large)"
          - "'No notifications yet'"
          - "Explanation text"
      actions:
        - "Click 'Back' → return to dashboard"
        - "Click 'Mark all as read' → marks all as read"
        - "Click individual notification → marks as read"
        - "Click settings button → placeholder"
      system_response:
        - "Groups notifications by date category"
        - "Date comparison: today/yesterday/thisWeek/older"
        - "Relative time formatting for each notification"
        - "Mark all read: updates all isRead to true"
        - "Mark individual: updates specific isRead to true"
      files_involved:
        - "components/NotificationsPage.tsx"
        - "components/NotificationItem.tsx"
      state_changes:
        - "Same state updates as notification panel"

    # ─────────────────────────────────────────────────────────────────────────
    # SYSTEM BEHAVIORS & ERROR HANDLING
    # ─────────────────────────────────────────────────────────────────────────

    - id: "USER-023"
      title: "Logout Flow"
      description: "User logs out and session is cleared."
      visuals:
        - "Logout option in user dropdown menu (desktop)"
        - "Logout button in mobile navigation"
        - "Red text color for logout option"
        - "LogOut icon"
      actions:
        - "Click 'Logout' from dropdown or mobile button"
      system_response:
        - "Calls onLogout handler"
        - "Resets all state:"
          - "isLoggedIn: true → false"
          - "userEmail: cleared"
          - "currentPage: reset to 'dashboard'"
          - "notifications: cleared to []"
          - "isNotificationPanelOpen: false"
        - "Navigates back to LoginPage"
      files_involved:
        - "App.tsx"
        - "components/Navigation.tsx"
      state_changes:
        - "Complete state reset to initial values"

    - id: "USER-024"
      title: "Responsive Design - Mobile Adaptations"
      description: |
        System automatically adapts UI for mobile devices.
      visuals:
        - "Mobile viewport (< 768px):"
          - "Top navigation: logo only, notification bell, simplified user menu"
          - "Bottom navigation bar: 6 icons (Home, Applications, Renewals, Track, Feedback, Profile)"
          - "Notification panel: bottom sheet instead of dropdown"
          - "Forms: single column layouts"
          - "Tables: horizontal scroll"
          - "Cards: stacked vertically"
      actions:
        - "Same user actions, different UI presentation"
      system_response:
        - "Tailwind responsive classes: sm:, md:, lg:"
        - "Mobile detection for notification panel"
        - "Window resize listener updates isMobile state"
      files_involved:
        - "All components (responsive design throughout)"

    - id: "USER-025"
      title: "Document Upload Validation & Error Handling"
      description: "System validates document uploads and provides error feedback."
      visuals:
        - "Document upload field with error state:"
          - "Error message in red text below upload button"
          - "Error icon (red)"
          - "File name not displayed"
      actions:
        - "User attempts to upload invalid file"
      system_response:
        - "Validates file type: only PDF, JPG, PNG allowed"
        - "Validates file size: max 5MB"
        - "On type error: shows 'Invalid file type. Please upload PDF, JPG, or PNG.'"
        - "On size error: shows 'File too large. Maximum size is 5MB.'"
        - "Sets document status to 'error'"
        - "File not saved to state"
        - "Next button remains disabled"
      files_involved:
        - "components/SubmitApplicationForm.tsx"

    - id: "USER-026"
      title: "Payment Failure Handling"
      description: "System handles payment failures gracefully."
      visuals:
        - "Payment failed state:"
          - "Red X icon (large)"
          - "Heading: 'Payment Failed'"
          - "Error message"
          - "'Try Again' button (red outline)"
      actions:
        - "Payment simulation returns failure (10% chance in new applications)"
        - "User clicks 'Try Again'"
      system_response:
        - "On failure: sets paymentStatus to 'failed'"
        - "Displays error UI"
        - "Next button remains disabled"
        - "On 'Try Again': resets paymentStatus to '' (empty)"
        - "Returns to payment form"
        - "GCash number persists (not cleared)"
      files_involved:
        - "components/SubmitApplicationForm.tsx"
        - "components/RenewalForm.tsx"

  # ═══════════════════════════════════════════════════════════════════════════
  # ADMIN STORYBOARD - NOT YET IMPLEMENTED
  # ═══════════════════════════════════════════════════════════════════════════

  admin_scenes:
    - id: "ADMIN-NOTE"
      title: "Admin Functionality - Not Implemented"
      description: |
        The ADMIN side of the Smart Governance system is NOT YET IMPLEMENTED in the current codebase.
        
        Based on the use case diagram and original storyboard requirements, the admin system should include:
        
        PLANNED ADMIN FEATURES (Not in current code):
        - Admin Login & Authentication
        - Admin Dashboard with application queue
        - Review Applications workflow
        - Document validation interface
        - Approve/Reject application actions
        - Payment verification (PayMongo webhook handling)
        - Permit generation and issuance
        - User management
        - System settings and configuration
        - Audit logs and reporting
        - Notification management to clients
        - Renewal request processing
        - Amendment request handling
        
        IMPLEMENTATION STATUS: NOT STARTED
        
        The current implementation focuses exclusively on the CLIENT/USER experience.
        Admin functionality would require:
        - Separate admin authentication system
        - Admin-specific components and routes
        - Backend API integration (currently using mock data)
        - Database operations (MySQL as per use case diagram)
        - PayMongo webhook integration
        - Email service integration
        - Role-based access control (RBAC)
        - Admin notification system
        
      implementation_status: "PLANNED_NOT_IMPLEMENTED"
      visuals: []
      actions: []
      system_response: []

# ═══════════════════════════════════════════════════════════════════════════
# TECHNICAL IMPLEMENTATION DETAILS
# ═══════════════════════════════════════════════════════════════════════════

technical_details:
  frontend_stack:
    framework: "React with TypeScript"
    styling: "Tailwind CSS v4.0"
    ui_components: "ShadCN UI component library"
    icons: "Lucide React"
    state_management: "React useState hooks (component-level state)"
    routing: "Client-side routing via state (currentPage)"
  
  color_scheme:
    primary_blue: "#1E40AF"
    primary_green: "#059669"
    status_colors:
      submitted: "#3B82F6 (blue-500)"
      under_review: "#EAB308 (yellow-500)"
      payment_pending: "#F97316 (orange-500)"
      approved: "#10B981 (green-500)"
      rejected: "#EF4444 (red-500)"
      archived: "#6B7280 (gray-500)"
  
  key_state_management:
    app_level_state:
      - "isLoggedIn: boolean"
      - "userEmail: string"
      - "currentPage: string"
      - "selectedApplicationData: any"
      - "notifications: Notification[]"
      - "isNotificationPanelOpen: boolean"
    
    notification_structure:
      - "id: number"
      - "message: string"
      - "time: string"
      - "timestamp: Date"
      - "type: 'success' | 'warning' | 'info' | 'error'"
      - "isRead: boolean"
  
  mock_data:
    applications:
      - "3 sample applications in TrackStatusPage"
      - "8 sample records in PermitHistory"
      - "Dates range from 2023 to 2025"
    
    payments:
      - "GCash payment simulation"
      - "2-second processing delay"
      - "90% success rate for new applications"
      - "95% success rate for renewals"
      - "Reference number format: GC/RN + 8-digit timestamp"
    
    user_profile:
      - "Mock user: Gidjazphin B. Izon"
      - "Mock business: Sunrise Bakery"
      - "Account stats: 23 applications, 5 active permits, 8 renewals, 1 year"
  
  responsive_breakpoints:
    mobile: "< 768px"
    tablet: "768px - 1024px"
    desktop: "> 1024px"
  
  file_structure:
    main_app: "/App.tsx"
    components:
      - "/components/LoginPage.tsx"
      - "/components/Dashboard.tsx"
      - "/components/Navigation.tsx"
      - "/components/SubmitApplicationForm.tsx"
      - "/components/RenewalForm.tsx"
      - "/components/TrackStatusPage.tsx"
      - "/components/FeedbackForm.tsx"
      - "/components/UserProfile.tsx"
      - "/components/PermitHistory.tsx"
      - "/components/ApplicationDetails.tsx"
      - "/components/NotificationsPage.tsx"
      - "/components/NotificationPanel.tsx"
      - "/components/NotificationItem.tsx"
      - "/components/ErrorBoundary.tsx"
    ui_components: "/components/ui/* (40+ ShadCN components)"
    styles: "/styles/globals.css"

# ═══════════════════════════════════════════════════════════════════════════
# FUTURE ENHANCEMENTS & MISSING FEATURES
# ═══════════════════════════════════════════════════════════════════════════

missing_features:
  authentication:
    - "Real backend authentication (currently mock)"
    - "OTP verification via email"
    - "Password reset/recovery"
    - "Session management with JWT"
    - "OAuth integration"
  
  payment_integration:
    - "Actual PayMongo API integration"
    - "Real payment processing"
    - "Payment webhooks"
    - "Payment history tracking"
    - "Refund handling"
  
  document_management:
    - "Actual file upload to cloud storage"
    - "Document preview/viewer"
    - "Document versioning"
    - "PDF generation for permits"
    - "QR code generation for permit validation"
  
  notifications:
    - "Real-time notifications (WebSocket/SSE)"
    - "Email notifications"
    - "SMS notifications"
    - "Push notifications (PWA)"
    - "Notification preferences/settings"
  
  admin_system:
    - "Complete admin portal (not implemented)"
    - "Application review workflow"
    - "Document validation tools"
    - "Approval/rejection workflow"
    - "User management"
    - "System configuration"
    - "Reports and analytics"
  
  backend_integration:
    - "RESTful API endpoints"
    - "Database integration (MySQL)"
    - "File storage (AWS S3/Azure Blob)"
    - "Email service integration"
    - "PayMongo webhook handlers"
    - "Audit logging"
  
  user_experience:
    - "Amendment/resubmission flow (Scene 9.5 from original storyboard)"
    - "Permit download with QR code"
    - "Application templates/pre-fill"
    - "Multi-language support"
    - "Accessibility improvements (WCAG compliance)"
    - "Dark mode"

# ═══════════════════════════════════════════════════════════════════════════
# SUMMARY & NOTES
# ═══════════════════════════════════════════════════════════════════════════

summary: |
  This storyboard represents the COMPLETE USER-SIDE implementation of the Smart Governance
  Business Permit Processing and Renewal System for LGU General Santos City.
  
  FULLY IMPLEMENTED USER FEATURES:
  ✅ User authentication (Login/Register with mock backend)
  ✅ Personalized dashboard with status overview
  ✅ 4-step new application submission flow
  ✅ 4-step permit renewal flow
  ✅ Application status tracking with timeline visualization
  ✅ Comprehensive permit history with filtering and search
  ✅ Detailed application view with tabbed interface
  ✅ User profile management with editable fields
  ✅ Password change functionality
  ✅ Feedback submission system
  ✅ Advanced notification system (panel + dedicated page)
  ✅ Responsive design (desktop + mobile)
  ✅ Mock payment integration (GCash simulation)
  ✅ Document upload with validation
  

  
  TECHNOLOGY:
  - React + TypeScript
  - Tailwind CSS v4.0
  - ShadCN UI components
  - Client-side state management
  - Mock data for demonstration
  
  The system provides a complete, functional user experience with all major user journeys
  implemented, but operates on mock data and simulated backend operations. Admin functionality
  and real backend integration are planned but not yet implemented.
