# Algorithm 3: OTP Verification

## Overview
This algorithm verifies the One-Time Password (OTP) sent to users during registration, validates the code against the database, checks expiration, and marks the user account as verified.

---

## Algorithm Complexity
- **Time Complexity:** O(1) - Constant time for database operations
- **Space Complexity:** O(1) - Fixed size data structures
- **Database Operations:** 2 queries (1 SELECT, 1 UPDATE)

---

## Input Parameters
```typescript
interface OTPVerificationInput {
  email: string;    // User's email address
  otp_code: string; // 6-digit OTP code
}
```

## Output
```typescript
interface OTPVerificationOutput {
  success: boolean;
  message: string;
  verified?: boolean;
}
```

---

## Pseudocode

```
ALGORITHM VerifyOTP(email, otp_code)
BEGIN
  // Step 1: Input Validation
  IF email is empty OR otp_code is empty THEN
    RETURN error("Email and OTP code are required")
  END IF
  
  IF otp_code.length ≠ 6 OR NOT is_numeric(otp_code) THEN
    RETURN error("Invalid OTP format. Must be 6 digits")
  END IF
  
  // Step 2: Find User by Email
  user ← DATABASE.query("SELECT * FROM client_accounts WHERE email = ?", email)
  
  IF user NOT EXISTS THEN
    RETURN error("User not found")
  END IF
  
  // Step 3: Check if Already Verified
  IF user.is_verified = TRUE THEN
    RETURN error("Email already verified")
  END IF
  
  // Step 4: Find OTP Record
  otp_record ← DATABASE.query(
    "SELECT * FROM otp_verification 
     WHERE client_account_id = ? 
     AND purpose = 'registration' 
     ORDER BY created_at DESC 
     LIMIT 1",
    user.id
  )
  
  IF otp_record NOT EXISTS THEN
    RETURN error("No OTP found. Please request a new one")
  END IF
  
  // Step 5: Check OTP Expiration
  current_time ← CURRENT_TIMESTAMP
  
  IF current_time > otp_record.expires_at THEN
    RETURN error("OTP has expired. Please request a new one")
  END IF
  
  // Step 6: Verify OTP Code
  IF otp_code ≠ otp_record.otp_code THEN
    RETURN error("Invalid OTP code")
  END IF
  
  // Step 7: Mark User as Verified
  DATABASE.update("client_accounts", {
    is_verified: TRUE,
    updated_at: CURRENT_TIMESTAMP
  }, WHERE id = user.id)
  
  // Step 8: Delete Used OTP (Optional - for security)
  DATABASE.delete("otp_verification", WHERE id = otp_record.id)
  
  // Step 9: Return Success Response
  RETURN success({
    message: "Email verified successfully. You can now log in.",
    verified: TRUE
  })
END
```

---

## Flowchart

```mermaid
flowchart TD
    Start([Start: OTP Verification]) --> Input[Receive: email, otp_code]
    Input --> ValidateInput{Email and<br/>OTP Code<br/>Provided?}
    ValidateInput -->|No| ErrorInput[Return Error:<br/>Email and OTP required]
    ValidateInput -->|Yes| ValidateFormat{OTP is<br/>6 digits?}
    ValidateFormat -->|No| ErrorFormat[Return Error:<br/>Invalid OTP format]
    ValidateFormat -->|Yes| FindUser[Query Database:<br/>Find user by email]
    FindUser --> UserExists{User<br/>Found?}
    UserExists -->|No| ErrorUser[Return Error:<br/>User not found]
    UserExists -->|Yes| CheckVerified{Already<br/>Verified?}
    CheckVerified -->|Yes| ErrorAlready[Return Error:<br/>Email already verified]
    CheckVerified -->|No| FindOTP[Query Database:<br/>Find latest OTP record]
    FindOTP --> OTPExists{OTP<br/>Record<br/>Found?}
    OTPExists -->|No| ErrorNoOTP[Return Error:<br/>No OTP found]
    OTPExists -->|Yes| CheckExpiry{OTP<br/>Expired?}
    CheckExpiry -->|Yes| ErrorExpired[Return Error:<br/>OTP has expired]
    CheckExpiry -->|No| CompareOTP{OTP Code<br/>Matches?}
    CompareOTP -->|No| ErrorInvalid[Return Error:<br/>Invalid OTP code]
    CompareOTP -->|Yes| UpdateUser[Update client_accounts:<br/>Set is_verified = TRUE]
    UpdateUser --> DeleteOTP[Delete OTP Record:<br/>Security cleanup]
    DeleteOTP --> Success[Return Success:<br/>Email verified]
    Success --> End([End])
    ErrorInput --> End
    ErrorFormat --> End
    ErrorUser --> End
    ErrorAlready --> End
    ErrorNoOTP --> End
    ErrorExpired --> End
    ErrorInvalid --> End
```

---

## Step-by-Step Explanation

### Step 1: Input Validation
- Verify email and OTP code are provided
- Check OTP format (must be exactly 6 digits)
- Validate OTP contains only numeric characters

### Step 2: Find User by Email
- Query `client_accounts` table
- Ensure user exists before proceeding
- Return error if user not found

### Step 3: Check if Already Verified
- Check `is_verified` field
- Prevent duplicate verification
- Return appropriate message if already verified

### Step 4: Find OTP Record
- Query `otp_verification` table
- Filter by client_account_id and purpose='registration'
- Order by created_at DESC to get latest OTP
- Limit to 1 record

### Step 5: Check OTP Expiration
- Compare current timestamp with expires_at
- OTP valid for 10 minutes from creation
- Return error if expired

### Step 6: Verify OTP Code
- Compare provided OTP with stored OTP
- Must match exactly (case-sensitive for numbers)
- Return error if mismatch

### Step 7: Mark User as Verified
- Update `is_verified` to TRUE
- Update `updated_at` timestamp
- User can now log in

### Step 8: Delete Used OTP
- Remove OTP record from database
- Prevents OTP reuse
- Security best practice

### Step 9: Return Success Response
- Confirm email verification
- Redirect user to login page
- Display success message

---

## Security Considerations

1. **OTP Expiration:** 10-minute window limits brute force attempts
2. **Single Use:** OTP deleted after successful verification
3. **Latest OTP Only:** Only most recent OTP is valid
4. **No OTP in Response:** Never return OTP code in API response
5. **Rate Limiting:** Should implement rate limiting on verification attempts (not yet implemented)

---

## Error Handling

| Error Type | HTTP Status | Message |
|------------|-------------|---------|
| Missing Input | 400 | "Email and OTP code are required" |
| Invalid Format | 400 | "Invalid OTP format. Must be 6 digits" |
| User Not Found | 404 | "User not found" |
| Already Verified | 400 | "Email already verified" |
| No OTP Found | 404 | "No OTP found. Please request a new one" |
| OTP Expired | 400 | "OTP has expired. Please request a new one" |
| Invalid OTP | 400 | "Invalid OTP code" |
| Database Error | 500 | "Verification failed. Please try again." |

---

## Database Tables Involved

### client_accounts
- **Operation:** SELECT, UPDATE
- **Fields Read:** id, email, is_verified
- **Fields Updated:** is_verified, updated_at

### otp_verification
- **Operation:** SELECT, DELETE
- **Fields Read:** id, client_account_id, otp_code, purpose, expires_at, created_at
- **Fields Deleted:** Entire record after successful verification

---

## OTP Generation Details

### OTP Format
- **Length:** 6 digits
- **Range:** 100000 - 999999
- **Type:** Numeric only
- **Generation:** `Math.floor(100000 + Math.random() * 900000)`

### OTP Expiration
- **Duration:** 10 minutes
- **Calculation:** `created_at + INTERVAL '10 minutes'`
- **Timezone:** UTC with timezone support

---

## Implementation Files

- **Controller:** `controllers/authController.js` - `verifyOtp()` function
- **Model:** `models/ClientAccount.js`, `models/OtpVerification.js`
- **Route:** `routes/auth.js` - `POST /api/auth/verify-otp`
- **Frontend:** `src/pages/RegisterPage.tsx` (OTP input modal)

---

## Future Enhancements

1. **Rate Limiting:** Limit verification attempts (e.g., 5 attempts per 15 minutes)
2. **Resend OTP:** Implement resend functionality with cooldown period
3. **SMS OTP:** Add SMS as alternative to email
4. **OTP Length:** Make OTP length configurable (4, 6, or 8 digits)
5. **Attempt Tracking:** Track failed attempts and lock account after threshold

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

