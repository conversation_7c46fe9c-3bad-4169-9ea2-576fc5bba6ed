// Sequelize Model for Feedback Submission - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define FeedbackSubmission model for user feedback
// Security: UUID primary keys, rating validation, foreign key constraints
// Standards: Sequelize v6+ conventions with PostgreSQL data types

module.exports = (sequelize, DataTypes) => {
  const FeedbackSubmission = sequelize.define('FeedbackSubmission', {
    // Primary key: UUID v4 for secure identifiers
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique feedback identifier (UUID v4)',
    },
    // Foreign key to client_accounts table
    client_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'client_accounts',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Reference to user account',
    },
    // Optional foreign key to business_applications table
    application_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'business_applications',
        key: 'id',
      },
      onDelete: 'SET NULL',
      comment: 'Optional reference to related application',
    },
    // Rating from 1 to 5 stars
    rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: {
          args: [1],
          msg: 'Rating must be at least 1',
        },
        max: {
          args: [5],
          msg: 'Rating must not exceed 5',
        },
        isInt: {
          msg: 'Rating must be an integer',
        },
      },
      comment: 'Rating from 1 to 5 stars',
    },
    // Feedback comments
    comments: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'User feedback comments',
    },
    // Submission timestamp
    submitted_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Timestamp when feedback was submitted',
    },
    // Additional metadata for flexible extensions
    metadata: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
      comment: 'JSONB field for flexible metadata (user agent, IP, etc.)',
    },
  }, {
    // Table configuration
    tableName: 'feedback_submissions',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Foreign key index for performance
      {
        fields: ['client_id'],
        name: 'idx_feedback_submissions_client_id',
        using: 'btree',
      },
      // Application reference index
      {
        fields: ['application_id'],
        name: 'idx_feedback_submissions_application_id',
        using: 'btree',
      },
      // Rating filtering index
      {
        fields: ['rating'],
        name: 'idx_feedback_submissions_rating',
        using: 'btree',
      },
      // Submission date index for reporting
      {
        fields: ['submitted_at'],
        name: 'idx_feedback_submissions_submitted_at',
        using: 'btree',
      },
      // GIN index for JSONB metadata queries
      {
        fields: ['metadata'],
        name: 'idx_feedback_submissions_metadata',
        using: 'gin',
      },
    ],
  });

  // Instance method to get star rating display
  FeedbackSubmission.prototype.getStarDisplay = function() {
    return '★'.repeat(this.rating) + '☆'.repeat(5 - this.rating);
  };

  // Static method to find feedback by client
  FeedbackSubmission.findByClient = async function(clientId) {
    return await this.findAll({
      where: { client_id: clientId },
      order: [['submitted_at', 'DESC']],
    });
  };

  // Static method to find feedback by application
  FeedbackSubmission.findByApplication = async function(applicationId) {
    return await this.findAll({
      where: { application_id: applicationId },
      order: [['submitted_at', 'DESC']],
    });
  };

  // Static method to calculate average rating
  FeedbackSubmission.getAverageRating = async function() {
    const result = await this.findAll({
      attributes: [
        [sequelize.fn('AVG', sequelize.col('rating')), 'average_rating'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'total_count'],
      ],
    });
    
    if (result && result[0]) {
      return {
        average: parseFloat(result[0].dataValues.average_rating || 0).toFixed(2),
        count: parseInt(result[0].dataValues.total_count || 0),
      };
    }
    
    return { average: 0, count: 0 };
  };

  // Static method to get rating distribution
  FeedbackSubmission.getRatingDistribution = async function() {
    const result = await this.findAll({
      attributes: [
        'rating',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
      ],
      group: ['rating'],
      order: [['rating', 'DESC']],
    });
    
    const distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    result.forEach(item => {
      distribution[item.rating] = parseInt(item.dataValues.count);
    });
    
    return distribution;
  };

  return FeedbackSubmission;
};

