/**
 * Utility for handling API retries with exponential backoff
 * @param {Function} apiCall - The API function to call
 * @param {Object} options - Configuration options
 * @param {number} options.maxRetries - Maximum number of retry attempts (default: 3)
 * @param {number} options.initialDelay - Initial delay in milliseconds (default: 1000)
 * @param {Function} options.shouldRetry - Function to determine if a retry should be attempted
 * @returns {Promise<any>} - The API response
 */
const withRetry = async (apiCall, options = {}) => {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    shouldRetry = (error) => {
      // Retry on network errors or 5xx server errors
      return !error.response || (error.response.status >= 500 && error.response.status < 600);
    }
  } = options;

  let lastError;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      // Check if we should retry
      if (!shouldRetry(error) || attempt === maxRetries - 1) {
        break;
      }

      // Calculate delay with exponential backoff and jitter
      const delay = Math.min(
        initialDelay * Math.pow(2, attempt) + Math.random() * 1000,
        30000 // Maximum 30 seconds delay
      );

      console.warn(`API call failed (attempt ${attempt + 1}/${maxRetries}), retrying in ${Math.round(delay)}ms...`, error.message);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
      attempt++;
    }
  }

  // If we get here, all retries failed
  console.error(`API call failed after ${maxRetries} attempts`);
  throw lastError;
};

module.exports = { withRetry };
