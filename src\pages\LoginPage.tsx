import React, { useState, useEffect } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { Eye, EyeOff, Lock, Mail } from 'lucide-react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { Label } from '@/components/ui/Label'
import { Checkbox } from '@/components/ui/Checkbox'
// TEMPORARILY DISABLED: OTP Modal import
// import { OTPModal } from '@/components/OTPModal'
import { loginSchema, LoginFormData } from '@/schemas/authSchemas'
import { authService } from '@/services/authService'
import { useAuth } from '@/context/AuthContext'
import { useToast } from '@/hooks/useToast'

const LoginPage: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const navigate = useNavigate()
  const { login, isAuthenticated, isLoggingIn } = useAuth()
  const { toast } = useToast()

  // Show login form immediately after splash screen navigation
  useEffect(() => {
    setIsVisible(true)
  }, [])

  // Navigate to dashboard when user becomes authenticated after login
  useEffect(() => {
    if (isAuthenticated) {
      console.log('[LoginPage] Redirecting to dashboard: isAuthenticated=', isAuthenticated)
      navigate('/dashboard', { replace: true })
    }
  }, [isAuthenticated, navigate])

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    mode: 'onBlur',
   })

  const onSubmit = async (data: LoginFormData) => {
    try {
      console.log('[LoginPage] Attempting login for:', data.email)
      const response = await authService.login(data)
      console.log('[LoginPage] Login response:', response)

      // authService returns the backend response: { success, message, data: { token, user } }
      // Check if login was successful
      if (response.success === true && response.data?.token && response.data?.user) {
        console.log('[LoginPage] Login successful, user:', response.data.user)
        console.log('[LoginPage] Calling login() with user data')
        // Token and user are already stored by authService
        // Update auth context
        await login(response.data.user)
        console.log('[LoginPage] Auth context login() completed, user should be set now')
        console.log('[LoginPage] Navigating directly to dashboard')
        // Navigate directly to dashboard after successful login
        navigate('/dashboard', { replace: true })
      } else {
        // If login succeeds but no user data, show error
        console.error('[LoginPage] Login response incomplete:', response)
        toast({
          variant: 'destructive',
          title: 'Login Failed',
          description: 'Login response missing required data. Please try again.',
        })
      }
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid email or password. Please try again.'
      console.error('[LoginPage] Login error:', errorMessage)

      toast({
        variant: 'destructive',
        title: 'Login Failed',
        description: errorMessage,
      })
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 px-4 py-8">
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 40 }}
        transition={{
          duration: 0.8,
          ease: [0.25, 0.46, 0.45, 0.94], // Match splash screen easing
          delay: isVisible ? 0 : 0
        }}
        className="w-full max-w-md"
      >
        <Card className="shadow-2xl">
          <CardHeader className="space-y-1">
            <div className="flex justify-center mb-4">
              <div className="w-24 h-24 rounded-full shadow-md flex items-center justify-center overflow-hidden border-4 border-blue-100">
                <img 
                  src="/logo.webp" 
                  alt="LGU Logo" 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // Fallback if image doesn't load
                    (e.target as HTMLImageElement).style.display = 'none'
                  }}
                />
              </div>
            </div>
            <CardTitle className="text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              General Santos City Business Permit & Renewal System
            </CardTitle>
          </CardHeader>

          <CardContent>
            <form 
              onSubmit={handleSubmit(onSubmit)} 
              onKeyDown={(e) => {
                if (e.key === 'Enter' && e.ctrlKey === false && e.shiftKey === false) {
                  e.preventDefault()
                  handleSubmit(onSubmit)()
                }
              }}
              className="space-y-5" 
              noValidate
            >
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium">
                  Email Address
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" aria-hidden="true" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    autoComplete="email"
                    className="pl-10"
                    error={errors.email?.message}
                    aria-describedby={errors.email ? 'email-error' : undefined}
                    {...register('email')}
                  />
                </div>
                {errors.email && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    id="email-error"
                    className="text-sm text-destructive"
                    role="alert"
                  >
                    {errors.email.message}
                  </motion.p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium">
                  Password
                </Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" aria-hidden="true" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    autoComplete="current-password"
                    className="pl-10 pr-10"
                    error={errors.password?.message}
                    aria-describedby={errors.password ? 'password-error' : undefined}
                    {...register('password')}
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                    tabIndex={-1}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
                {errors.password && (
                  <motion.p
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    id="password-error"
                    className="text-sm text-destructive"
                    role="alert"
                  >
                    {errors.password.message}
                  </motion.p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="rememberMe" 
                    aria-label="Remember me for next login"
                    {...register('rememberMe')} 
                  />
                  <Label
                    htmlFor="rememberMe"
                    className="text-sm font-normal cursor-pointer"
                  >
                    Remember me
                  </Label>
                </div>
                <Link
                  to="/forgot-password"
                  className="text-sm text-primary hover:underline font-medium"
                >
                  Forgot password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full"
                size="lg"
                isLoading={isLoggingIn}
                disabled={isLoggingIn}
              >
                Sign In
              </Button>
            </form>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4">
            <div className="text-sm text-center text-muted-foreground">
              Don't have an account?{' '}
              <Link to="/register" className="text-primary hover:underline font-medium">
                Sign up
              </Link>
            </div>
          </CardFooter>
        </Card>

        <p className="mt-6 text-center text-xs text-muted-foreground">
          Smart Governance System © 2025. All rights reserved.
        </p>
      </motion.div>
    </div>
  )
}

export default LoginPage

