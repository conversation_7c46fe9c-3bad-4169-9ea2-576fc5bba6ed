# Algorithm 4: Business Profile Creation

## Overview
This algorithm handles the creation of business profiles for authenticated users, including validation of business information, TIN number format checking, and storing business data in the PostgreSQL database.

---

## Algorithm Complexity
- **Time Complexity:** O(1) - Constant time for database operations
- **Space Complexity:** O(1) - Fixed size data structures
- **Database Operations:** 1 INSERT query (business_profiles)

---

## Input Parameters
```typescript
interface BusinessProfileInput {
  client_account_id: UUID;      // From JWT token (authenticated user)
  business_name: string;         // Required, 2-255 characters
  business_type: string;         // Required (e.g., "Retail", "Food Service")
  address: string;               // Required, full business address
  contact_number: string;        // Optional, phone number
  tin_number: string;            // Optional, Tax Identification Number
}
```

## Output
```typescript
interface BusinessProfileOutput {
  success: boolean;
  message: string;
  business_profile?: {
    id: UUID;
    business_name: string;
    business_type: string;
    address: string;
    contact_number: string;
    tin_number: string;
    created_at: Date;
  };
}
```

---

## Pseudocode

```
ALGORITHM CreateBusinessProfile(client_account_id, business_name, business_type, address, contact_number, tin_number)
BEGIN
  // Step 1: Authentication Check
  IF client_account_id is empty THEN
    RETURN error(401, "Authentication required")
  END IF
  
  // Step 2: Input Validation
  IF business_name is empty THEN
    RETURN error("Business name is required")
  END IF
  
  IF business_name.length < 2 OR business_name.length > 255 THEN
    RETURN error("Business name must be between 2 and 255 characters")
  END IF
  
  IF business_type is empty THEN
    RETURN error("Business type is required")
  END IF
  
  IF address is empty THEN
    RETURN error("Address is required")
  END IF
  
  // Step 3: Validate Contact Number (if provided)
  IF contact_number is NOT empty THEN
    IF NOT matches_pattern(contact_number, /^[0-9+\-\s()]+$/) THEN
      RETURN error("Invalid phone number format")
    END IF
  END IF
  
  // Step 4: Validate TIN Number (if provided)
  IF tin_number is NOT empty THEN
    IF NOT matches_pattern(tin_number, /^[0-9\-]+$/) THEN
      RETURN error("TIN must contain only numbers and hyphens")
    END IF
  END IF
  
  // Step 5: Generate UUID for Business Profile
  business_profile_id ← UUID.generate_v4()
  
  // Step 6: Insert Business Profile
  business_profile ← DATABASE.insert("business_profiles", {
    id: business_profile_id,
    client_account_id: client_account_id,
    business_name: business_name,
    business_type: business_type,
    address: address,
    contact_number: contact_number OR NULL,
    tin_number: tin_number OR NULL,
    metadata: {},
    created_at: CURRENT_TIMESTAMP,
    updated_at: CURRENT_TIMESTAMP
  })
  
  // Step 7: Return Success Response
  RETURN success({
    message: "Business profile created successfully",
    business_profile: business_profile
  })
END
```

---

## Flowchart

```mermaid
flowchart TD
    Start([Start: Create Business Profile]) --> Auth{User<br/>Authenticated?}
    Auth -->|No| ErrorAuth[Return Error 401:<br/>Authentication required]
    Auth -->|Yes| Input[Receive: business_name,<br/>business_type, address,<br/>contact_number, tin_number]
    Input --> ValidateName{Business Name<br/>Provided?}
    ValidateName -->|No| ErrorName[Return Error:<br/>Business name required]
    ValidateName -->|Yes| ValidateLength{Name Length<br/>2-255 chars?}
    ValidateLength -->|No| ErrorLength[Return Error:<br/>Invalid name length]
    ValidateLength -->|Yes| ValidateType{Business Type<br/>Provided?}
    ValidateType -->|No| ErrorType[Return Error:<br/>Business type required]
    ValidateType -->|Yes| ValidateAddress{Address<br/>Provided?}
    ValidateAddress -->|No| ErrorAddress[Return Error:<br/>Address required]
    ValidateAddress -->|Yes| CheckContact{Contact Number<br/>Provided?}
    CheckContact -->|Yes| ValidateContact{Valid Phone<br/>Format?}
    ValidateContact -->|No| ErrorContact[Return Error:<br/>Invalid phone format]
    ValidateContact -->|Yes| CheckTIN
    CheckContact -->|No| CheckTIN{TIN Number<br/>Provided?}
    CheckTIN -->|Yes| ValidateTIN{Valid TIN<br/>Format?}
    ValidateTIN -->|No| ErrorTIN[Return Error:<br/>Invalid TIN format]
    ValidateTIN -->|Yes| GenerateUUID
    CheckTIN -->|No| GenerateUUID[Generate UUID<br/>for business_profile_id]
    GenerateUUID --> InsertProfile[Insert into<br/>business_profiles table]
    InsertProfile --> Success[Return Success:<br/>Business profile created]
    Success --> End([End])
    ErrorAuth --> End
    ErrorName --> End
    ErrorLength --> End
    ErrorType --> End
    ErrorAddress --> End
    ErrorContact --> End
    ErrorTIN --> End
```

---

## Step-by-Step Explanation

### Step 1: Authentication Check
- Verify JWT token is valid
- Extract client_account_id from token payload
- Middleware handles authentication before controller

### Step 2: Input Validation
- Check required fields: business_name, business_type, address
- Validate business_name length (2-255 characters)
- Ensure no empty strings for required fields

### Step 3: Validate Contact Number
- Optional field validation
- Must match pattern: digits, +, -, spaces, parentheses
- Examples: "***********", "+63 912 345 6789", "(02) 1234-5678"

### Step 4: Validate TIN Number
- Optional field validation
- Must contain only numbers and hyphens
- Examples: "123-456-789-000", "************"

### Step 5: Generate UUID
- Create unique identifier for business profile
- Uses UUID v4 (random generation)
- Ensures uniqueness across distributed systems

### Step 6: Insert Business Profile
- Insert record into `business_profiles` table
- Link to user via client_account_id foreign key
- Set metadata to empty JSON object
- Record creation timestamp

### Step 7: Return Success Response
- Return complete business profile data
- Include generated UUID
- Frontend uses this data to update UI

---

## Security Considerations

1. **Authentication Required:** JWT token must be valid
2. **User Isolation:** Users can only create profiles for themselves
3. **Input Sanitization:** Sequelize prevents SQL injection
4. **Length Limits:** Prevent database overflow attacks
5. **Format Validation:** Regex patterns prevent malicious input

---

## Error Handling

| Error Type | HTTP Status | Message |
|------------|-------------|---------|
| Not Authenticated | 401 | "Authentication required" |
| Missing Business Name | 400 | "Business name is required" |
| Invalid Name Length | 400 | "Business name must be between 2 and 255 characters" |
| Missing Business Type | 400 | "Business type is required" |
| Missing Address | 400 | "Address is required" |
| Invalid Phone Format | 400 | "Invalid phone number format" |
| Invalid TIN Format | 400 | "TIN must contain only numbers and hyphens" |
| Database Error | 500 | "Failed to create business profile" |

---

## Database Tables Involved

### business_profiles
- **Operation:** INSERT
- **Fields:** id, client_account_id, business_name, business_type, address, contact_number, tin_number, metadata
- **Indexes:** btree on client_account_id, btree on business_name, gin on metadata
- **Foreign Key:** client_account_id → client_accounts(id) ON DELETE CASCADE

---

## Business Type Examples

Common business types in General Santos City:
- Retail Store
- Food Service / Restaurant
- Sari-Sari Store
- Beauty Salon / Barbershop
- Internet Cafe
- Laundry Service
- Construction
- Transportation
- Wholesale
- Manufacturing
- Professional Services
- Other

---

## Implementation Files

- **Controller:** `controllers/businessController.js` - `createBusinessProfile()` function
- **Model:** `models/BusinessProfile.js`
- **Route:** `routes/business.js` - `POST /api/business-profile`
- **Middleware:** `middleware/auth.js` - `authenticateToken()`
- **Frontend:** `src/pages/BusinessProfilePage.tsx`
- **Service:** `src/services/businessService.ts` - `createBusinessProfile()`
- **Validation:** `src/schemas/businessSchemas.ts` - `businessProfileSchema`

---

## Frontend Validation (Yup Schema)

```typescript
const businessProfileSchema = yup.object({
  business_name: yup.string()
    .required('Business name is required')
    .min(2, 'Business name must be at least 2 characters')
    .max(255, 'Business name must not exceed 255 characters'),
  business_type: yup.string()
    .required('Business type is required'),
  address: yup.string()
    .required('Address is required'),
  contact_number: yup.string()
    .matches(/^[0-9+\-\s()]+$/, 'Invalid phone number format')
    .optional(),
  tin_number: yup.string()
    .matches(/^[0-9\-]+$/, 'TIN must contain only numbers and hyphens')
    .optional(),
});
```

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

