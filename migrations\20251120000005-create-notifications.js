// Migration: Create notifications table - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Create notifications table with UUID primary key and ENUM type

'use strict';

export default {
  up: async (queryInterface, Sequelize) => {
    // Create ENUM type
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE enum_notifications_type AS ENUM ('info', 'success', 'warning', 'error');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryInterface.createTable('notifications', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        comment: 'Unique notification identifier (UUID v4)',
      },
      client_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'client_accounts',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Reference to user account',
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Notification title',
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Notification message content',
      },
      type: {
        type: Sequelize.ENUM('info', 'success', 'warning', 'error'),
        allowNull: false,
        defaultValue: 'info',
        comment: 'Notification type for UI styling',
      },
      is_read: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Whether notification has been read',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'JSONB field for flexible metadata',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('notifications', ['client_id'], {
      name: 'idx_notifications_client_id',
      using: 'btree',
    });

    await queryInterface.addIndex('notifications', ['is_read'], {
      name: 'idx_notifications_is_read',
      using: 'btree',
    });

    await queryInterface.addIndex('notifications', ['client_id', 'is_read'], {
      name: 'idx_notifications_client_is_read',
      using: 'btree',
    });

    await queryInterface.addIndex('notifications', ['created_at'], {
      name: 'idx_notifications_created_at',
      using: 'btree',
    });

    await queryInterface.addIndex('notifications', ['metadata'], {
      name: 'idx_notifications_metadata',
      using: 'gin',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('notifications');
    
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS enum_notifications_type;
    `);
  },
};

