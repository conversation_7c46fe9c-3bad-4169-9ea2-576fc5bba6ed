// Feedback Page - Submit Feedback and Ratings
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { Star, MessageSquare, Send, Loader2, TrendingUp } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { Select } from '@/components/ui/Select';
import { feedbackSchema, FeedbackFormData } from '@/schemas/businessSchemas';
import { feedbackService, FeedbackStats } from '@/services/feedbackService';
import { applicationService, BusinessApplication } from '@/services/applicationService';
import { useToast } from '@/hooks/useToast';

const FeedbackPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [applications, setApplications] = useState<BusinessApplication[]>([]);
  const [stats, setStats] = useState<FeedbackStats | null>(null);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [selectedRating, setSelectedRating] = useState(0);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<FeedbackFormData>({
    resolver: yupResolver(feedbackSchema),
    mode: 'onBlur',
  });

  useEffect(() => {
    fetchApplications();
    fetchStats();
  }, []);

  const fetchApplications = async () => {
    try {
      setIsFetching(true);
      const response = await applicationService.getApplications();
      // Only show submitted/approved applications
      const submittedApps = response.data.filter(
        (app) => app.status !== 'draft'
      );
      setApplications(submittedApps);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load applications',
      });
    } finally {
      setIsFetching(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await feedbackService.getFeedbackStats();
      setStats(response.data);
    } catch (error: any) {
      // Stats might not be available yet
      console.error('Failed to load stats:', error);
    }
  };

  const onSubmit = async (data: FeedbackFormData) => {
    if (selectedRating === 0) {
      toast({
        variant: 'destructive',
        title: 'Rating Required',
        description: 'Please select a rating',
      });
      return;
    }

    setIsLoading(true);
    try {
      await feedbackService.submitFeedback({
        rating: selectedRating,
        comments: data.comments,
        application_id: data.application_id || undefined,
      });

      toast({
        variant: 'success',
        title: 'Thank You!',
        description: 'Your feedback has been submitted successfully',
      });

      reset();
      setSelectedRating(0);
      fetchStats();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to submit feedback',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderStars = () => {
    return (
      <div className="flex gap-2 justify-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => {
              setSelectedRating(star);
              setValue('rating', star);
            }}
            onMouseEnter={() => setHoveredRating(star)}
            onMouseLeave={() => setHoveredRating(0)}
            className="transition-transform hover:scale-110 focus:outline-none"
            aria-label={`Rate ${star} star${star !== 1 ? 's' : ''}`}
            title={`${star} star rating`}
          >
            <Star
              className={`w-10 h-10 ${
                star <= (hoveredRating || selectedRating)
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  const renderRatingDistribution = () => {
    if (!stats || stats.total_submissions === 0) return null;

    return (
      <div className="space-y-2">
        {[5, 4, 3, 2, 1].map((rating) => {
          const count = stats.rating_distribution[rating as keyof typeof stats.rating_distribution] || 0;
          const percentage = (count / stats.total_submissions) * 100;

          return (
            <div key={rating} className="flex items-center gap-3">
              <div className="flex items-center gap-1 w-16">
                <span className="text-sm font-medium">{rating}</span>
                <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
              </div>
              <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-yellow-400 transition-all duration-500"
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span className="text-sm text-gray-600 w-12 text-right">{count}</span>
            </div>
          );
        })}
      </div>
    );
  };

  if (isFetching) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Feedback & Ratings
          </h1>
          <p className="text-gray-600 mt-1">Share your experience with our service</p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Feedback Form */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
          >
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm h-full">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  Submit Feedback
                </CardTitle>
                <CardDescription>Help us improve our service</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                  {/* Rating */}
                  <div className="space-y-3">
                    <Label className="text-center block">
                      How would you rate our service? <span className="text-red-500">*</span>
                    </Label>
                    {renderStars()}
                    {selectedRating > 0 && (
                      <p className="text-center text-sm text-gray-600">
                        {selectedRating === 5 && 'Excellent!'}
                        {selectedRating === 4 && 'Very Good!'}
                        {selectedRating === 3 && 'Good'}
                        {selectedRating === 2 && 'Fair'}
                        {selectedRating === 1 && 'Poor'}
                      </p>
                    )}
                  </div>

                  {/* Application (Optional) */}
                  <div className="space-y-2">
                    <Label htmlFor="application_id">Related Application (Optional)</Label>
                    <Select id="application_id" {...register('application_id')}>
                      <option value="">General Feedback</option>
                      {applications.map((app) => (
                        <option key={app.id} value={app.id}>
                          {app.businessProfile?.business_name} - {app.application_type}
                        </option>
                      ))}
                    </Select>
                  </div>

                  {/* Comments */}
                  <div className="space-y-2">
                    <Label htmlFor="comments">Comments (Optional)</Label>
                    <Textarea
                      id="comments"
                      {...register('comments')}
                      placeholder="Tell us more about your experience..."
                      className="min-h-[120px]"
                    />
                    {errors.comments && (
                      <p className="text-sm text-red-600">{errors.comments.message}</p>
                    )}
                  </div>

                  {/* Submit Button */}
                  <Button
                    type="submit"
                    disabled={isLoading || selectedRating === 0}
                    className="w-full gap-2"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Submit Feedback
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          {/* Statistics */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            {/* Average Rating */}
            <Card className="shadow-xl border-0 bg-gradient-to-br from-yellow-50 to-orange-50">
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-8 h-8 mx-auto text-yellow-600 mb-3" />
                <h3 className="text-sm font-medium text-gray-600 mb-2">Average Rating</h3>
                {stats && stats.total_submissions > 0 ? (
                  <>
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <span className="text-4xl font-bold text-gray-900">
                        {stats.average_rating.toFixed(1)}
                      </span>
                      <Star className="w-8 h-8 fill-yellow-400 text-yellow-400" />
                    </div>
                    <p className="text-sm text-gray-600">
                      Based on {stats.total_submissions} review{stats.total_submissions > 1 ? 's' : ''}
                    </p>
                  </>
                ) : (
                  <p className="text-gray-600">No ratings yet</p>
                )}
              </CardContent>
            </Card>

            {/* Rating Distribution */}
            {stats && stats.total_submissions > 0 && (
              <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-lg">Rating Distribution</CardTitle>
                </CardHeader>
                <CardContent>{renderRatingDistribution()}</CardContent>
              </Card>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default FeedbackPage;

