# Diagram 12: Data Flow Diagram (DFD)

## Overview
This data flow diagram shows the end-to-end data flow through the Smart Governance User Module system, from user input through frontend validation, API calls, backend processing, database operations, and response back to the user.

---

## DFD Levels
- **Level 0 (Context Diagram):** System overview with external entities
- **Level 1 (Process Diagram):** Major processes and data flows

---

## Mermaid Data Flow Diagram - Level 0 (Context Diagram)

```mermaid
graph LR
    User((User<br/>Business Owner))
    
    subgraph "Smart Governance User Module System"
        System[Smart Governance<br/>User Module<br/>Business Permit Processing]
    end
    
    EmailSys[Email System<br/>Nodemailer]
    FileStorage[File Storage<br/>Local Disk]
    
    User -->|Registration Data<br/>Login Credentials<br/>Profile Updates<br/>Business Info<br/>Applications<br/>Documents<br/>Feedback| System
    
    System -->|OTP Emails<br/>Notifications| EmailSys
    System -->|Uploaded Documents| FileStorage
    FileStorage -->|Document Files| System
    
    System -->|Authentication Status<br/>Profile Data<br/>Application Status<br/>Notifications<br/>Feedback Stats| User
    
    classDef external fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef system fill:#10b981,stroke:#059669,color:#fff
    
    class User,EmailSys,FileStorage external
    class System system
```

---

## Mermaid Data Flow Diagram - Level 1 (Process Diagram)

```mermaid
graph TB
    User((User))
    
    subgraph "Smart Governance User Module System"
        subgraph "1.0 Authentication Process"
            P1[1.1 User Registration<br/>Validate email, hash password,<br/>generate OTP]
            P2[1.2 Email Verification<br/>Verify OTP, activate account]
            P3[1.3 User Login<br/>Verify credentials, generate JWT]
        end
        
        subgraph "2.0 Profile Management Process"
            P4[2.1 View Profile<br/>Retrieve user data]
            P5[2.2 Update Profile<br/>Update name, contact]
            P6[2.3 Change Password<br/>Verify old, hash new password]
        end
        
        subgraph "3.0 Business Profile Process"
            P7[3.1 Create Business Profile<br/>Validate TIN, save business data]
            P8[3.2 View Business Profiles<br/>Retrieve user's businesses]
            P9[3.3 Edit Business Profile<br/>Update business information]
        end
        
        subgraph "4.0 Application Process"
            P10[4.1 Create Application<br/>Create draft application]
            P11[4.2 Upload Documents<br/>Validate, save files]
            P12[4.3 Submit Application<br/>Change status to submitted]
            P13[4.4 View Application Status<br/>Retrieve application list]
        end
        
        subgraph "5.0 Notification Process"
            P14[5.1 View Notifications<br/>Retrieve user notifications]
            P15[5.2 Mark as Read<br/>Update read status]
        end
        
        subgraph "6.0 Feedback Process"
            P16[6.1 Submit Feedback<br/>Save rating and comments]
            P17[6.2 View Statistics<br/>Calculate average rating]
        end
        
        subgraph "Data Stores"
            DS1[(D1: client_accounts<br/>User credentials)]
            DS2[(D2: otp_verification<br/>OTP codes)]
            DS3[(D3: business_profiles<br/>Business data)]
            DS4[(D4: business_applications<br/>Applications)]
            DS5[(D5: document_uploads<br/>Document metadata)]
            DS6[(D6: notifications<br/>User notifications)]
            DS7[(D7: feedback_submissions<br/>User feedback)]
            FS[File System<br/>uploads/documents/]
        end
    end
    
    EmailSys[Email System]
    
    %% User to Authentication
    User -->|Registration Form| P1
    P1 -->|User Data| DS1
    P1 -->|OTP Code| DS2
    P1 -->|OTP Email| EmailSys
    
    User -->|OTP Code| P2
    P2 -->|Verify OTP| DS2
    P2 -->|Update is_verified| DS1
    P2 -->|Success| User
    
    User -->|Email, Password| P3
    P3 -->|Verify Credentials| DS1
    P3 -->|JWT Token| User
    
    %% User to Profile Management
    User -->|View Request| P4
    P4 -->|Query User| DS1
    P4 -->|Profile Data| User
    
    User -->|Update Data| P5
    P5 -->|Update Record| DS1
    P5 -->|Success| User
    
    User -->|Old & New Password| P6
    P6 -->|Verify & Update| DS1
    P6 -->|Success| User
    
    %% User to Business Profile
    User -->|Business Data| P7
    P7 -->|Insert Record| DS3
    P7 -->|Business Profile| User
    
    User -->|View Request| P8
    P8 -->|Query Businesses| DS3
    P8 -->|Business List| User
    
    User -->|Update Data| P9
    P9 -->|Update Record| DS3
    P9 -->|Success| User
    
    %% User to Application
    User -->|Application Data| P10
    P10 -->|Insert Draft| DS4
    P10 -->|Create Notification| DS6
    P10 -->|Application ID| User
    
    User -->|File Upload| P11
    P11 -->|Save File| FS
    P11 -->|File Metadata| DS5
    P11 -->|Success| User
    
    User -->|Submit Request| P12
    P12 -->|Update Status| DS4
    P12 -->|Create Notification| DS6
    P12 -->|Success| User
    
    User -->|View Request| P13
    P13 -->|Query Applications| DS4
    P13 -->|Application List| User
    
    %% User to Notifications
    User -->|View Request| P14
    P14 -->|Query Notifications| DS6
    P14 -->|Notification List| User
    
    User -->|Mark Read| P15
    P15 -->|Update is_read| DS6
    P15 -->|Success| User
    
    %% User to Feedback
    User -->|Rating & Comments| P16
    P16 -->|Insert Feedback| DS7
    P16 -->|Success| User
    
    User -->|View Stats| P17
    P17 -->|Query Feedback| DS7
    P17 -->|Statistics| User
    
    %% Styling
    classDef process fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef datastore fill:#10b981,stroke:#059669,color:#fff
    classDef external fill:#f59e0b,stroke:#d97706,color:#fff
    
    class P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16,P17 process
    class DS1,DS2,DS3,DS4,DS5,DS6,DS7,FS datastore
    class User,EmailSys external
```

---

## Process Descriptions

### 1.0 Authentication Process

#### 1.1 User Registration
- **Input:** Email, password, full name, contact number
- **Processing:**
  - Validate email format
  - Check email uniqueness
  - Hash password with bcrypt (10 rounds)
  - Generate 6-digit OTP
  - Save user to database
  - Send OTP email
- **Output:** Success message, redirect to OTP verification
- **Data Stores:** D1 (client_accounts), D2 (otp_verification)

#### 1.2 Email Verification
- **Input:** Email, OTP code
- **Processing:**
  - Verify OTP code
  - Check expiration (10 minutes)
  - Update is_verified = TRUE
  - Delete OTP record
- **Output:** Success message, redirect to login
- **Data Stores:** D1 (client_accounts), D2 (otp_verification)

#### 1.3 User Login
- **Input:** Email, password
- **Processing:**
  - Find user by email
  - Verify is_verified = TRUE
  - Compare password with bcrypt
  - Generate JWT token (24h expiration)
- **Output:** JWT token, user data
- **Data Stores:** D1 (client_accounts)

---

### 2.0 Profile Management Process

#### 2.1 View Profile
- **Input:** JWT token (user ID)
- **Processing:** Query user by ID
- **Output:** User profile data
- **Data Stores:** D1 (client_accounts)

#### 2.2 Update Profile
- **Input:** Full name, contact number
- **Processing:**
  - Validate input
  - Update user record
- **Output:** Updated profile data
- **Data Stores:** D1 (client_accounts)

#### 2.3 Change Password
- **Input:** Current password, new password
- **Processing:**
  - Verify current password
  - Hash new password
  - Update password_hash
- **Output:** Success message
- **Data Stores:** D1 (client_accounts)

---

### 3.0 Business Profile Process

#### 3.1 Create Business Profile
- **Input:** Business name, type, address, contact, TIN
- **Processing:**
  - Validate TIN format
  - Validate contact number
  - Insert business profile
- **Output:** Business profile data
- **Data Stores:** D3 (business_profiles)

#### 3.2 View Business Profiles
- **Input:** JWT token (user ID)
- **Processing:** Query businesses by client_account_id
- **Output:** List of business profiles
- **Data Stores:** D3 (business_profiles)

#### 3.3 Edit Business Profile
- **Input:** Business ID, updated data
- **Processing:**
  - Verify ownership
  - Update business record
- **Output:** Updated business profile
- **Data Stores:** D3 (business_profiles)

---

### 4.0 Application Process

#### 4.1 Create Application
- **Input:** Business profile ID, application type
- **Processing:**
  - Verify business ownership
  - Create application (status: draft)
  - Create notification
- **Output:** Application ID
- **Data Stores:** D4 (business_applications), D6 (notifications)

#### 4.2 Upload Documents
- **Input:** Application ID, document type, file
- **Processing:**
  - Validate file type (PDF, JPG, PNG)
  - Validate file size (max 5MB)
  - Verify application ownership
  - Check status = draft
  - Save file to disk
  - Create document record
- **Output:** Document metadata
- **Data Stores:** D5 (document_uploads), File System

#### 4.3 Submit Application
- **Input:** Application ID
- **Processing:**
  - Verify documents uploaded
  - Update status to 'submitted'
  - Set submitted_at timestamp
  - Create notification
- **Output:** Success message
- **Data Stores:** D4 (business_applications), D6 (notifications)

#### 4.4 View Application Status
- **Input:** JWT token (user ID)
- **Processing:**
  - Query applications by user
  - Include business profile data
- **Output:** List of applications with status
- **Data Stores:** D4 (business_applications), D3 (business_profiles)

---

### 5.0 Notification Process

#### 5.1 View Notifications
- **Input:** JWT token (user ID), filter (read/unread)
- **Processing:** Query notifications by client_account_id
- **Output:** List of notifications
- **Data Stores:** D6 (notifications)

#### 5.2 Mark as Read
- **Input:** Notification ID
- **Processing:**
  - Verify ownership
  - Update is_read = TRUE
- **Output:** Success message
- **Data Stores:** D6 (notifications)

---

### 6.0 Feedback Process

#### 6.1 Submit Feedback
- **Input:** Rating (1-5), comments
- **Processing:**
  - Validate rating range
  - Insert feedback record
- **Output:** Success message
- **Data Stores:** D7 (feedback_submissions)

#### 6.2 View Statistics
- **Input:** None
- **Processing:**
  - Calculate average rating
  - Calculate rating distribution
  - Count total feedback
- **Output:** Feedback statistics
- **Data Stores:** D7 (feedback_submissions)

---

## Data Stores (7 Tables)

### D1: client_accounts
- **Purpose:** Store user credentials and profile
- **Key Fields:** id (PK), email (unique), password_hash, full_name, contact_number, is_verified

### D2: otp_verification
- **Purpose:** Store OTP codes for email verification
- **Key Fields:** id (PK), email, otp_code, expires_at

### D3: business_profiles
- **Purpose:** Store business information
- **Key Fields:** id (PK), client_account_id (FK), business_name, business_type, tin_number

### D4: business_applications
- **Purpose:** Store permit applications
- **Key Fields:** id (PK), business_profile_id (FK), application_type, status, submitted_at

### D5: document_uploads
- **Purpose:** Store document metadata
- **Key Fields:** id (PK), application_id (FK), document_type, file_path, file_name, file_size

### D6: notifications
- **Purpose:** Store user notifications
- **Key Fields:** id (PK), client_account_id (FK), type, title, message, is_read

### D7: feedback_submissions
- **Purpose:** Store user feedback
- **Key Fields:** id (PK), client_account_id (FK), rating, comments

---

## External Entities

### User (Business Owner)
- **Inputs:** Registration data, login credentials, profile updates, business info, applications, documents, feedback
- **Outputs:** Authentication status, profile data, application status, notifications, feedback stats

### Email System (Nodemailer)
- **Inputs:** OTP emails, notification emails
- **Outputs:** Email delivery status
- **Current Mode:** console.log (demo)

### File Storage (Local Disk)
- **Inputs:** Uploaded document files
- **Outputs:** Stored files, file paths
- **Location:** uploads/documents/

---

**Diagram Status:** ✅ Complete  
**DFD Levels:** 2 (Level 0 Context, Level 1 Process)  
**Processes:** 17  
**Data Stores:** 7 tables + 1 file system  
**External Entities:** 3  
**Last Updated:** November 20, 2025

