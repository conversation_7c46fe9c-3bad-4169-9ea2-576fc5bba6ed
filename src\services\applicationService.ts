// Application Service - Business Application Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import apiClient from './apiClient';
import { BusinessProfile } from './businessService';

export interface DocumentUpload {
  id: string;
  document_type: string;
  file_name: string;
  uploaded_at: string;
}

export interface BusinessApplication {
  id: string;
  business_profile_id: string;
  application_type: 'new' | 'renewal';
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  submitted_at: string | null;
  reviewed_at: string | null;
  notes: string | null;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
  businessProfile?: BusinessProfile;
  documents?: DocumentUpload[];
}

export interface CreateApplicationData {
  business_profile_id: string;
  application_type: 'new' | 'renewal';
}

export interface UpdateApplicationData {
  application_type?: 'new' | 'renewal';
}

export const applicationService = {
  /**
   * Get all applications for authenticated user
   */
  getApplications: async () => {
    const response = await apiClient.get<{ success: boolean; data: BusinessApplication[] }>(
      '/applications'
    );
    return response.data;
  },

  /**
   * Get single application by ID
   */
  getApplicationById: async (id: string) => {
    const response = await apiClient.get<{ success: boolean; data: BusinessApplication }>(
      `/applications/${id}`
    );
    return response.data;
  },

  /**
   * Create new application
   */
  createApplication: async (data: CreateApplicationData) => {
    const response = await apiClient.post<{ success: boolean; message: string; data: BusinessApplication }>(
      '/applications',
      data
    );
    return response.data;
  },

  /**
   * Update application (draft only)
   */
  updateApplication: async (id: string, data: UpdateApplicationData) => {
    const response = await apiClient.put<{ success: boolean; message: string; data: BusinessApplication }>(
      `/applications/${id}`,
      data
    );
    return response.data;
  },

  /**
   * Submit application for review
   */
  submitApplication: async (id: string) => {
    const response = await apiClient.put<{ success: boolean; message: string; data: BusinessApplication }>(
      `/applications/${id}/submit`
    );
    return response.data;
  },
};

