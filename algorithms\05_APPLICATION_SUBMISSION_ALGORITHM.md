# Algorithm 5: Business Permit Application Submission

## Overview
This algorithm handles the complete workflow of creating and submitting business permit applications, including validation, status management, and notification creation.

---

## Algorithm Complexity
- **Time Complexity:** O(1) - Constant time for database operations
- **Space Complexity:** O(1) - Fixed size data structures
- **Database Operations:** 2-3 queries (INSERT application, UPDATE status, INSERT notification)

---

## Input Parameters
```typescript
interface ApplicationSubmissionInput {
  client_account_id: UUID;        // From JWT token
  business_profile_id: UUID;      // Selected business profile
  application_type: 'new' | 'renewal';
  notes?: string;                 // Optional application notes
}
```

## Output
```typescript
interface ApplicationSubmissionOutput {
  success: boolean;
  message: string;
  application?: {
    id: UUID;
    business_profile_id: UUID;
    application_type: string;
    status: string;
    created_at: Date;
  };
}
```

---

## Pseudocode

```
ALGORITHM SubmitApplication(client_account_id, business_profile_id, application_type, notes)
BEGIN
  // Step 1: Authentication Check
  IF client_account_id is empty THEN
    RETURN error(401, "Authentication required")
  END IF
  
  // Step 2: Validate Business Profile Ownership
  business_profile ← DATABASE.query(
    "SELECT * FROM business_profiles WHERE id = ? AND client_account_id = ?",
    business_profile_id, client_account_id
  )
  
  IF business_profile NOT EXISTS THEN
    RETURN error(404, "Business profile not found or access denied")
  END IF
  
  // Step 3: Validate Application Type
  IF application_type NOT IN ['new', 'renewal'] THEN
    RETURN error("Invalid application type. Must be 'new' or 'renewal'")
  END IF
  
  // Step 4: Create Application (Draft Status)
  application_id ← UUID.generate_v4()
  
  application ← DATABASE.insert("business_applications", {
    id: application_id,
    business_profile_id: business_profile_id,
    application_type: application_type,
    status: 'draft',
    notes: notes OR NULL,
    metadata: {},
    created_at: CURRENT_TIMESTAMP,
    updated_at: CURRENT_TIMESTAMP
  })
  
  // Step 5: Create Notification
  notification_id ← UUID.generate_v4()
  
  DATABASE.insert("notifications", {
    id: notification_id,
    client_account_id: client_account_id,
    type: 'info',
    title: 'Application Created',
    message: 'Your ' + application_type + ' permit application has been created. Please upload required documents.',
    is_read: FALSE,
    metadata: { application_id: application_id },
    created_at: CURRENT_TIMESTAMP
  })
  
  // Step 6: Return Success Response
  RETURN success({
    message: "Application created successfully. Please upload required documents.",
    application: application
  })
END

ALGORITHM SubmitApplicationForReview(application_id, client_account_id)
BEGIN
  // Step 1: Find Application
  application ← DATABASE.query(
    "SELECT a.*, bp.client_account_id 
     FROM business_applications a
     JOIN business_profiles bp ON a.business_profile_id = bp.id
     WHERE a.id = ?",
    application_id
  )
  
  IF application NOT EXISTS THEN
    RETURN error(404, "Application not found")
  END IF
  
  // Step 2: Verify Ownership
  IF application.client_account_id ≠ client_account_id THEN
    RETURN error(403, "Access denied")
  END IF
  
  // Step 3: Check Current Status
  IF application.status ≠ 'draft' THEN
    RETURN error("Application already submitted")
  END IF
  
  // Step 4: Verify Documents Uploaded
  document_count ← DATABASE.count(
    "SELECT COUNT(*) FROM document_uploads WHERE application_id = ?",
    application_id
  )
  
  IF document_count = 0 THEN
    RETURN error("Please upload at least one document before submitting")
  END IF
  
  // Step 5: Update Application Status
  DATABASE.update("business_applications", {
    status: 'submitted',
    submitted_at: CURRENT_TIMESTAMP,
    updated_at: CURRENT_TIMESTAMP
  }, WHERE id = application_id)
  
  // Step 6: Create Notification
  notification_id ← UUID.generate_v4()
  
  DATABASE.insert("notifications", {
    id: notification_id,
    client_account_id: client_account_id,
    type: 'success',
    title: 'Application Submitted',
    message: 'Your application has been submitted for review. You will be notified of any updates.',
    is_read: FALSE,
    metadata: { application_id: application_id },
    created_at: CURRENT_TIMESTAMP
  })
  
  // Step 7: Return Success Response
  RETURN success({
    message: "Application submitted successfully",
    application: updated_application
  })
END
```

---

## Flowchart (Create Application)

```mermaid
flowchart TD
    Start([Start: Create Application]) --> Auth{User<br/>Authenticated?}
    Auth -->|No| ErrorAuth[Return Error 401:<br/>Authentication required]
    Auth -->|Yes| Input[Receive: business_profile_id,<br/>application_type, notes]
    Input --> ValidateOwnership[Query: Verify business<br/>profile ownership]
    ValidateOwnership --> OwnershipCheck{User Owns<br/>Business<br/>Profile?}
    OwnershipCheck -->|No| ErrorOwnership[Return Error 404:<br/>Profile not found]
    OwnershipCheck -->|Yes| ValidateType{Application Type<br/>Valid?}
    ValidateType -->|No| ErrorType[Return Error:<br/>Invalid type]
    ValidateType -->|Yes| GenerateUUID[Generate UUID<br/>for application_id]
    GenerateUUID --> InsertApp[Insert into<br/>business_applications<br/>status = 'draft']
    InsertApp --> CreateNotif[Create Notification:<br/>Application Created]
    CreateNotif --> Success[Return Success:<br/>Application created]
    Success --> End([End])
    ErrorAuth --> End
    ErrorOwnership --> End
    ErrorType --> End
```

---

## Flowchart (Submit for Review)

```mermaid
flowchart TD
    Start([Start: Submit Application]) --> FindApp[Query: Find application<br/>with business profile join]
    FindApp --> AppExists{Application<br/>Found?}
    AppExists -->|No| ErrorNotFound[Return Error 404:<br/>Application not found]
    AppExists -->|Yes| CheckOwner{User Owns<br/>Application?}
    CheckOwner -->|No| ErrorAccess[Return Error 403:<br/>Access denied]
    CheckOwner -->|Yes| CheckStatus{Status is<br/>'draft'?}
    CheckStatus -->|No| ErrorSubmitted[Return Error:<br/>Already submitted]
    CheckStatus -->|Yes| CountDocs[Query: Count<br/>uploaded documents]
    CountDocs --> HasDocs{Documents<br/>Uploaded?}
    HasDocs -->|No| ErrorNoDocs[Return Error:<br/>Upload documents first]
    HasDocs -->|Yes| UpdateStatus[Update status to 'submitted'<br/>Set submitted_at timestamp]
    UpdateStatus --> CreateNotif[Create Notification:<br/>Application Submitted]
    CreateNotif --> Success[Return Success:<br/>Application submitted]
    Success --> End([End])
    ErrorNotFound --> End
    ErrorAccess --> End
    ErrorSubmitted --> End
    ErrorNoDocs --> End
```

---

## Application Status Flow

```
draft → submitted → under_review → approved/rejected
```

### Status Definitions
- **draft:** Application created, documents being uploaded
- **submitted:** Application submitted for review, awaiting admin action
- **under_review:** Admin is reviewing the application (admin feature)
- **approved:** Application approved, permit issued (admin feature)
- **rejected:** Application rejected, reason provided (admin feature)

---

## Database Tables Involved

### business_applications
- **Operations:** INSERT, UPDATE, SELECT
- **Fields:** id, business_profile_id, application_type, status, submitted_at, notes, metadata

### business_profiles
- **Operations:** SELECT (for ownership verification)
- **Fields:** id, client_account_id

### document_uploads
- **Operations:** COUNT (for submission validation)
- **Fields:** application_id

### notifications
- **Operations:** INSERT
- **Fields:** id, client_account_id, type, title, message, is_read, metadata

---

## Implementation Files

- **Controller:** `controllers/applicationController.js`
- **Model:** `models/BusinessApplication.js`
- **Routes:** `routes/applications.js`
- **Frontend:** `src/pages/ApplicationFormPage.tsx`, `src/pages/ApplicationStatusPage.tsx`
- **Service:** `src/services/applicationService.ts`

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

