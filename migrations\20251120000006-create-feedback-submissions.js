// Migration: Create feedback_submissions table - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Create feedback_submissions table with UUID primary key and rating validation

'use strict';

export default {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('feedback_submissions', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        comment: 'Unique feedback identifier (UUID v4)',
      },
      client_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'client_accounts',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Reference to user account',
      },
      application_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'business_applications',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'Optional reference to related application',
      },
      rating: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'Rating from 1 to 5 stars',
      },
      comments: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'User feedback comments',
      },
      submitted_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: 'Timestamp when feedback was submitted',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'JSONB field for flexible metadata',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add CHECK constraint for rating (1-5)
    await queryInterface.sequelize.query(`
      ALTER TABLE feedback_submissions
      ADD CONSTRAINT check_rating_range CHECK (rating >= 1 AND rating <= 5);
    `);

    // Create indexes
    await queryInterface.addIndex('feedback_submissions', ['client_id'], {
      name: 'idx_feedback_submissions_client_id',
      using: 'btree',
    });

    await queryInterface.addIndex('feedback_submissions', ['application_id'], {
      name: 'idx_feedback_submissions_application_id',
      using: 'btree',
    });

    await queryInterface.addIndex('feedback_submissions', ['rating'], {
      name: 'idx_feedback_submissions_rating',
      using: 'btree',
    });

    await queryInterface.addIndex('feedback_submissions', ['submitted_at'], {
      name: 'idx_feedback_submissions_submitted_at',
      using: 'btree',
    });

    await queryInterface.addIndex('feedback_submissions', ['metadata'], {
      name: 'idx_feedback_submissions_metadata',
      using: 'gin',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('feedback_submissions');
  },
};

