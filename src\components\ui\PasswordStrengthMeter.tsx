import React from 'react'
import { calculatePasswordStrength } from '@/lib/utils'
import { motion } from 'framer-motion'

interface PasswordStrengthMeterProps {
  password: string
}

export const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({ password }) => {
  const strength = calculatePasswordStrength(password)

  if (!password) return null

  return (
    <div className="mt-2 space-y-2" role="status" aria-live="polite">
      <div className="flex items-center justify-between text-xs">
        <span className="text-muted-foreground">Password strength:</span>
        <span className={`font-medium ${
          strength.score <= 2 ? 'text-red-500' : 
          strength.score <= 4 ? 'text-yellow-500' : 
          'text-green-500'
        }`}>
          {strength.label}
        </span>
      </div>
      <div className="h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <motion.div
          className={`h-full ${strength.color} transition-all duration-300`}
          initial={{ width: 0 }}
          animate={{ width: `${strength.percentage}%` }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
          aria-valuenow={strength.percentage}
          aria-valuemin={0}
          aria-valuemax={100}
        />
      </div>
      <div className="text-xs text-muted-foreground space-y-1">
        <p className="flex items-center gap-1">
          <span className={password.length >= 8 ? 'text-green-500' : 'text-gray-400'}>
            {password.length >= 8 ? '✓' : '○'}
          </span>
          At least 8 characters
        </p>
        <p className="flex items-center gap-1">
          <span className={/[A-Z]/.test(password) ? 'text-green-500' : 'text-gray-400'}>
            {/[A-Z]/.test(password) ? '✓' : '○'}
          </span>
          One uppercase letter
        </p>
        <p className="flex items-center gap-1">
          <span className={/[a-z]/.test(password) ? 'text-green-500' : 'text-gray-400'}>
            {/[a-z]/.test(password) ? '✓' : '○'}
          </span>
          One lowercase letter
        </p>
        <p className="flex items-center gap-1">
          <span className={/[0-9]/.test(password) ? 'text-green-500' : 'text-gray-400'}>
            {/[0-9]/.test(password) ? '✓' : '○'}
          </span>
          One number
        </p>
        <p className="flex items-center gap-1">
          <span className={/[^a-zA-Z0-9]/.test(password) ? 'text-green-500' : 'text-gray-400'}>
            {/[^a-zA-Z0-9]/.test(password) ? '✓' : '○'}
          </span>
          One special character
        </p>
      </div>
    </div>
  )
}

