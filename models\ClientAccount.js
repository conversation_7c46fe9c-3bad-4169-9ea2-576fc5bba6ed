import bcrypt from 'bcrypt';
import crypto from 'crypto';

export default (sequelize, DataTypes) => {
  const ClientAccount = sequelize.define('ClientAccount', {
    // Primary key: UUID v4 for secure identifiers (PostgreSQL native support)
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique user identifier (UUID v4)',
    },
    // Email with format validation and uniqueness
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: {
        msg: 'Email address is already registered',
      },
      validate: {
        isEmail: {
          msg: 'Please enter a valid email address',
        },
        notEmpty: {
          msg: 'Email is required',
        },
      },
      comment: 'User email for login',
    },
    // Password hash (never store plain text)
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'bcrypt-hashed password',
    },
    // Full name with length validation
    full_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: {
          args: [2, 255],
          msg: 'Full name must be between 2 and 255 characters',
        },
        notEmpty: {
          msg: 'Full name is required',
        },
      },
      comment: 'User\'s full name',
    },
    // Required contact number
    contact_number: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Contact number is required',
        },
        is: {
          args: /^(\+63|0)[0-9]{10}$/,
          msg: 'Please enter a valid Philippine mobile number (e.g., +639xxxxxxxxx or 09xxxxxxxxx)',
        },
      },
      comment: 'Required Philippine mobile number',
    },
    // Verification status
    is_verified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Email/OTP verification status',
    },
    // PostgreSQL JSONB field for flexible future extensions
    metadata: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
      comment: 'JSONB field for flexible metadata (notifications, preferences, etc.)',
    },
  }, {
    // Table configuration
    tableName: 'client_accounts',
    timestamps: true, // Enables createdAt and updatedAt fields
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Email search index for fast login queries
      {
        fields: ['email'],
        name: 'idx_client_accounts_email_search',
        using: 'btree', // PostgreSQL-specific index type
      },
      // Index for filtering verified users
      {
        fields: ['is_verified'],
        name: 'idx_client_accounts_is_verified',
        using: 'btree',
      },
      // Index for user analytics (PostgreSQL advantage)
      {
        fields: ['created_at'],
        name: 'idx_client_accounts_created_at',
        using: 'btree',
      },
      // GIN index for JSONB metadata queries (PostgreSQL-specific)
      {
        fields: ['metadata'],
        name: 'idx_client_accounts_metadata',
        using: 'gin',
      },
    ],
    hooks: {
      // Hash password before creating user
      beforeCreate: async (user) => {
        if (user.password_hash && !user.password_hash.startsWith('$2a$') && !user.password_hash.startsWith('$2b$') && !user.password_hash.startsWith('$2y$')) {
          // Hash the password with cost factor 10 (OWASP recommended)
          const saltRounds = 10;
          user.password_hash = await bcrypt.hash(user.password_hash, saltRounds);
        }
      },
      // Hash password before updating user
      beforeUpdate: async (user) => {
        if (user.changed('password_hash') && user.password_hash && !user.password_hash.startsWith('$2a$') && !user.password_hash.startsWith('$2b$') && !user.password_hash.startsWith('$2y$')) {
          // Hash the password with cost factor 10
          const saltRounds = 10;
          user.password_hash = await bcrypt.hash(user.password_hash, saltRounds);
        }
      },
    },
  });

  // Instance method to validate password during login
  ClientAccount.prototype.validatePassword = async function(plainPassword) {
    return await bcrypt.compare(plainPassword, this.password_hash);
  };

  // Instance method to generate password reset token (for future use)
  ClientAccount.prototype.generatePasswordResetToken = function() {
    // Generate a secure random token using PostgreSQL pgcrypto if available
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  };

  // Instance method to update metadata (JSONB operations)
  ClientAccount.prototype.updateMetadata = async function(key, value) {
    const metadata = { ...this.metadata };
    metadata[key] = value;
    return await this.update({ metadata });
  };

  // Static method to find user by email for login
  ClientAccount.findByEmail = async function(email) {
    return await this.findOne({
      where: { email: email.toLowerCase() },
    });
  };

  // Static method to create verified user (for admin use)
  ClientAccount.createVerifiedUser = async function(userData) {
    return await this.create({
      ...userData,
      is_verified: true,
    });
  };

  // Static method to get user statistics with PostgreSQL JSON aggregation
  ClientAccount.getStats = async function() {
    const totalUsers = await this.count();
    const verifiedUsers = await this.count({ where: { is_verified: true } });
    const unverifiedUsers = totalUsers - verifiedUsers;

    return {
      total: totalUsers,
      verified: verifiedUsers,
      unverified: unverifiedUsers,
      verificationRate: totalUsers > 0 ? (verifiedUsers / totalUsers * 100).toFixed(2) : 0,
    };
  };

  // Static method to search users by metadata (PostgreSQL JSONB advantage)
  ClientAccount.findByMetadata = async function(key, value) {
    return await this.findAll({
      where: sequelize.where(
        sequelize.json(`metadata.${key}`),
        value
      ),
    });
  };

  return ClientAccount;
};
