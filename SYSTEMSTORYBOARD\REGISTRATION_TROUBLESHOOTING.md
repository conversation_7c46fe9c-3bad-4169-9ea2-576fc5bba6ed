# TROUBLESHOOTING DECISION TREE

## 🎯 START HERE: Is Registration Working?

```
Does the registration form load?
│
├─ NO → Go to [Step 1: Frontend Not Loading]
│
└─ YES → Can you submit the form?
    │
    ├─ NO → Go to [Step 2: Form Won't Submit]
    │
    └─ YES → What happens after you click "Create Account"?
        │
        ├─ Error Toast appears → Go to [Step 3: Error Response]
        │
        ├─ Loading spins forever → Go to [Step 4: Timeout/No Response]
        │
        └─ Success Toast & Redirects to Dashboard → ✅ ALL WORKING!
```

---

## STEP 1: Frontend Not Loading

**Problem:** Registration page shows blank, error, or old version

### Check 1: Frontend Server Running?
```bash
# Check if Vite is still running
# Terminal 2 should show: "VITE v5.0.7 ready in XXX ms"
# and "Local: http://localhost:5173/"

# If NOT running:
npm run dev
```

**Result:** ✅ If you see the page now, issue is SOLVED

### Check 2: Cache Issue?
```
1. Hard refresh: Ctrl+Shift+Delete (or Cmd+Shift+Delete on Mac)
2. Clear localStorage: Open DevTools → Application → LocalStorage → Clear All
3. Reload: F5
```

**Result:** ✅ If page loads, issue is SOLVED

### Check 3: Port 5173 Conflict?
```bash
# Find what's using port 5173
netstat -ano | findstr :5173

# Kill the process
taskkill /PID <PID> /F

# Restart frontend
npm run dev
```

**Result:** ✅ If frontend starts, issue is SOLVED

**If None Work:** Check Node.js console for errors → Contact support

---

## STEP 2: Form Won't Submit

**Problem:** Click button but nothing happens, or validation stuck

### Check 1: Form Validation Errors?
```
Each field should show error messages if invalid.

Check your values:
- Email: Must be valid format (<EMAIL>)
- Full Name: 2+ characters (e.g., John Doe)
- Contact: Must be Philippine format (e.g., +************ OR ***********)
- Password: 8+ chars with uppercase, lowercase, number, special char
  Example: TestPass123!
- Confirm: Must match password exactly

Fix any red error messages and try again.
```

### Check 2: Button Click Registering?
```
1. Open DevTools: F12
2. Go to Console tab
3. Try submitting form again
4. Look for JavaScript errors (red text)

If you see errors → Report them to developer
If no errors but button doesn't work → Check browser console for permission issues
```

**Result:** ✅ If form submits, move to Step 3

### Check 3: Browser Compatibility?
```
Supported Browsers:
✅ Chrome 90+
✅ Firefox 88+
✅ Safari 14+
✅ Edge 90+

Try in different browser if having issues.
```

**Result:** ✅ If works in other browser, use that

**If Still Not Working:** Form might be disabled → Check Step 4

---

## STEP 3: Error Response from Backend

**Problem:** Form submits, then error toast appears

### Possible Error Messages:

#### Error: "User with this email already exists"
```
You already registered this email.

Solutions:
1. Use different email address
2. Or reset test data in database:
   
   psql -U postgres -d smart_governance_auth
   DELETE FROM client_accounts WHERE email = '<EMAIL>';
```

#### Error: "Validation failed"
```
One or more fields didn't pass server validation.

Check:
- Email: Valid format? (<EMAIL>)
- Password: 8+ characters?
- Contact: Correct format? (+************ or ***********)

To see detailed errors:
1. Open DevTools: F12
2. Go to Network tab
3. Click POST /api/auth/register request
4. Look at Response tab
5. It will show which field failed validation
```

#### Error: "Unable to connect to server"
```
Frontend can't reach backend API.

Check:
1. Is backend running?
   Terminal 1 should show: "🚀 Smart Governance Auth API Server running on port 3001"

2. Is port 3001 accessible?
   curl http://localhost:3001/health
   (Should return JSON with "status: healthy")

3. Is .env configured correctly?
   Check .env file has: VITE_API_URL=http://localhost:3001/api

If all check out but still failing:
- Firewall might be blocking port 3001
- Try: Windows Firewall → Allow app through firewall → check Node.js
```

#### Error: "Registration failed. Please try again."
```
Backend error occurred.

Check Backend Terminal:
1. Look at Terminal 1 (backend)
2. Find "Registration error: ..." message
3. Report the error message to developer

Common backend errors:
- "Password validation error" → Check password strength
- "Email already exists" → Use different email
- "Database connection error" → PostgreSQL not running
- "JWT error" → Restart server (server.js)
```

#### Error: "Request timeout"
```
Server took too long to respond.

Check:
1. Is backend running?
2. Is database responding? (check PostgreSQL is running)
3. Try again (might be temporary network issue)
4. If persists: Restart backend
   - Terminal 1: Press Ctrl+C
   - Then: node server.js
```

### Debug Action:
```
1. Open DevTools: F12
2. Click Network tab
3. Try registration again
4. Find POST /api/auth/register
5. Click it
6. Check:
   - Request tab: What data was sent?
   - Response tab: What error came back?
   - Status: Is it 201 (success), 400 (validation), 409 (duplicate), 500 (server error)?
```

**Result:** ✅ If you understand error, try to fix and resubmit

---

## STEP 4: Timeout or No Response

**Problem:** Form submits, loading spins forever, then timeout error

### Check 1: Backend Running?
```bash
# In new terminal, run:
curl http://localhost:3001/health

# Should return immediately:
{
  "status": "healthy",
  "timestamp": "...",
  "database": "connected",
  "uptime": ...
}

If NO response → Backend not running
If ERROR → Database not connected
```

### Check 2: Network Tab
```
1. Open DevTools: F12
2. Click Network tab
3. Try registration
4. Look for POST /api/auth/register
5. Check:
   - Is request pending? (spinning circle)
   - Check how long it takes
   - Click request → Response tab
   - Is there any error message?
```

### Check 3: Backend Terminal
```
Look at Terminal 1 (backend):
- Is it still showing "running on port 3001"?
- Are there any error messages?
- Try scrolling up to see if there was a crash

If backend crashed:
1. Press Ctrl+C in Terminal 1
2. Run: node server.js again
3. Watch for error messages
```

### Check 4: Database Connection
```bash
# Verify database is running and accessible:
psql -U postgres

# Should connect without errors
# Then exit: \q

If error:
- PostgreSQL not running
- Wrong username/password
- Check .env for DATABASE credentials
```

### Check 5: Port Blocking
```bash
# Make sure port 3001 is not blocked:
netstat -ano | findstr :3001

# Should show Node.js process using it
# If not, backend is not listening

# Check firewall:
# Windows Firewall → Allow app through firewall → Look for Node.js
```

**Result:** ✅ If you identify the issue, fix and try again

---

## STEP 5: Success! User Created

**Problem:** Registration worked! But want to verify...

### Verify in Database:
```bash
# Connect to database
psql -U postgres -d smart_governance_auth

# Check user was created
SELECT email, full_name, contact_number, is_verified, created_at
FROM client_accounts
WHERE email = '<EMAIL>';

# Should return:
# email | <EMAIL>
# full_name | Your Name
# contact_number | +************
# is_verified | true (should be TRUE for auto-verification)
# created_at | 2025-11-23 ...
```

### Verify in Browser:
```
After successful registration, you should:
1. See green success toast: "Registration Successful!"
2. Get redirected to dashboard (automatic)
3. See "Welcome back, [Your Name]!"
4. See your email displayed
5. See "Verified ✓" status

If you don't see dashboard:
- Check URL: should be http://localhost:5173/dashboard
- Check localStorage: F12 → Application → LocalStorage → Look for "authToken" and "user"
```

### Test Further:
```
1. Go to other pages (Notifications, Profile, etc.)
2. Try logout → Should redirect to login
3. Try login again with same email → Should work
4. Password must be exactly as you created it
```

**Result:** ✅ Registration system is fully functional!

---

## 🔧 QUICK FIXES REFERENCE

| Issue | Quick Fix |
|-------|-----------|
| Backend not running | `node server.js` in Terminal 1 |
| Frontend not loading | `npm run dev` in Terminal 2 |
| Port 3001 in use | `netstat -ano \| findstr :3001` then `taskkill /PID <PID> /F` |
| Database error | Ensure PostgreSQL is running, check credentials in `.env` |
| Validation fails | Check field values match requirements (see validation rules) |
| Timeout error | Restart backend server |
| CORS error | Check backend `.env` FRONTEND_URL setting |
| Token not storing | Check localStorage not full, try clearing it |
| Can't login after | Use exact same email/password as registration |

---

## 📞 NEED MORE HELP?

### Information to Provide:

1. **What error do you see?**
   - Exact error message (take screenshot)
   - Toast message content
   - Browser console error (F12 → Console)

2. **Browser details:**
   - Browser name & version
   - Operating system

3. **Terminal output:**
   - Screenshot of both Terminal 1 (backend) and Terminal 2 (frontend)
   - Any error messages

4. **Network details:**
   - Screenshot of Network tab (F12)
   - POST /api/auth/register status code & response

5. **What you've already tried:**
   - Restart backend? ✓/✗
   - Restart frontend? ✓/✗
   - Clear cache & localStorage? ✓/✗
   - Checked database connection? ✓/✗

---

## ✅ FINAL CHECKLIST

Before concluding registration is working:

- [ ] Form loads at http://localhost:5173/register
- [ ] All validation works (shows error messages)
- [ ] Can submit form with valid data
- [ ] Get success toast notification
- [ ] Redirected to http://localhost:5173/dashboard
- [ ] Dashboard shows "Welcome back, [Name]!"
- [ ] User data persists in localStorage
- [ ] Can logout and login again
- [ ] User exists in database with is_verified = true
- [ ] Contact number saved correctly

**If all ✅:** Registration system is fully operational!

---

**Created:** November 23, 2025  
**Last Updated:** November 23, 2025  
**Version:** 1.0
