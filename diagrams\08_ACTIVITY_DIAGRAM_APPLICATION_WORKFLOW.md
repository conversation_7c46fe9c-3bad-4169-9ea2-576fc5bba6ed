# Diagram 8: Activity Diagram - Application Workflow

## Overview
This activity diagram shows the complete application workflow process with status transitions from draft creation through submission, review, and final approval or rejection, including all decision points and user actions.

---

## Status Flow
**draft** → **submitted** → **under_review** → **approved** OR **rejected**

**User Module Scope:** draft → submitted (User actions only)  
**Admin Module Scope:** submitted → under_review → approved/rejected (Out of scope)

---

## Mermaid Activity Diagram

```mermaid
flowchart TD
    Start([User Starts Application Process]) --> CheckBP{Has Business<br/>Profile?}
    
    CheckBP -->|No| CreateBP[Create Business Profile]
    CreateBP --> SelectBP
    CheckBP -->|Yes| SelectBP[Select Business Profile]
    
    SelectBP --> ChooseType{Choose<br/>Application Type}
    ChooseType -->|New Permit| CreateNew[Create New Application<br/>Status: draft]
    ChooseType -->|Renewal| CreateRenewal[Create Renewal Application<br/>Status: draft]
    
    CreateNew --> NotifCreated[Notification: Application Created]
    CreateRenewal --> NotifCreated
    
    NotifCreated --> RedirectDoc[Redirect to Document Upload Page]
    
    RedirectDoc --> UploadLoop{Upload<br/>Documents?}
    
    UploadLoop -->|Yes| SelectDocType[Select Document Type<br/>DTI, Barangay Clearance, etc.]
    SelectDocType --> ChooseFile[Choose File<br/>PDF, JPG, or PNG]
    ChooseFile --> ValidateFile{File Valid?<br/>Type & Size}
    
    ValidateFile -->|No| ErrorFile[Display Error:<br/>Invalid file type or size]
    ErrorFile --> UploadLoop
    
    ValidateFile -->|Yes| SaveFile[Save File to Disk<br/>uploads/documents/]
    SaveFile --> CreateDocRecord[Create Document Record<br/>in Database]
    CreateDocRecord --> UploadLoop
    
    UploadLoop -->|No More| CheckDocs{At Least One<br/>Document?}
    
    CheckDocs -->|No| ErrorNoDocs[Display Error:<br/>Upload at least one document]
    ErrorNoDocs --> UploadLoop
    
    CheckDocs -->|Yes| ReviewDocs[User Reviews Application<br/>and Documents]
    
    ReviewDocs --> ConfirmSubmit{Confirm<br/>Submission?}
    
    ConfirmSubmit -->|No| MoreActions{What to do?}
    MoreActions -->|Upload More| UploadLoop
    MoreActions -->|Edit Application| EditApp[Edit Application Details]
    EditApp --> ReviewDocs
    MoreActions -->|Delete Document| DeleteDoc[Delete Document<br/>Only in draft status]
    DeleteDoc --> ReviewDocs
    MoreActions -->|Cancel| SaveDraft[Save as Draft<br/>Exit]
    SaveDraft --> End1([End: Draft Saved])
    
    ConfirmSubmit -->|Yes| UpdateStatus[Update Status: submitted<br/>Set submitted_at timestamp]
    
    UpdateStatus --> NotifSubmitted[Notification: Application Submitted<br/>Type: success]
    
    NotifSubmitted --> LockApp[Lock Application<br/>No more edits allowed]
    
    LockApp --> WaitReview[Wait for Admin Review<br/>Status: submitted]
    
    WaitReview -.->|Admin Action| AdminReview{Admin Reviews<br/>Application}
    
    AdminReview -.->|Start Review| UpdateUnderReview[Update Status: under_review<br/>Admin Module - Out of Scope]
    
    UpdateUnderReview -.->|Admin Decision| AdminDecision{Approve or<br/>Reject?}
    
    AdminDecision -.->|Approve| UpdateApproved[Update Status: approved<br/>Set reviewed_at timestamp<br/>Admin Module - Out of Scope]
    
    UpdateApproved -.-> NotifApproved[Notification: Application Approved<br/>Type: success<br/>Admin Module - Out of Scope]
    
    NotifApproved -.-> End2([End: Application Approved])
    
    AdminDecision -.->|Reject| UpdateRejected[Update Status: rejected<br/>Set reviewed_at timestamp<br/>Add rejection notes<br/>Admin Module - Out of Scope]
    
    UpdateRejected -.-> NotifRejected[Notification: Application Rejected<br/>Type: error<br/>Include rejection reason<br/>Admin Module - Out of Scope]
    
    NotifRejected -.-> UserNotified[User Receives Rejection<br/>Notification]
    
    UserNotified -.-> NewApp{Create New<br/>Application?}
    
    NewApp -.->|Yes| SelectBP
    NewApp -.->|No| End3([End: Application Rejected])
    
    %% Styling
    classDef userAction fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef systemAction fill:#10b981,stroke:#059669,color:#fff
    classDef adminAction fill:#f59e0b,stroke:#d97706,color:#fff
    classDef decision fill:#8b5cf6,stroke:#7c3aed,color:#fff
    classDef error fill:#ef4444,stroke:#dc2626,color:#fff
    
    class CreateBP,SelectBP,CreateNew,CreateRenewal,SelectDocType,ChooseFile,ReviewDocs,EditApp,DeleteDoc userAction
    class NotifCreated,SaveFile,CreateDocRecord,UpdateStatus,NotifSubmitted,LockApp,WaitReview systemAction
    class UpdateUnderReview,UpdateApproved,NotifApproved,UpdateRejected,NotifRejected adminAction
    class CheckBP,ChooseType,UploadLoop,ValidateFile,CheckDocs,ConfirmSubmit,MoreActions,AdminReview,AdminDecision,NewApp decision
    class ErrorFile,ErrorNoDocs error
```

---

## Status Definitions

### 1. draft (User Module)
- **Description:** Application created but not submitted
- **User Can:**
  - ✅ Upload documents
  - ✅ Delete documents
  - ✅ Edit application details
  - ✅ Submit for review
- **User Cannot:**
  - ❌ View admin review notes

### 2. submitted (User Module)
- **Description:** Application submitted for admin review
- **User Can:**
  - ✅ View application status
  - ✅ View uploaded documents
- **User Cannot:**
  - ❌ Edit application
  - ❌ Upload/delete documents
  - ❌ Cancel submission

### 3. under_review (Admin Module - Out of Scope)
- **Description:** Admin is actively reviewing the application
- **Admin Can:**
  - ✅ View all application details
  - ✅ View all documents
  - ✅ Add review notes
  - ✅ Approve or reject
- **User Can:**
  - ✅ View status only

### 4. approved (Admin Module - Out of Scope)
- **Description:** Application approved, permit issued
- **User Can:**
  - ✅ View approval notification
  - ✅ View application details
  - ✅ Download permit (future feature)
- **User Cannot:**
  - ❌ Edit application

### 5. rejected (Admin Module - Out of Scope)
- **Description:** Application rejected with reason
- **User Can:**
  - ✅ View rejection notification
  - ✅ View rejection reason
  - ✅ Create new application
- **User Cannot:**
  - ❌ Resubmit same application
  - ❌ Edit rejected application

---

## Decision Points

### 1. Has Business Profile?
- **Yes:** Proceed to select business profile
- **No:** Must create business profile first

### 2. Choose Application Type
- **New Permit:** For first-time business permit
- **Renewal:** For renewing existing permit

### 3. File Valid?
- **Yes:** Save file and create record
- **No:** Display error, allow retry

### 4. At Least One Document?
- **Yes:** Allow submission
- **No:** Require document upload

### 5. Confirm Submission?
- **Yes:** Submit application
- **No:** Continue editing or save as draft

### 6. Admin Decision (Out of Scope)
- **Approve:** Issue permit
- **Reject:** Provide reason

---

## Notifications Created

### 1. Application Created
- **Type:** info
- **Title:** "Application Created"
- **Message:** "Your draft application has been created. Please upload required documents."

### 2. Application Submitted
- **Type:** success
- **Title:** "Application Submitted"
- **Message:** "Your application has been submitted for review. You will be notified of the review status."

### 3. Application Approved (Admin - Out of Scope)
- **Type:** success
- **Title:** "Application Approved"
- **Message:** "Congratulations! Your application has been approved. Your permit is ready."

### 4. Application Rejected (Admin - Out of Scope)
- **Type:** error
- **Title:** "Application Rejected"
- **Message:** "Your application has been rejected. Reason: {rejection_notes}"

---

## Database Operations

### Create Application (draft)
```sql
INSERT INTO business_applications (
  id, business_profile_id, application_type, status, created_at, updated_at
) VALUES (
  uuid_generate_v4(), ?, ?, 'draft', NOW(), NOW()
);
```

### Submit Application
```sql
UPDATE business_applications
SET status = 'submitted', submitted_at = NOW(), updated_at = NOW()
WHERE id = ? AND status = 'draft';
```

### Approve Application (Admin - Out of Scope)
```sql
UPDATE business_applications
SET status = 'approved', reviewed_at = NOW(), updated_at = NOW()
WHERE id = ? AND status = 'under_review';
```

### Reject Application (Admin - Out of Scope)
```sql
UPDATE business_applications
SET status = 'rejected', reviewed_at = NOW(), notes = ?, updated_at = NOW()
WHERE id = ? AND status = 'under_review';
```

---

## User Module vs Admin Module

### User Module (In Scope)
- ✅ Create application (draft)
- ✅ Upload documents
- ✅ Submit application (draft → submitted)
- ✅ View application status
- ✅ Receive notifications

### Admin Module (Out of Scope)
- ❌ Review applications (submitted → under_review)
- ❌ Approve applications (under_review → approved)
- ❌ Reject applications (under_review → rejected)
- ❌ Add review notes
- ❌ Issue permits

---

**Diagram Status:** ✅ Complete  
**Total Steps:** 30+  
**Decision Points:** 9  
**Status Transitions:** 5  
**Last Updated:** November 20, 2025

