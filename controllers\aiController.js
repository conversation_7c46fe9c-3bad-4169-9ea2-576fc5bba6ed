// AI Controller - OpenRouter AI Integration
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Handle AI-powered features using OpenRouter

const { getOpenRouterService } = require('../services/openRouterService');
const { BusinessApplication, BusinessProfile, DocumentUpload, FeedbackSubmission } = require('../models');

/**
 * Generate AI response for general queries
 * @route POST /api/ai/chat
 * @access Private
 */
const generateChatResponse = async (req, res) => {
  try {
    const { message, context = 'general' } = req.body;

    if (!message || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Message is required.',
      });
    }

    // Get OpenRouter service instance
    const openRouterService = getOpenRouterService(process.env.OPENROUTER_API_KEY);

    // Prepare context-aware prompt
    let systemPrompt = 'You are a helpful assistant for a business application submission system. ';

    switch (context) {
      case 'application':
        systemPrompt += 'Help users with business application forms, requirements, and guidance.';
        break;
      case 'documents':
        systemPrompt += 'Assist with document preparation, requirements, and best practices.';
        break;
      case 'feedback':
        systemPrompt += 'Help users provide constructive feedback about the application process.';
        break;
      default:
        systemPrompt += 'Provide general assistance with business applications and government processes.';
    }

    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: message }
    ];

    const response = await openRouterService.generateText({
      model: 'anthropic/claude-3-haiku',
      messages,
      max_tokens: 500,
      temperature: 0.7,
    });

    res.status(200).json({
      success: true,
      data: {
        response: response.choices[0].message.content,
        model: response.model,
        usage: response.usage,
      },
    });
  } catch (error) {
    console.error('AI chat error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate AI response.',
    });
  }
};

/**
 * Analyze application data and provide insights
 * @route POST /api/ai/analyze-application
 * @access Private
 */
const analyzeApplication = async (req, res) => {
  try {
    const { application_id } = req.body;

    if (!application_id) {
      return res.status(400).json({
        success: false,
        message: 'Application ID is required.',
      });
    }

    // Get application with related data
    const application = await BusinessApplication.findOne({
      where: { id: application_id },
      include: [
        {
          model: BusinessProfile,
          as: 'businessProfile',
          where: { client_id: req.user.userId },
        },
        {
          model: DocumentUpload,
          as: 'documents',
          required: false,
        },
      ],
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: 'Application not found.',
      });
    }

    // Get OpenRouter service
    const openRouterService = getOpenRouterService(process.env.OPENROUTER_API_KEY);

    // Prepare analysis prompt
    const applicationData = {
      type: application.application_type,
      status: application.status,
      submitted_at: application.submitted_at,
      business_profile: {
        business_name: application.businessProfile.business_name,
        business_type: application.businessProfile.business_type,
        description: application.businessProfile.description,
      },
      documents_count: application.documents?.length || 0,
      documents_types: application.documents?.map(doc => doc.document_type) || [],
    };

    const analysisPrompt = `
Analyze this business application and provide insights:

Application Data:
- Type: ${applicationData.type}
- Status: ${applicationData.status}
- Business Name: ${applicationData.business_profile.business_name}
- Business Type: ${applicationData.business_profile.business_type}
- Description: ${applicationData.business_profile.description}
- Documents Submitted: ${applicationData.documents_count}
- Document Types: ${applicationData.documents_types.join(', ')}

Please provide:
1. Application completeness assessment
2. Potential issues or missing information
3. Recommendations for improvement
4. Success probability estimate
5. Next steps advice
`;

    const messages = [
      { role: 'system', content: 'You are an expert business application analyst. Provide detailed, actionable insights.' },
      { role: 'user', content: analysisPrompt }
    ];

    const response = await openRouterService.generateText({
      model: 'anthropic/claude-3-haiku',
      messages,
      max_tokens: 800,
      temperature: 0.3,
    });

    res.status(200).json({
      success: true,
      data: {
        analysis: response.choices[0].message.content,
        application_data: applicationData,
        model: response.model,
        usage: response.usage,
      },
    });
  } catch (error) {
    console.error('AI application analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze application.',
    });
  }
};

/**
 * Generate feedback suggestions
 * @route POST /api/ai/feedback-suggestions
 * @access Private
 */
const generateFeedbackSuggestions = async (req, res) => {
  try {
    const { application_id, current_rating } = req.body;

    // Get OpenRouter service
    const openRouterService = getOpenRouterService(process.env.OPENROUTER_API_KEY);

    let contextInfo = '';
    if (application_id) {
      const application = await BusinessApplication.findOne({
        where: { id: application_id },
        include: [
          {
            model: BusinessProfile,
            as: 'businessProfile',
            where: { client_id: req.user.userId },
          },
        ],
      });

      if (application) {
        contextInfo = ` for a ${application.application_type} application (${application.status} status)`;
      }
    }

    const prompt = `Generate 3-5 constructive feedback suggestions${contextInfo} with a ${current_rating || 'general'}-star rating perspective. Focus on system usability, process efficiency, and service quality.`;

    const messages = [
      { role: 'system', content: 'You are a feedback expert. Generate specific, actionable feedback suggestions.' },
      { role: 'user', content: prompt }
    ];

    const response = await openRouterService.generateText({
      model: 'anthropic/claude-3-haiku',
      messages,
      max_tokens: 400,
      temperature: 0.6,
    });

    res.status(200).json({
      success: true,
      data: {
        suggestions: response.choices[0].message.content,
        model: response.model,
        usage: response.usage,
      },
    });
  } catch (error) {
    console.error('AI feedback suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate feedback suggestions.',
    });
  }
};

/**
 * Get AI model information
 * @route GET /api/ai/models
 * @access Private
 */
const getAvailableModels = async (req, res) => {
  try {
    // Return available models for the application
    const models = [
      {
        id: 'anthropic/claude-3-haiku',
        name: 'Claude 3 Haiku',
        provider: 'Anthropic',
        capabilities: ['chat', 'analysis', 'suggestions'],
        max_tokens: 4096,
      },
      {
        id: 'openai/gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        capabilities: ['chat', 'analysis'],
        max_tokens: 4096,
      },
    ];

    res.status(200).json({
      success: true,
      data: models,
    });
  } catch (error) {
    console.error('Get models error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve available models.',
    });
  }
};

module.exports = {
  generateChatResponse,
  analyzeApplication,
  generateFeedbackSuggestions,
  getAvailableModels,
};
