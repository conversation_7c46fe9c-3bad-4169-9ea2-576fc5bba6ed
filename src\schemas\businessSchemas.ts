// Business Schemas - Validation schemas for business-related forms
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import * as yup from 'yup';

// Business Profile Schema
export const businessProfileSchema = yup.object().shape({
  business_name: yup
    .string()
    .required('Business name is required')
    .min(2, 'Business name must be at least 2 characters')
    .max(255, 'Business name must not exceed 255 characters'),
  business_type: yup
    .string()
    .max(100, 'Business type must not exceed 100 characters')
    .optional(),
  address: yup
    .string()
    .optional(),
  contact_number: yup
    .string()
    .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format')
    .optional(),
  tin_number: yup
    .string()
    .matches(/^[0-9\-]+$/, 'TIN must contain only numbers and hyphens')
    .max(50, 'TIN must not exceed 50 characters')
    .optional(),
});

export type BusinessProfileFormData = yup.InferType<typeof businessProfileSchema>;

// Business Application Schema
export const businessApplicationSchema = yup.object().shape({
  business_profile_id: yup
    .string()
    .required('Please select a business profile'),
  application_type: yup
    .string()
    .oneOf(['new', 'renewal'], 'Application type must be either "new" or "renewal"')
    .required('Application type is required'),
});

export type BusinessApplicationFormData = yup.InferType<typeof businessApplicationSchema>;

// Profile Update Schema
export const profileUpdateSchema = yup.object().shape({
  full_name: yup
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(255, 'Full name must not exceed 255 characters')
    .optional(),
  contact_number: yup
    .string()
    .matches(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format')
    .optional(),
  current_password: yup
    .string()
    .when('new_password', {
      is: (val: string) => val && val.length > 0,
      then: (schema) => schema.required('Current password is required to change password'),
      otherwise: (schema) => schema.optional(),
    }),
  new_password: yup
    .string()
    .min(8, 'New password must be at least 8 characters')
    .optional(),
  confirm_password: yup
    .string()
    .when('new_password', {
      is: (val: string) => val && val.length > 0,
      then: (schema) => schema
        .required('Please confirm your new password')
        .oneOf([yup.ref('new_password')], 'Passwords must match'),
      otherwise: (schema) => schema.optional(),
    }),
});

export type ProfileUpdateFormData = yup.InferType<typeof profileUpdateSchema>;

// Feedback Schema
export const feedbackSchema = yup.object().shape({
  rating: yup
    .number()
    .required('Rating is required')
    .min(1, 'Rating must be at least 1')
    .max(5, 'Rating must not exceed 5'),
  comments: yup
    .string()
    .max(1000, 'Comments must not exceed 1000 characters')
    .optional(),
  application_id: yup
    .string()
    .optional(),
});

export type FeedbackFormData = yup.InferType<typeof feedbackSchema>;

