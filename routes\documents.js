// Document Routes - Document Upload Management API
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define API routes for document upload operations

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { upload, handleMulterError, validateUpload } = require('../middleware/upload');
const {
  uploadDocument,
  getDocumentsByApplication,
  deleteDocument,
} = require('../controllers/documentController');

/**
 * @route   POST /api/documents/upload
 * @desc    Upload document for application
 * @access  Private
 */
router.post(
  '/upload',
  authenticateToken,
  upload.single('document'),
  handleMulterError,
  validateUpload,
  uploadDocument
);

/**
 * @route   GET /api/documents/application/:applicationId
 * @desc    Get documents for application
 * @access  Private
 */
router.get('/application/:applicationId', authenticateToken, getDocumentsByApplication);

/**
 * @route   DELETE /api/documents/:id
 * @desc    Delete document
 * @access  Private
 */
router.delete('/:id', authenticateToken, deleteDocument);

module.exports = router;

