// Notification Controller - Notification Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Handle notification operations (GET, PUT)

const { Notification } = require('../models');

/**
 * Get all notifications for authenticated user
 * @route GET /api/notifications
 * @access Private
 */
const getNotifications = async (req, res) => {
  try {
    const { limit = 50, unread_only = false } = req.query;

    const where = { client_id: req.user.userId };
    
    if (unread_only === 'true') {
      where.is_read = false;
    }

    const notifications = await Notification.findAll({
      where: where,
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
    });

    const unreadCount = await Notification.countUnread(req.user.userId);

    res.status(200).json({
      success: true,
      data: notifications,
      unread_count: unreadCount,
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve notifications.',
    });
  }
};

/**
 * Mark notification as read
 * @route PUT /api/notifications/:id/read
 * @access Private
 */
const markAsRead = async (req, res) => {
  try {
    const { id } = req.params;

    const notification = await Notification.findOne({
      where: {
        id: id,
        client_id: req.user.userId,
      },
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found.',
      });
    }

    await notification.markAsRead();

    res.status(200).json({
      success: true,
      message: 'Notification marked as read.',
      data: notification,
    });
  } catch (error) {
    console.error('Mark as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read.',
    });
  }
};

/**
 * Mark all notifications as read
 * @route PUT /api/notifications/read-all
 * @access Private
 */
const markAllAsRead = async (req, res) => {
  try {
    const updatedCount = await Notification.markAllAsRead(req.user.userId);

    res.status(200).json({
      success: true,
      message: 'All notifications marked as read.',
      updated_count: updatedCount[0],
    });
  } catch (error) {
    console.error('Mark all as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark all notifications as read.',
    });
  }
};

/**
 * Get unread notification count
 * @route GET /api/notifications/unread-count
 * @access Private
 */
const getUnreadCount = async (req, res) => {
  try {
    const count = await Notification.countUnread(req.user.userId);

    res.status(200).json({
      success: true,
      unread_count: count,
    });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get unread count.',
    });
  }
};

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  getUnreadCount,
};

