import * as yup from 'yup'

export const loginSchema = yup.object({
  email: yup
    .string()
    .required('Email is required')
    .email('Invalid email format')
    .trim()
    .lowercase(),
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters'),
  rememberMe: yup.boolean().default(false),
})

export type LoginFormData = yup.InferType<typeof loginSchema>

export const registerSchema = yup.object({
  email: yup
    .string()
    .required('Email is required')
    .email('Invalid email format')
    .trim()
    .lowercase(),
  fullName: yup
    .string()
    .required('Full name is required')
    .trim()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must not exceed 100 characters'),
  contactNumber: yup
    .string()
    .required('Contact number is required')
    .matches(/^(\+63|0)[0-9]{10}$/, 'Contact number must be a valid Philippine mobile number (e.g., +639xxxxxxxxx or 09xxxxxxxxx)'),
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must not exceed 128 characters')
    .matches(/[a-z]/, 'Password must include at least one lowercase letter')
    .matches(/[A-Z]/, 'Password must include at least one uppercase letter')
    .matches(/[0-9]/, 'Password must include at least one number')
    .matches(/[^a-zA-Z0-9]/, 'Password must include at least one special character'),
  confirmPassword: yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref('password')], 'Passwords do not match'),
})

export type RegisterFormData = yup.InferType<typeof registerSchema>

export const otpSchema = yup.object({
  otp: yup
    .string()
    .required('OTP is required')
    .matches(/^\d{6}$/, 'OTP must be exactly 6 digits')
    .length(6, 'OTP must be exactly 6 digits'),
})

export type OTPFormData = yup.InferType<typeof otpSchema>

