// Business Routes - Business Profile Management API
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define API routes for business profile operations

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const {
  getBusinessProfile,
  createBusinessProfile,
  updateBusinessProfile,
} = require('../controllers/businessController');

/**
 * @route   GET /api/business
 * @desc    Get business profile for authenticated user
 * @access  Private
 */
router.get('/', authenticateToken, getBusinessProfile);

/**
 * @route   POST /api/business
 * @desc    Create business profile for authenticated user
 * @access  Private
 */
router.post('/', authenticateToken, createBusinessProfile);

/**
 * @route   PUT /api/business
 * @desc    Update business profile for authenticated user
 * @access  Private
 */
router.put('/', authenticateToken, updateBusinessProfile);

export default router;
