const fs = require('fs');
const path = require('path');

console.log('🔍 PostgreSQL Migration Validation Script');
console.log('==========================================\n');

// Validation results
const results = {
  passed: 0,
  failed: 0,
  warnings: 0
};

function logResult(test, status, message = '') {
  const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  console.log(`${icon} ${test}: ${status}`);
  if (message) console.log(`   ${message}`);

  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else results.warnings++;
}

// Test 1: Check PostgreSQL schema file exists and has correct syntax
console.log('1. Schema File Validation');
console.log('-------------------------');

try {
  const schemaPath = path.join(__dirname, 'database', 'postgres-schema.sql');
  const schemaContent = fs.readFileSync(schemaPath, 'utf8');

  // Check for PostgreSQL-specific keywords
  const postgresKeywords = [
    'CREATE EXTENSION',
    'uuid-ossp',
    'pgcrypto',
    'UUID PRIMARY KEY',
    'JSONB',
    'TIMESTAMP WITH TIME ZONE',
    'CREATE TYPE',
    'ENUM',
    'CHECK',
    'LANGUAGE plpgsql'
  ];

  postgresKeywords.forEach(keyword => {
    if (schemaContent.includes(keyword)) {
      logResult(`${keyword} syntax`, 'PASS');
    } else {
      logResult(`${keyword} syntax`, 'FAIL', `Missing ${keyword} in schema`);
    }
  });

  // Check for MySQL-specific syntax (should not be present)
  const mysqlKeywords = ['AUTO_INCREMENT', 'ENGINE=InnoDB', 'UNSIGNED', 'ZEROFILL'];
  mysqlKeywords.forEach(keyword => {
    if (schemaContent.includes(keyword)) {
      logResult(`MySQL ${keyword} removal`, 'FAIL', `Found MySQL syntax: ${keyword}`);
    } else {
      logResult(`MySQL ${keyword} removal`, 'PASS');
    }
  });

} catch (error) {
  logResult('Schema file reading', 'FAIL', error.message);
}

// Test 2: Check Sequelize model updates
console.log('\n2. Sequelize Model Validation');
console.log('-----------------------------');

try {
  const modelPath = path.join(__dirname, 'models', 'ClientAccount.js');
  const modelContent = fs.readFileSync(modelPath, 'utf8');

  // Check for PostgreSQL data types
  if (modelContent.includes('DataTypes.UUID')) {
    logResult('UUID data type', 'PASS');
  } else {
    logResult('UUID data type', 'FAIL', 'Missing DataTypes.UUID');
  }

  if (modelContent.includes('DataTypes.JSONB')) {
    logResult('JSONB data type', 'PASS');
  } else {
    logResult('JSONB data type', 'FAIL', 'Missing DataTypes.JSONB');
  }

  if (modelContent.includes('DataTypes.DATE')) {
    logResult('TIMESTAMP data type', 'PASS');
  } else {
    logResult('TIMESTAMP data type', 'FAIL', 'Missing DataTypes.DATE');
  }

  // Check for PostgreSQL-specific index types
  if (modelContent.includes('using: \'gin\'')) {
    logResult('GIN index for JSONB', 'PASS');
  } else {
    logResult('GIN index for JSONB', 'FAIL', 'Missing GIN index configuration');
  }

} catch (error) {
  logResult('Model file reading', 'FAIL', error.message);
}

// Test 3: Check database configuration
console.log('\n3. Database Configuration Validation');
console.log('-----------------------------------');

try {
  const configPath = path.join(__dirname, 'config', 'database.js');
  const configContent = fs.readFileSync(configPath, 'utf8');

  if (configContent.includes('dialect: \'postgres\'')) {
    logResult('PostgreSQL dialect', 'PASS');
  } else {
    logResult('PostgreSQL dialect', 'FAIL', 'Missing postgres dialect');
  }

  if (configContent.includes('port: 5432')) {
    logResult('PostgreSQL port', 'PASS');
  } else {
    logResult('PostgreSQL port', 'FAIL', 'Missing PostgreSQL port 5432');
  }

  if (configContent.includes('pg_hstore')) {
    logResult('pg-hstore dependency', 'WARN', 'pg-hstore not found in config, but may be handled by pg package');
  }

} catch (error) {
  logResult('Config file reading', 'FAIL', error.message);
}

// Test 4: Check migration file
console.log('\n4. Migration File Validation');
console.log('----------------------------');

try {
  const migrationPath = path.join(__dirname, 'migrations', '**************-create-client-accounts-postgres.js');
  const migrationContent = fs.readFileSync(migrationPath, 'utf8');

  if (migrationContent.includes('Sequelize.UUID')) {
    logResult('Migration UUID type', 'PASS');
  } else {
    logResult('Migration UUID type', 'FAIL', 'Missing Sequelize.UUID in migration');
  }

  if (migrationContent.includes('Sequelize.JSONB')) {
    logResult('Migration JSONB type', 'PASS');
  } else {
    logResult('Migration JSONB type', 'FAIL', 'Missing Sequelize.JSONB in migration');
  }

  if (migrationContent.includes('otp_purpose')) {
    logResult('Custom ENUM type', 'PASS');
  } else {
    logResult('Custom ENUM type', 'FAIL', 'Missing custom ENUM type');
  }

  if (migrationContent.includes('LANGUAGE plpgsql')) {
    logResult('PL/pgSQL function', 'PASS');
  } else {
    logResult('PL/pgSQL function', 'FAIL', 'Missing PL/pgSQL stored procedure');
  }

} catch (error) {
  logResult('Migration file reading', 'FAIL', error.message);
}

// Test 5: Check package.json dependencies
console.log('\n5. Package Dependencies Validation');
console.log('----------------------------------');

try {
  const packagePath = path.join(__dirname, 'package.json');
  const packageContent = fs.readFileSync(packagePath, 'utf8');
  const packageJson = JSON.parse(packageContent);

  const dependencies = packageJson.dependencies || {};

  if (dependencies.pg) {
    logResult('pg dependency', 'PASS');
  } else {
    logResult('pg dependency', 'FAIL', 'Missing pg dependency');
  }

  if (dependencies['pg-hstore']) {
    logResult('pg-hstore dependency', 'PASS');
  } else {
    logResult('pg-hstore dependency', 'FAIL', 'Missing pg-hstore dependency');
  }

  if (dependencies.mysql2) {
    logResult('mysql2 removal', 'FAIL', 'MySQL dependency still present');
  } else {
    logResult('mysql2 removal', 'PASS');
  }

} catch (error) {
  logResult('Package.json reading', 'FAIL', error.message);
}

// Test 6: Check test file creation
console.log('\n6. Test Files Validation');
console.log('------------------------');

try {
  const testPath = path.join(__dirname, 'database', 'postgres-test.sql');
  if (fs.existsSync(testPath)) {
    logResult('PostgreSQL test file', 'PASS');
  } else {
    logResult('PostgreSQL test file', 'FAIL', 'postgres-test.sql not found');
  }

  const fileMapPath = path.join(__dirname, 'FILE-MAP.md');
  if (fs.existsSync(fileMapPath)) {
    logResult('Migration documentation', 'PASS');
  } else {
    logResult('Migration documentation', 'FAIL', 'FILE-MAP.md not found');
  }

} catch (error) {
  logResult('Test files check', 'FAIL', error.message);
}

// Final results
console.log('\n📊 Validation Summary');
console.log('====================');
console.log(`✅ Passed: ${results.passed}`);
console.log(`❌ Failed: ${results.failed}`);
console.log(`⚠️  Warnings: ${results.warnings}`);

const totalTests = results.passed + results.failed + results.warnings;
const successRate = ((results.passed / totalTests) * 100).toFixed(1);

if (results.failed === 0) {
  console.log(`\n🎉 SUCCESS: ${successRate}% of tests passed!`);
  console.log('PostgreSQL migration is ready for deployment.');
  console.log('\nNext steps:');
  console.log('1. Install PostgreSQL 16+ on your system');
  console.log('2. Create database: smart_governance_auth');
  console.log('3. Update .env with PostgreSQL credentials');
  console.log('4. Run: npx sequelize-cli db:migrate');
  console.log('5. Test with: psql -d smart_governance_auth -f database/postgres-test.sql');
} else {
  console.log(`\n⚠️  ISSUES FOUND: ${successRate}% success rate`);
  console.log('Please review and fix the failed tests before deployment.');
}

console.log('\n📚 Academic Validation:');
console.log('This migration aligns with Holy Trinity College PCMRC BSCS thesis requirements:');
console.log('- ILO (a): Demonstrates practical database migration skills');
console.log('- ILO (b): Addresses scalability limitations in government systems');
console.log('- ILO (c): Documents systematic migration methodology');
console.log('- ILO (d-e-f): Shows theoretical framework application in real-world scenario');

process.exit(results.failed > 0 ? 1 : 0);
