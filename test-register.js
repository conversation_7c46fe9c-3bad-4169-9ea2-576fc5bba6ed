// Simple test script to test registration endpoint
const http = require('http');

const data = JSON.stringify({
  email: '<EMAIL>',
  password: 'JestPass123!',
  fullName: 'Jest Test User',
  contactNumber: '+639123456789'
});

console.log('[TEST] Starting registration test...');
console.log('[TEST] Endpoint: http://localhost:3001/api/auth/register');
console.log('[TEST] Body:', data);

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/api/auth/register',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length
  }
};

const req = http.request(options, (res) => {
  let responseData = '';

  console.log(`[TEST] Status Code: ${res.statusCode}`);

  res.on('data', (chunk) => {
    responseData += chunk;
  });

  res.on('end', () => {
    console.log('[TEST] Response:', responseData);
    try {
      const parsed = JSON.parse(responseData);
      console.log('[TEST] Parsed:', JSON.stringify(parsed, null, 2));
    } catch (e) {
      console.error('[TEST] Failed to parse response:', e.message);
    }
  });
});

req.on('error', (e) => {
  console.error('[TEST] Request failed:', e.message);
});

req.write(data);
req.end();

console.log('[TEST] Request sent, waiting for response...');
