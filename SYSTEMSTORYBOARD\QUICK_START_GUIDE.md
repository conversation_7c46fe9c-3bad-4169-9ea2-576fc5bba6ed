# 🚀 Quick Start Guide
## Smart Governance User Module - BSCS Mini-Thesis

**Last Updated:** November 22, 2025  
**Status:** ✅ All bugs fixed, ready to run!

---

## ⚡ FASTEST WAY TO RUN THE APPLICATION

### **Prerequisites:**
- ✅ PostgreSQL 18 installed and running
- ✅ Node.js 22.19.0 installed
- ✅ npm installed

---

## 🎯 3-STEP STARTUP

### **Step 1: Ensure PostgreSQL is Running**
```bash
# Check if PostgreSQL is running
# Windows: Check Services or Task Manager
# The database should already exist: smart_governance_auth
# User: postgres
# Password: jazs4438123
# Port: 5432
```

---

### **Step 2: Start Backend Server**

**Option A: Direct Node Command (Recommended)**
```bash
# Open Terminal 1
cd "C:\Users\<USER>\OneDrive\Desktop\MINI THESIS\TEST 2"
node server.js
```

**Expected Output:**
```
✅ Database connection established successfully.
✅ Database synchronized successfully.
🚀 Smart Governance Auth API Server running on port 3001
📊 Health check available at: http://localhost:3001/health
🔐 Environment: development
```

**Option B: Using npm script**
```bash
npm start
```

---

### **Step 3: Start Frontend Dev Server**

```bash
# Open Terminal 2 (new terminal window)
cd "C:\Users\<USER>\OneDrive\Desktop\MINI THESIS\TEST 2"
npm run dev
```

**Expected Output:**
```
VITE v5.0.7  ready in XXX ms

➜  Local:   http://localhost:5173/
➜  Network: use --host to expose
➜  press h to show help
```

---

## 🌐 ACCESS THE APPLICATION

### **Frontend:**
```
http://localhost:5173
```

### **Backend API:**
```
http://localhost:3001/api
```

### **Health Check:**
```
http://localhost:3001/health
```

---

## 🧪 TEST THE APPLICATION

### **1. Register a New Account**
1. Go to `http://localhost:5173`
2. Click "Register" or navigate to `/register`
3. Fill in:
   - Email: `<EMAIL>`
   - Password: `Test123!@#` (must meet requirements)
4. Click "Register"
5. **Auto-login should work** - you'll be redirected to dashboard

### **2. Test Dashboard**
- Should see welcome message
- Should see quick stats
- Should see recent applications

### **3. Test Business Profile**
1. Click "Business Profile" in sidebar
2. Create a new business profile
3. Fill in all required fields
4. Submit

### **4. Test Application**
1. Click "New Application" in sidebar
2. Select business profile
3. Choose application type (New/Renewal)
4. Fill in details
5. Submit

### **5. Test Document Upload**
1. After creating application
2. Upload required documents (PDF, JPG, PNG)
3. Max file size: 5MB
4. Submit documents

---

## 🔧 TROUBLESHOOTING

### **Backend won't start:**
```bash
# Check if PostgreSQL is running
# Check if port 3001 is available
# Check .env file exists with correct credentials
```

### **Frontend won't start:**
```bash
# Check if port 5173 is available
# Try: npm install (if dependencies missing)
```

### **Database connection error:**
```bash
# Verify PostgreSQL is running on port 5432
# Verify database 'smart_governance_auth' exists
# Verify credentials: postgres/jazs4438123
```

### **CORS errors:**
```bash
# Backend CORS is configured for http://localhost:5173
# Make sure frontend is running on port 5173
```

---

## 📊 API ENDPOINTS AVAILABLE

### **Authentication:**
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/verify-otp` - Verify OTP (legacy)
- `POST /api/auth/resend-otp` - Resend OTP (legacy)

### **Profile:**
- `GET /api/profile` - Get user profile
- `PUT /api/profile` - Update profile
- `PUT /api/profile/password` - Change password

### **Business Profiles:**
- `GET /api/business-profile` - Get all business profiles
- `GET /api/business-profile/:id` - Get specific profile
- `POST /api/business-profile` - Create new profile
- `PUT /api/business-profile/:id` - Update profile

### **Applications:**
- `GET /api/applications` - Get all applications
- `GET /api/applications/:id` - Get specific application
- `POST /api/applications` - Create new application
- `PUT /api/applications/:id` - Update application
- `POST /api/applications/:id/submit` - Submit application

### **Documents:**
- `POST /api/documents/upload` - Upload document
- `GET /api/documents/application/:id` - Get application documents
- `DELETE /api/documents/:id` - Delete document

### **Notifications:**
- `GET /api/notifications` - Get all notifications
- `PUT /api/notifications/:id/read` - Mark as read
- `PUT /api/notifications/read-all` - Mark all as read

### **Feedback:**
- `POST /api/feedback` - Submit feedback
- `GET /api/feedback` - Get user feedback
- `GET /api/feedback/:id` - Get specific feedback

---

## 🎓 PROJECT INFORMATION

**Project:** Smart Governance Business Permit Processing System
**Module:** USER MODULE ONLY (No Admin Features)
**Institution:** Holy Trinity College of General Santos City
**Course:** BSCS Mini-Thesis
**Database:** PostgreSQL 18 (NOT MySQL, NOT version 16+)
**File Storage:** Multer Local Storage (NOT AWS S3)
**OTP System:** Fake OTP (Auto-verification for demo purposes)

---

## ✅ VERIFICATION CHECKLIST

Before testing, ensure:
- [ ] PostgreSQL is running on port 5432
- [ ] Database 'smart_governance_auth' exists
- [ ] Backend server started successfully (port 3001)
- [ ] Frontend dev server started successfully (port 5173)
- [ ] No error messages in either terminal
- [ ] Can access http://localhost:5173 in browser

---

## 📞 NEED HELP?

Check these files for more information:
- `CODE_REVIEW_COMPLETE_REPORT.md` - Full bug fix report
- `BUG_FIX_REPORT.md` - Initial bug analysis
- `smart_governance_full_system.yaml` - Complete system specification
- `diagrams/` - System architecture diagrams
- `algorithms/` - Algorithm documentation

---

**Happy Testing! 🚀**

