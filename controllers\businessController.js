// Business Profile Controller - Business Profile Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Handle business profile operations (GET, POST, PUT)

const { BusinessProfile, ClientAccount } = require('../models');

/**
 * Get all business profiles for authenticated user
 * @route GET /api/business-profile
 * @access Private
 */
const getBusinessProfiles = async (req, res) => {
  try {
    const profiles = await BusinessProfile.findAll({
      where: { client_id: req.user.userId },
      order: [['created_at', 'DESC']],
    });

    res.status(200).json({
      success: true,
      data: profiles,
    });
  } catch (error) {
    console.error('Get business profiles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve business profiles.',
    });
  }
};

/**
 * Get single business profile by ID
 * @route GET /api/business-profile/:id
 * @access Private
 */
const getBusinessProfileById = async (req, res) => {
  try {
    const { id } = req.params;

    const profile = await BusinessProfile.findOne({
      where: {
        id: id,
        client_id: req.user.userId,
      },
    });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Business profile not found.',
      });
    }

    res.status(200).json({
      success: true,
      data: profile,
    });
  } catch (error) {
    console.error('Get business profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve business profile.',
    });
  }
};

/**
 * Create new business profile
 * @route POST /api/business-profile
 * @access Private
 */
const createBusinessProfile = async (req, res) => {
  try {
    const { business_name, business_type, address, contact_number, tin_number } = req.body;

    // Validation
    if (!business_name || business_name.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Business name is required and must be at least 2 characters.',
      });
    }

    // Check if TIN already exists (if provided)
    if (tin_number) {
      const existingProfile = await BusinessProfile.findOne({
        where: { tin_number: tin_number },
      });

      if (existingProfile) {
        return res.status(400).json({
          success: false,
          message: 'TIN number is already registered.',
        });
      }
    }

    // Create business profile
    const profile = await BusinessProfile.create({
      client_id: req.user.userId,
      business_name: business_name.trim(),
      business_type: business_type?.trim() || null,
      address: address?.trim() || null,
      contact_number: contact_number || null,
      tin_number: tin_number || null,
      metadata: {},
    });

    res.status(201).json({
      success: true,
      message: 'Business profile created successfully.',
      data: profile,
    });
  } catch (error) {
    console.error('Create business profile error:', error);
    
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'TIN number is already registered.',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create business profile.',
    });
  }
};

/**
 * Update business profile
 * @route PUT /api/business-profile/:id
 * @access Private
 */
const updateBusinessProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const { business_name, business_type, address, contact_number, tin_number } = req.body;

    const profile = await BusinessProfile.findOne({
      where: {
        id: id,
        client_id: req.user.userId,
      },
    });

    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Business profile not found.',
      });
    }

    // Update fields
    if (business_name !== undefined) {
      if (business_name.trim().length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Business name must be at least 2 characters.',
        });
      }
      profile.business_name = business_name.trim();
    }

    if (business_type !== undefined) profile.business_type = business_type?.trim() || null;
    if (address !== undefined) profile.address = address?.trim() || null;
    if (contact_number !== undefined) profile.contact_number = contact_number || null;
    if (tin_number !== undefined) profile.tin_number = tin_number || null;

    await profile.save();

    res.status(200).json({
      success: true,
      message: 'Business profile updated successfully.',
      data: profile,
    });
  } catch (error) {
    console.error('Update business profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update business profile.',
    });
  }
};

module.exports = {
  getBusinessProfiles,
  getBusinessProfileById,
  createBusinessProfile,
  updateBusinessProfile,
};

