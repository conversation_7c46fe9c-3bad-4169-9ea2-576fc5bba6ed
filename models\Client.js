const bcrypt = require('bcrypt');
const crypto = require('crypto');

module.exports = (sequelize, DataTypes) => {
  const Client = sequelize.define('Client', {
    // Primary key: UUID v4 for secure identifiers (PostgreSQL native support)
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique client identifier (UUID v4)',
    },
    // Email with format validation and uniqueness
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: {
        msg: 'Email address is already registered',
      },
      validate: {
        isEmail: {
          msg: 'Please enter a valid email address',
        },
        notEmpty: {
          msg: 'Email is required',
        },
      },
      comment: 'Client email for login',
    },
    // Password hash (never store plain text)
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: 'bcrypt-hashed password',
    },
    // Client name with length validation
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: {
          args: [2, 255],
          msg: 'Client name must be between 2 and 255 characters',
        },
        notEmpty: {
          msg: 'Client name is required',
        },
      },
      comment: 'Client organization name',
    },
    // Required contact number
    contact_number: {
      type: DataTypes.STRING(50),
      allowNull: false,
      validate: {
        notEmpty: {
          msg: 'Contact number is required',
        },
        is: {
          args: /^(\+63|0)[0-9]{10}$/,
          msg: 'Please enter a valid Philippine mobile number (e.g., +639xxxxxxxxx or 09xxxxxxxxx)',
        },
      },
      comment: 'Required Philippine mobile number for client organization',
    },
    // Optional address
    address: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Optional address for the client organization',
    },
    // PostgreSQL JSONB field for flexible future extensions
    metadata: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
      comment: 'JSONB field for flexible metadata (preferences, etc.)',
    },
  }, {
    // Table configuration
    tableName: 'clients',
    timestamps: true, // Enables createdAt and updatedAt fields
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Email search index for fast login queries
      {
        fields: ['email'],
        name: 'idx_clients_email_search',
        using: 'btree', // PostgreSQL-specific index type
      },
      // Name index for search
      {
        fields: ['name'],
        name: 'idx_clients_name',
        using: 'btree',
      },
      // GIN index for JSONB metadata queries (PostgreSQL-specific)
      {
        fields: ['metadata'],
        name: 'idx_clients_metadata',
        using: 'gin',
      },
    ],
    hooks: {
      // Hash password before creating client
      beforeCreate: async (client) => {
        if (client.password_hash && !client.password_hash.startsWith('$2a$') && !client.password_hash.startsWith('$2b$') && !client.password_hash.startsWith('$2y$')) {
          // Hash the password with cost factor 10 (OWASP recommended)
          const saltRounds = 10;
          client.password_hash = await bcrypt.hash(client.password_hash, saltRounds);
        }
      },
      // Hash password before updating client
      beforeUpdate: async (client) => {
        if (client.changed('password_hash') && client.password_hash && !client.password_hash.startsWith('$2a$') && !client.password_hash.startsWith('$2b$') && !client.password_hash.startsWith('$2y$')) {
          // Hash the password with cost factor 10
          const saltRounds = 10;
          client.password_hash = await bcrypt.hash(client.password_hash, saltRounds);
        }
      },
    },
  });

  // Instance method to validate password during login
  Client.prototype.validatePassword = async function(plainPassword) {
    return await bcrypt.compare(plainPassword, this.password_hash);
  };

  // Instance method to generate password reset token (for future use)
  Client.prototype.generatePasswordResetToken = function() {
    // Generate a secure random token using crypto
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  };

  // Instance method to update metadata (JSONB operations)
  Client.prototype.updateMetadata = async function(key, value) {
    const metadata = { ...this.metadata };
    metadata[key] = value;
    return await this.update({ metadata });
  };

  // Static method to find client by email for login
  Client.findByEmail = async function(email) {
    return await this.findOne({
      where: { email: email.toLowerCase() },
    });
  };

  // Static method to create verified client (for admin use)
  Client.createVerifiedClient = async function(clientData) {
    return await this.create(clientData);
  };

  // Static method to get client statistics with PostgreSQL JSON aggregation
  Client.getStats = async function() {
    const totalClients = await this.count();

    return {
      total: totalClients,
    };
  };

  // Static method to search clients by metadata (PostgreSQL JSONB advantage)
  Client.findByMetadata = async function(key, value) {
    return await this.findAll({
      where: sequelize.where(
        sequelize.json(`metadata.${key}`),
        value
      ),
    });
  };

  return Client;
};
