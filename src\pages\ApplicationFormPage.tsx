// Application Form Page - Business Permit Application
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { FileText, Building2, Loader2, ArrowRight } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Label } from '@/components/ui/Label';
import { Select } from '@/components/ui/Select';
import BackButton from '@/components/ui/BackButton';
import { businessApplicationSchema, BusinessApplicationFormData } from '@/schemas/businessSchemas';
import { businessService, BusinessProfile } from '@/services/businessService';
import { applicationService } from '@/services/applicationService';
import { useToast } from '@/hooks/useToast';

const ApplicationFormPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [profiles, setProfiles] = useState<BusinessProfile[]>([]);
  const navigate = useNavigate();
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<BusinessApplicationFormData>({
    resolver: yupResolver(businessApplicationSchema),
    mode: 'onBlur',
  });

  useEffect(() => {
    fetchProfiles();
  }, []);

  const fetchProfiles = async () => {
    try {
      setIsFetching(true);
      const response = await businessService.getBusinessProfiles();
      setProfiles(response.data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load business profiles',
      });
    } finally {
      setIsFetching(false);
    }
  };

  const onSubmit = async (data: BusinessApplicationFormData) => {
    setIsLoading(true);
    try {
      const response = await applicationService.createApplication(data);
      
      toast({
        variant: 'success',
        title: 'Application Created',
        description: 'Your application has been created as a draft. Please upload required documents.',
      });

      // Navigate to document upload page
      navigate(`/applications/${response.data.id}/documents`);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to create application',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isFetching) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (profiles.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="mb-6">
            <BackButton />
          </div>
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardContent className="py-12 text-center">
              <Building2 className="w-16 h-16 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Business Profiles</h3>
              <p className="text-gray-600 mb-4">
                You need to create a business profile before applying for a permit
              </p>
              <Button onClick={() => navigate('/business-profile')} className="gap-2">
                <Building2 className="w-5 h-5" />
                Create Business Profile
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="space-y-1 pb-6">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                New Permit Application
              </CardTitle>
              <CardDescription className="text-base">
                Start your business permit application process
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Application Type */}
                <div className="space-y-2">
                  <Label htmlFor="application_type">
                    Application Type <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none z-10" />
                    <Select
                      id="application_type"
                      {...register('application_type')}
                      error={errors.application_type?.message}
                      className="pl-10"
                    >
                      <option value="">Select application type</option>
                      <option value="new">New Business Permit</option>
                      <option value="renewal">Permit Renewal</option>
                    </Select>
                  </div>
                  {errors.application_type && (
                    <p className="text-sm text-red-600">{errors.application_type.message}</p>
                  )}
                  <p className="text-xs text-gray-500">
                    Choose "New" for first-time applications or "Renewal" for existing permits
                  </p>
                </div>

                {/* Business Profile */}
                <div className="space-y-2">
                  <Label htmlFor="business_profile_id">
                    Select Business <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <Building2 className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none z-10" />
                    <Select
                      id="business_profile_id"
                      {...register('business_profile_id')}
                      error={errors.business_profile_id?.message}
                      className="pl-10"
                    >
                      <option value="">Select a business profile</option>
                      {profiles.map((profile) => (
                        <option key={profile.id} value={profile.id}>
                          {profile.business_name}
                          {profile.business_type && ` - ${profile.business_type}`}
                        </option>
                      ))}
                    </Select>
                  </div>
                  {errors.business_profile_id && (
                    <p className="text-sm text-red-600">{errors.business_profile_id.message}</p>
                  )}
                </div>

                {/* Info Box */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">Next Steps:</h4>
                  <ol className="list-decimal list-inside space-y-1 text-sm text-blue-800">
                    <li>Create your application (this form)</li>
                    <li>Upload required documents (next page)</li>
                    <li>Review and submit your application</li>
                    <li>Wait for approval from the city office</li>
                  </ol>
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 text-base font-semibold"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Creating Application...
                    </>
                  ) : (
                    <>
                      Continue to Document Upload
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default ApplicationFormPage;

