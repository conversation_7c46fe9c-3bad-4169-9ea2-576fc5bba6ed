import profileRoutes from './routes/profile.js';
import businessRoutes from './routes/business.js';
const applicationRoutes = require('./routes/applications.js');
const documentRoutes = require('./routes/documents.js');
import notificationRoutes from './routes/notifications.js';
import feedbackRoutes from './routes/feedback.js';
=======
import authRoutes from './routes/auth.js';
import profileRoutes from './routes/profile.js';
import businessRoutes from './routes/business.js';
const applicationRoutes = require('./routes/applications.js');
const documentRoutes = require('./routes/documents.js');
import notificationRoutes from './routes/notifications.js';
import feedbackRoutes from './routes/feedback.js';
import aiRoutes from './routes/ai.js';
