// Document Upload Page - Upload Application Documents
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Upload, FileText, Trash2, Loader2, Send } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Label } from '@/components/ui/Label';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import BackButton from '@/components/ui/BackButton';

import { documentService, DocumentUpload } from '@/services/documentService';
import { applicationService, BusinessApplication } from '@/services/applicationService';
import { useToast } from '@/hooks/useToast';

const DocumentUploadPage: React.FC = () => {
  const { applicationId } = useParams<{ applicationId: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [application, setApplication] = useState<BusinessApplication | null>(null);
  const [documents, setDocuments] = useState<DocumentUpload[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [documentType, setDocumentType] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    if (applicationId) {
      fetchApplication();
      fetchDocuments();
    }
  }, [applicationId]);

  const fetchApplication = async () => {
    try {
      const response = await applicationService.getApplicationById(applicationId!);
      setApplication(response.data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load application',
      });
      navigate('/applications');
    }
  };

  const fetchDocuments = async () => {
    try {
      setIsFetching(true);
      const response = await documentService.getDocumentsByApplication(applicationId!);
      setDocuments(response.data);
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load documents',
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: 'destructive',
          title: 'File Too Large',
          description: 'File size must not exceed 5MB',
        });
        return;
      }

      // Validate file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        toast({
          variant: 'destructive',
          title: 'Invalid File Type',
          description: 'Only PDF, JPG, and PNG files are allowed',
        });
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !documentType) {
      toast({
        variant: 'destructive',
        title: 'Missing Information',
        description: 'Please select a file and document type',
      });
      return;
    }

    setIsLoading(true);
    try {
      await documentService.uploadDocument(applicationId!, documentType, selectedFile);
      
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Document uploaded successfully!',
      });

      setSelectedFile(null);
      setDocumentType('');
      // Reset file input
      const fileInput = document.getElementById('file-upload') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
      
      fetchDocuments();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Upload Failed',
        description: error.response?.data?.message || 'Failed to upload document',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (documentId: string) => {
    if (!confirm('Are you sure you want to delete this document?')) return;

    try {
      await documentService.deleteDocument(documentId);
      
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Document deleted successfully!',
      });

      fetchDocuments();
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to delete document',
      });
    }
  };

  const handleSubmitApplication = async () => {
    if (documents.length === 0) {
      toast({
        variant: 'destructive',
        title: 'No Documents',
        description: 'Please upload at least one document before submitting',
      });
      return;
    }

    if (!confirm('Are you sure you want to submit this application? You cannot edit it after submission.')) {
      return;
    }

    setIsSubmitting(true);
    try {
      await applicationService.submitApplication(applicationId!);
      
      toast({
        variant: 'success',
        title: 'Application Submitted',
        description: 'Your application has been submitted for review!',
      });

      navigate('/applications');
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Submission Failed',
        description: error.response?.data?.message || 'Failed to submit application',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  };

  if (isFetching) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  const canEdit = application?.status === 'draft';

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="mb-6">
          <BackButton />
        </div>
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Document Upload
          </h1>
          <p className="text-gray-600 mt-1">
            {application?.businessProfile?.business_name || 'Application'} - {application?.application_type === 'new' ? 'New Permit' : 'Renewal'}
          </p>
        </motion.div>

        {/* Upload Form */}
        {canEdit && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>Upload Documents</CardTitle>
                <CardDescription>
                  Upload required documents (PDF, JPG, PNG - Max 5MB per file)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Document Type */}
                <div className="space-y-2">
                  <Label htmlFor="document_type">Document Type</Label>
                  <Select
                    id="document_type"
                    value={documentType}
                    onChange={(e) => setDocumentType(e.target.value)}
                  >
                    <option value="">Select document type</option>
                    <option value="DTI_Registration">DTI Registration</option>
                    <option value="Barangay_Clearance">Barangay Clearance</option>
                    <option value="Fire_Safety_Certificate">Fire Safety Certificate</option>
                    <option value="Sanitary_Permit">Sanitary Permit</option>
                    <option value="Tax_Clearance">Tax Clearance</option>
                    <option value="Valid_ID">Valid ID</option>
                    <option value="Other">Other</option>
                  </Select>
                </div>

                {/* File Upload */}
                <div className="space-y-2">
                  <Label htmlFor="file-upload">Select File</Label>
                  <Input
                    id="file-upload"
                    type="file"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={handleFileChange}
                  />
                  {selectedFile && (
                    <p className="text-sm text-gray-600">
                      Selected: {selectedFile.name} ({formatFileSize(selectedFile.size)})
                    </p>
                  )}
                </div>

                {/* Upload Button */}
                <Button
                  onClick={handleUpload}
                  disabled={isLoading || !selectedFile || !documentType}
                  className="w-full gap-2"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      Upload Document
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Documents List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader>
              <CardTitle>Uploaded Documents ({documents.length})</CardTitle>
            </CardHeader>
            <CardContent>
              {documents.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 mx-auto text-gray-300 mb-3" />
                  <p className="text-gray-600">No documents uploaded yet</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {documents.map((doc) => (
                    <div
                      key={doc.id}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <FileText className="w-5 h-5 text-blue-600" />
                        <div>
                          <p className="font-medium text-gray-900">{doc.document_type.replace(/_/g, ' ')}</p>
                          <p className="text-sm text-gray-600">
                            {doc.file_name} • {formatFileSize(doc.file_size)}
                          </p>
                        </div>
                      </div>
                      {canEdit && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(doc.id)}
                          className="gap-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <Trash2 className="w-4 h-4" />
                          Delete
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Submit Application */}
        {canEdit && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="shadow-xl border-0 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold mb-1">Ready to Submit?</h3>
                    <p className="text-blue-100 text-sm">
                      Make sure all required documents are uploaded before submitting
                    </p>
                  </div>
                  <Button
                    onClick={handleSubmitApplication}
                    disabled={isSubmitting || documents.length === 0}
                    className="bg-white text-blue-600 hover:bg-blue-50 gap-2"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4" />
                        Submit Application
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default DocumentUploadPage;

