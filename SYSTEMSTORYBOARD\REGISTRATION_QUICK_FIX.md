# QUICK REFERENCE - Registration Fix & Startup

## 🚀 FASTEST STARTUP (Copy-Paste)

### Terminal 1: Backend
```bash
cd "C:\Users\<USER>\OneDrive\Desktop\MINI THESIS\TEST 2"
node server.js
```

### Terminal 2: Frontend  
```bash
cd "C:\Users\<USER>\OneDrive\Desktop\MINI THESIS\TEST 2"
npm run dev
```

## 🌐 Access Points

| Component | URL |
|-----------|-----|
| Frontend | http://localhost:5173 |
| Backend API | http://localhost:3001/api |
| Health Check | http://localhost:3001/health |

## 🧪 Quick Test

1. Open: http://localhost:5173/register
2. Use this data:
   ```
   Email: <EMAIL>
   Full Name: Test User
   Contact: +63**********
   Password: TestPass123!
   Confirm: TestPass123!
   ```
3. Click "Create Account"
4. Should redirect to dashboard with welcome message

## ✅ What Was Fixed

**File:** `routes/auth.js` (Lines 35-53)
- Added support for both `contactNumber` and `contact_number` field names
- Ensures phone number is properly validated and stored
- Prevents field name mismatches

## 🔍 If It Fails

### Check Backend is Running
```bash
curl http://localhost:3001/health
```

### Check in Browser Console (F12)
- Look for JavaScript errors
- Check Network tab → POST /api/auth/register
- Response should have status 201 with token

### Check Backend Console
- Look for "Registration error: ..." messages
- Verify database connection message appears

## 📋 Expected Output

### Backend (Terminal 1)
```
Database connection established successfully.
Database synchronized successfully.
🚀 Smart Governance Auth API Server running on port 3001
📊 Health check available at: http://localhost:3001/health
🔐 Environment: development
```

### Frontend (Terminal 2)
```
VITE v5.0.7  ready in XXX ms

➜  Local:   http://localhost:5173/
```

## 💾 Database

Verify user was created:
```bash
psql -U postgres -d smart_governance_auth

SELECT email, full_name, contact_number, is_verified
FROM client_accounts WHERE email = '<EMAIL>';
```

## 📞 Contact Number Formats

✅ Valid:
- `+63**********`
- `0**********`

❌ Invalid:
- `63**********`
- `**********`
- `+*********`

## 🔐 Password Requirements

✅ Must have:
- Minimum 8 characters
- Uppercase letter (A-Z)
- Lowercase letter (a-z)
- Number (0-9)
- Special character (!@#$%^&*)

Example: `TestPass123!`

## 📊 Files Modified/Created

- ✅ `routes/auth.js` - FIXED
- 📄 `REGISTRATION_DEBUG_GUIDE.md` - NEW (detailed guide)
- 📄 `REGISTRATION_ANALYSIS_REPORT.md` - NEW (technical analysis)

## ⏭️ Next Steps

1. Run both terminals ▶️
2. Test registration 🧪
3. Check for errors 🔍
4. Query database 💾
5. Happy coding! 🎉
