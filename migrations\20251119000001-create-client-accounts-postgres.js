'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Create custom ENUM type for PostgreSQL
    await queryInterface.sequelize.query(`
      DO $$ BEGIN
        CREATE TYPE otp_purpose AS ENUM ('Registration', 'Password Reset');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create client_accounts table with PostgreSQL data types
    await queryInterface.createTable('client_accounts', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        comment: 'UUID v4 primary key for secure identifiers',
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
        comment: 'Unique email address for user login',
      },
      password_hash: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'bcrypt-hashed password',
      },
      full_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'User\'s full name',
      },
      contact_number: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'Optional phone number',
      },
      is_verified: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Email/OTP verification status',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'JSONB field for flexible metadata storage',
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        comment: 'Account creation timestamp',
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        comment: 'Last modification timestamp',
      },
    });

    // Create otp_verification table with PostgreSQL features
    await queryInterface.createTable('otp_verification', {
      id: {
        allowNull: false,
        primaryKey: true,
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        comment: 'UUID v4 for secure OTP identifiers',
      },
      client_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'client_accounts',
          key: 'id',
        },
        onDelete: 'CASCADE',
        comment: 'Foreign key to client_accounts.id',
      },
      otp_code: {
        type: Sequelize.STRING(6),
        allowNull: false,
        comment: '6-digit OTP code',
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: 'OTP expiration timestamp',
      },
      is_used: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'OTP usage status',
      },
      purpose: {
        type: 'otp_purpose', // Custom PostgreSQL ENUM type
        allowNull: false,
        defaultValue: 'Registration',
        comment: 'OTP purpose (Registration or Password Reset)',
      },
      attempts: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Rate limiting counter (max 5 attempts)',
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        comment: 'OTP generation timestamp',
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        comment: 'Last modification timestamp',
      },
    });

    // Create indexes for client_accounts
    await queryInterface.addIndex('client_accounts', ['email'], {
      name: 'idx_client_accounts_email_search',
      using: 'btree',
    });
    await queryInterface.addIndex('client_accounts', ['is_verified'], {
      name: 'idx_client_accounts_is_verified',
      using: 'btree',
    });
    await queryInterface.addIndex('client_accounts', ['created_at'], {
      name: 'idx_client_accounts_created_at',
      using: 'btree',
    });
    await queryInterface.addIndex('client_accounts', ['metadata'], {
      name: 'idx_client_accounts_metadata',
      using: 'gin', // GIN index for JSONB
    });


  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order (due to foreign key constraints)
    await queryInterface.dropTable('client_accounts');

    // Drop custom ENUM type
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS otp_purpose;');
  },
};
