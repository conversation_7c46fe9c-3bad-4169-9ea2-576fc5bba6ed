// Feedback Service - Feedback Submission Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import apiClient from './apiClient';

export interface FeedbackSubmission {
  id: string;
  client_id: string;
  application_id: string | null;
  rating: number;
  comments: string | null;
  submitted_at: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface SubmitFeedbackData {
  application_id?: string;
  rating: number;
  comments?: string;
}

export interface FeedbackStats {
  average_rating: number;
  total_submissions: number;
  rating_distribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

export const feedbackService = {
  /**
   * Submit feedback
   */
  submitFeedback: async (data: SubmitFeedbackData) => {
    const response = await apiClient.post<{ success: boolean; message: string; data: FeedbackSubmission }>(
      '/feedback',
      data
    );
    return response.data;
  },

  /**
   * Get feedback submissions for authenticated user
   */
  getFeedback: async () => {
    const response = await apiClient.get<{ success: boolean; data: FeedbackSubmission[] }>(
      '/feedback'
    );
    return response.data;
  },

  /**
   * Get feedback statistics
   */
  getFeedbackStats: async () => {
    const response = await apiClient.get<{ success: boolean; data: FeedbackStats }>(
      '/feedback/stats'
    );
    return response.data;
  },
};

