// Profile Controller - User Profile Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Handle user profile operations (GET, PUT)

const { ClientAccount } = require('../models');
const bcrypt = require('bcrypt');

/**
 * Get user profile
 * @route GET /api/profile
 * @access Private
 */
const getProfile = async (req, res) => {
  try {
    const user = await ClientAccount.findByPk(req.user.userId, {
      attributes: ['id', 'email', 'full_name', 'contact_number', 'is_verified', 'metadata', 'created_at', 'updated_at'],
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found.',
      });
    }

    res.status(200).json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve profile.',
    });
  }
};

/**
 * Update user profile
 * @route PUT /api/profile
 * @access Private
 */
const updateProfile = async (req, res) => {
  try {
    const { full_name, contact_number, current_password, new_password } = req.body;

    const user = await ClientAccount.findByPk(req.user.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found.',
      });
    }

    // Update basic fields
    if (full_name !== undefined) {
      if (full_name.trim().length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Full name must be at least 2 characters.',
        });
      }
      user.full_name = full_name.trim();
    }

    if (contact_number !== undefined) {
      if (contact_number && !/^[\+]?[1-9][\d]{0,15}$/.test(contact_number)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid phone number format.',
        });
      }
      user.contact_number = contact_number || null;
    }

    // Update password if provided
    if (new_password) {
      if (!current_password) {
        return res.status(400).json({
          success: false,
          message: 'Current password is required to change password.',
        });
      }

      // Verify current password
      const isPasswordValid = await user.validatePassword(current_password);
      if (!isPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Current password is incorrect.',
        });
      }

      // Validate new password
      if (new_password.length < 8) {
        return res.status(400).json({
          success: false,
          message: 'New password must be at least 8 characters.',
        });
      }

      // Hash and update password
      const saltRounds = 10;
      user.password_hash = await bcrypt.hash(new_password, saltRounds);
    }

    await user.save();

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully.',
      data: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        contact_number: user.contact_number,
        is_verified: user.is_verified,
        updated_at: user.updated_at,
      },
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile.',
    });
  }
};

module.exports = {
  getProfile,
  updateProfile,
};

