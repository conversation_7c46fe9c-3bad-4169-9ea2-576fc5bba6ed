# Algorithm 8: Notification System

## Overview
This algorithm manages in-app notifications for users, including creation, retrieval, filtering, marking as read, and unread count tracking for the Smart Governance system.

---

## Algorithm Complexity
- **Time Complexity:** O(n) where n = number of notifications
- **Space Complexity:** O(n) for storing notification list
- **Database Operations:** 1-2 queries (SELECT, UPDATE)

---

## Input Parameters
```typescript
interface NotificationInput {
  client_account_id: UUID;       // From JWT token
  filter?: 'all' | 'unread';     // Optional filter
  notification_id?: UUID;        // For mark as read operation
}
```

## Output
```typescript
interface NotificationOutput {
  success: boolean;
  notifications?: Array<{
    id: UUID;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    is_read: boolean;
    created_at: Date;
    metadata?: object;
  }>;
  unread_count?: number;
}
```

---

## Pseudocode

```
ALGORITHM GetNotifications(client_account_id, filter)
BEGIN
  // Step 1: Build Base Query
  query ← "SELECT * FROM notifications WHERE client_account_id = ?"
  params ← [client_account_id]
  
  // Step 2: Apply Filter
  IF filter = 'unread' THEN
    query ← query + " AND is_read = FALSE"
  END IF
  
  // Step 3: Add Sorting (newest first)
  query ← query + " ORDER BY created_at DESC"
  
  // Step 4: Execute Query
  notifications ← DATABASE.query(query, params)
  
  // Step 5: Format Timestamps
  FOR EACH notification IN notifications DO
    notification.relative_time ← GET_RELATIVE_TIME(notification.created_at)
  END FOR
  
  // Step 6: Return Results
  RETURN success({
    notifications: notifications,
    total_count: notifications.length
  })
END

ALGORITHM GetUnreadCount(client_account_id)
BEGIN
  // Step 1: Count Unread Notifications
  count ← DATABASE.count(
    "SELECT COUNT(*) FROM notifications 
     WHERE client_account_id = ? AND is_read = FALSE",
    client_account_id
  )
  
  // Step 2: Return Count
  RETURN success({
    unread_count: count
  })
END

ALGORITHM MarkAsRead(notification_id, client_account_id)
BEGIN
  // Step 1: Find Notification
  notification ← DATABASE.query(
    "SELECT * FROM notifications WHERE id = ?",
    notification_id
  )
  
  IF notification NOT EXISTS THEN
    RETURN error(404, "Notification not found")
  END IF
  
  // Step 2: Verify Ownership
  IF notification.client_account_id ≠ client_account_id THEN
    RETURN error(403, "Access denied")
  END IF
  
  // Step 3: Check if Already Read
  IF notification.is_read = TRUE THEN
    RETURN success({ message: "Notification already marked as read" })
  END IF
  
  // Step 4: Update Status
  DATABASE.update("notifications", {
    is_read: TRUE,
    updated_at: CURRENT_TIMESTAMP
  }, WHERE id = notification_id)
  
  // Step 5: Return Success
  RETURN success({
    message: "Notification marked as read"
  })
END

ALGORITHM MarkAllAsRead(client_account_id)
BEGIN
  // Step 1: Update All Unread Notifications
  updated_count ← DATABASE.update("notifications", {
    is_read: TRUE,
    updated_at: CURRENT_TIMESTAMP
  }, WHERE client_account_id = client_account_id AND is_read = FALSE)
  
  // Step 2: Return Success
  RETURN success({
    message: updated_count + " notifications marked as read"
  })
END

ALGORITHM CreateNotification(client_account_id, type, title, message, metadata)
BEGIN
  // Step 1: Validate Type
  IF type NOT IN ['info', 'success', 'warning', 'error'] THEN
    type ← 'info'  // Default to info
  END IF
  
  // Step 2: Generate UUID
  notification_id ← UUID.generate_v4()
  
  // Step 3: Insert Notification
  notification ← DATABASE.insert("notifications", {
    id: notification_id,
    client_account_id: client_account_id,
    type: type,
    title: title,
    message: message,
    is_read: FALSE,
    metadata: metadata OR {},
    created_at: CURRENT_TIMESTAMP,
    updated_at: CURRENT_TIMESTAMP
  })
  
  // Step 4: Return Created Notification
  RETURN notification
END

FUNCTION GET_RELATIVE_TIME(timestamp)
BEGIN
  now ← CURRENT_TIMESTAMP
  diff_seconds ← (now - timestamp) / 1000
  
  IF diff_seconds < 60 THEN
    RETURN "Just now"
  ELSE IF diff_seconds < 3600 THEN
    minutes ← FLOOR(diff_seconds / 60)
    RETURN minutes + " minute" + (minutes > 1 ? "s" : "") + " ago"
  ELSE IF diff_seconds < 86400 THEN
    hours ← FLOOR(diff_seconds / 3600)
    RETURN hours + " hour" + (hours > 1 ? "s" : "") + " ago"
  ELSE
    days ← FLOOR(diff_seconds / 86400)
    RETURN days + " day" + (days > 1 ? "s" : "") + " ago"
  END IF
END
```

---

## Flowchart (Get Notifications)

```mermaid
flowchart TD
    Start([Start: Get Notifications]) --> Auth{User<br/>Authenticated?}
    Auth -->|No| ErrorAuth[Return Error 401]
    Auth -->|Yes| BuildQuery[Build base query:<br/>WHERE client_account_id = ?]
    BuildQuery --> HasFilter{Filter =<br/>'unread'?}
    HasFilter -->|Yes| AddFilter[Add WHERE clause:<br/>is_read = FALSE]
    HasFilter -->|No| AddSorting
    AddFilter --> AddSorting[Add ORDER BY:<br/>created_at DESC]
    AddSorting --> ExecuteQuery[Execute database query]
    ExecuteQuery --> FormatTime[For each notification:<br/>Calculate relative time]
    FormatTime --> Success[Return Success:<br/>Notifications list]
    Success --> End([End])
    ErrorAuth --> End
```

---

## Flowchart (Mark as Read)

```mermaid
flowchart TD
    Start([Start: Mark as Read]) --> FindNotif[Query: Find notification<br/>by ID]
    FindNotif --> NotifExists{Notification<br/>Found?}
    NotifExists -->|No| ErrorNotFound[Return Error 404]
    NotifExists -->|Yes| CheckOwner{User Owns<br/>Notification?}
    CheckOwner -->|No| ErrorAccess[Return Error 403]
    CheckOwner -->|Yes| AlreadyRead{Already<br/>Read?}
    AlreadyRead -->|Yes| SuccessAlready[Return Success:<br/>Already marked]
    AlreadyRead -->|No| UpdateStatus[Update is_read = TRUE]
    UpdateStatus --> Success[Return Success:<br/>Marked as read]
    Success --> End([End])
    SuccessAlready --> End
    ErrorNotFound --> End
    ErrorAccess --> End
```

---

## Notification Types

| Type | Color | Icon | Use Case |
|------|-------|------|----------|
| info | Blue | Info | General information, updates |
| success | Green | CheckCircle | Successful actions, approvals |
| warning | Yellow | AlertTriangle | Warnings, pending actions |
| error | Red | XCircle | Errors, rejections |

---

## Notification Triggers

### Automatic Notifications
1. **Application Created** - Info notification when user creates application
2. **Application Submitted** - Success notification when application submitted
3. **Application Approved** - Success notification (admin action)
4. **Application Rejected** - Error notification with reason (admin action)
5. **Document Uploaded** - Info notification for each document
6. **Status Changed** - Info notification for any status change

---

## Database Tables Involved

### notifications
- **Operations:** SELECT, INSERT, UPDATE
- **Fields:** id, client_account_id, type, title, message, is_read, metadata, created_at, updated_at
- **Indexes:** btree on client_account_id, btree on is_read, btree on created_at

---

## Frontend Integration

### Unread Badge
```typescript
// Display unread count in navigation
<Bell className="h-5 w-5" />
{unreadCount > 0 && (
  <Badge variant="error">{unreadCount}</Badge>
)}
```

### Notification Card
```typescript
<Card className={notification.is_read ? 'opacity-60' : ''}>
  <Icon type={notification.type} />
  <div>
    <h4>{notification.title}</h4>
    <p>{notification.message}</p>
    <span>{notification.relative_time}</span>
  </div>
  {!notification.is_read && (
    <Button onClick={() => markAsRead(notification.id)}>
      Mark as read
    </Button>
  )}
</Card>
```

### Real-time Updates
```typescript
// Poll for new notifications every 30 seconds
useEffect(() => {
  const interval = setInterval(() => {
    fetchUnreadCount();
  }, 30000);
  return () => clearInterval(interval);
}, []);
```

---

## Implementation Files

- **Controller:** `controllers/notificationController.js`
- **Model:** `models/Notification.js`
- **Routes:** `routes/notifications.js`
- **Frontend:** `src/pages/NotificationsPage.tsx`
- **Service:** `src/services/notificationService.ts`

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

