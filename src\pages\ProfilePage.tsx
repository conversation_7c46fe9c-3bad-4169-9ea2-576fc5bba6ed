// Profile Page - User Profile Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { motion } from 'framer-motion';
import { User, Phone, Lock, Save, Loader2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Label } from '@/components/ui/Label';
import BackButton from '@/components/ui/BackButton';
import { profileUpdateSchema, ProfileUpdateFormData } from '@/schemas/businessSchemas';
import { profileService, UserProfile } from '@/services/profileService';
import { useToast } from '@/hooks/useToast';

const ProfilePage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<ProfileUpdateFormData>({
    resolver: yupResolver(profileUpdateSchema),
    mode: 'onBlur',
  });

  const newPassword = watch('new_password');

  useEffect(() => {
    fetchProfile();
  }, []);

  const fetchProfile = async () => {
    try {
      setIsFetching(true);
      const response = await profileService.getProfile();
      setProfile(response.data);
      reset({
        full_name: response.data.full_name,
        contact_number: response.data.contact_number || '',
      });
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to load profile',
      });
    } finally {
      setIsFetching(false);
    }
  };

  const onSubmit = async (data: ProfileUpdateFormData) => {
    setIsLoading(true);
    try {
      const updateData: any = {};
      
      if (data.full_name && data.full_name !== profile?.full_name) {
        updateData.full_name = data.full_name;
      }
      
      if (data.contact_number !== profile?.contact_number) {
        updateData.contact_number = data.contact_number || null;
      }
      
      if (data.new_password) {
        updateData.current_password = data.current_password;
        updateData.new_password = data.new_password;
      }

      if (Object.keys(updateData).length === 0) {
        toast({
          variant: 'default',
          title: 'No Changes',
          description: 'No changes were made to your profile.',
        });
        return;
      }

      const response = await profileService.updateProfile(updateData);
      setProfile(response.data);
      
      toast({
        variant: 'success',
        title: 'Success',
        description: 'Profile updated successfully!',
      });

      // Reset password fields
      reset({
        full_name: response.data.full_name,
        contact_number: response.data.contact_number || '',
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update profile',
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isFetching) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="mb-6">
          <BackButton />
        </div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="space-y-1 pb-6">
              <CardTitle className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Profile Settings
              </CardTitle>
              <CardDescription className="text-base">
                Manage your account information and security settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Account Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Account Information</h3>
                  
                  {/* Email (Read-only) */}
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile?.email || ''}
                      disabled
                      className="bg-gray-50"
                    />
                    <p className="text-xs text-gray-500">Email cannot be changed</p>
                  </div>

                  {/* Full Name */}
                  <div className="space-y-2">
                    <Label htmlFor="full_name">
                      Full Name <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Input
                        id="full_name"
                        {...register('full_name')}
                        error={errors.full_name?.message}
                        className="pl-10"
                        placeholder="Enter your full name"
                      />
                    </div>
                    {errors.full_name && (
                      <p className="text-sm text-red-600">{errors.full_name.message}</p>
                    )}
                  </div>

                  {/* Contact Number */}
                  <div className="space-y-2">
                    <Label htmlFor="contact_number">Contact Number</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Input
                        id="contact_number"
                        {...register('contact_number')}
                        error={errors.contact_number?.message}
                        className="pl-10"
                        placeholder="+63 XXX XXX XXXX"
                      />
                    </div>
                    {errors.contact_number && (
                      <p className="text-sm text-red-600">{errors.contact_number.message}</p>
                    )}
                  </div>
                </div>

                {/* Divider */}
                <div className="border-t border-gray-200 my-6"></div>

                {/* Password Change */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Change Password</h3>
                  <p className="text-sm text-gray-600">Leave blank if you don't want to change your password</p>
                  
                  {/* Current Password */}
                  <div className="space-y-2">
                    <Label htmlFor="current_password">Current Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Input
                        id="current_password"
                        type="password"
                        {...register('current_password')}
                        error={errors.current_password?.message}
                        className="pl-10"
                        placeholder="Enter current password"
                      />
                    </div>
                    {errors.current_password && (
                      <p className="text-sm text-red-600">{errors.current_password.message}</p>
                    )}
                  </div>

                  {/* New Password */}
                  <div className="space-y-2">
                    <Label htmlFor="new_password">New Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <Input
                        id="new_password"
                        type="password"
                        {...register('new_password')}
                        error={errors.new_password?.message}
                        className="pl-10"
                        placeholder="Enter new password (min. 8 characters)"
                      />
                    </div>
                    {errors.new_password && (
                      <p className="text-sm text-red-600">{errors.new_password.message}</p>
                    )}
                  </div>

                  {/* Confirm Password */}
                  {newPassword && (
                    <div className="space-y-2">
                      <Label htmlFor="confirm_password">Confirm New Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                        <Input
                          id="confirm_password"
                          type="password"
                          {...register('confirm_password')}
                          error={errors.confirm_password?.message}
                          className="pl-10"
                          placeholder="Confirm new password"
                        />
                      </div>
                      {errors.confirm_password && (
                        <p className="text-sm text-red-600">{errors.confirm_password.message}</p>
                      )}
                    </div>
                  )}
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-12 text-base font-semibold"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Saving Changes...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-5 w-5" />
                      Save Changes
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default ProfilePage;

