#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Comprehensive login flow diagnostic and testing script
.DESCRIPTION
    Tests the entire authentication flow from backend to frontend
#>

Write-Host "╔════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║  Smart Governance System - Login Flow Diagnostic Test          ║" -ForegroundColor Cyan
Write-Host "╚════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# Kill any existing Node processes
Write-Host "Step 1: Cleaning up existing Node processes..." -ForegroundColor Yellow
taskkill /f /im node.exe 2>$null
Start-Sleep -Seconds 2

# Start the backend server
Write-Host "Step 2: Starting backend server..." -ForegroundColor Yellow
$serverProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -PassThru -NoNewWindow -RedirectStandardOutput "server.log" -RedirectStandardError "server-error.log"
$serverId = $serverProcess.Id
Write-Host "✓ Server started with PID: $serverId" -ForegroundColor Green
Start-Sleep -Seconds 3

# Test health endpoint
Write-Host ""
Write-Host "Step 3: Testing health endpoint..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:3001/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
    $healthData = $healthResponse.Content | ConvertFrom-Json
    Write-Host "✓ Health check passed" -ForegroundColor Green
    Write-Host "  Status: $($healthData.status)" -ForegroundColor Gray
    Write-Host "  Database: $($healthData.database)" -ForegroundColor Gray
} catch {
    Write-Host "✗ Health check failed: $_" -ForegroundColor Red
}

# Test login endpoint
Write-Host ""
Write-Host "Step 4: Testing login endpoint with admin credentials..." -ForegroundColor Yellow
$loginPayload = @{
    email = "<EMAIL>"
    password = "AdminPass123!"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-WebRequest -Uri "http://localhost:3001/api/auth/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body $loginPayload -TimeoutSec 5 -ErrorAction Stop
    $loginData = $loginResponse.Content | ConvertFrom-Json
    
    Write-Host "✓ Login endpoint responded with HTTP $($loginResponse.StatusCode)" -ForegroundColor Green
    Write-Host ""
    Write-Host "Response Structure:" -ForegroundColor Yellow
    Write-Host "  success: $($loginData.success)" -ForegroundColor Gray
    Write-Host "  message: $($loginData.message)" -ForegroundColor Gray
    
    if ($loginData.data) {
        Write-Host "  data:" -ForegroundColor Gray
        Write-Host "    token: $(if ($loginData.data.token) { $loginData.data.token.Substring(0, 20) + '...' } else { 'NULL' })" -ForegroundColor Gray
        if ($loginData.data.user) {
            Write-Host "    user:" -ForegroundColor Gray
            Write-Host "      id: $($loginData.data.user.id)" -ForegroundColor Gray
            Write-Host "      email: $($loginData.data.user.email)" -ForegroundColor Gray
            Write-Host "      fullName: $($loginData.data.user.fullName)" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ✗ data field missing!" -ForegroundColor Red
    }
    
} catch {
    Write-Host "✗ Login endpoint test failed" -ForegroundColor Red
    Write-Host "  Error: $_" -ForegroundColor Red
}

# Check server logs
Write-Host ""
Write-Host "Step 5: Server logs (last 5 lines)..." -ForegroundColor Yellow
if (Test-Path "server.log") {
    Get-Content "server.log" | Select-Object -Last 5 | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
}

# Stop the server
Write-Host ""
Write-Host "Step 6: Stopping server..." -ForegroundColor Yellow
Stop-Process -Id $serverId -Force -ErrorAction SilentlyContinue
Write-Host "✓ Server stopped" -ForegroundColor Green

Write-Host ""
Write-Host "╔════════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║  Diagnostic Test Complete                                      ║" -ForegroundColor Cyan
Write-Host "╚════════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
