-- PostgreSQL Database Schema for Smart Governance User Authentication Module
-- BSCS Mini-Thesis Project - Holy Trinity College PCMRC
-- Created: November 19, 2025 (Migrated from MySQL)
-- Purpose: Complete database schema for secure user authentication with OTP verification
-- Security: Implements bcrypt password hashing, OTP expiration, and referential integrity
-- Standards: PostgreSQL 16+ with uuid-ossp extension, 3NF normalization, 2025 security best practices

-- Drop existing database if it exists (for clean re-creation)
DROP DATABASE IF EXISTS smart_governance_auth;

-- Create database with UTF8 encoding for full Unicode support
CREATE DATABASE smart_governance_auth
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TEMPLATE = template0;

-- Connect to the database
\c smart_governance_auth;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp"; -- For UUID generation
CREATE EXTENSION IF NOT EXISTS "pgcrypto"; -- For additional cryptographic functions (future use)

-- Create custom ENUM type for OTP purposes
-- PostgreSQL ENUM provides better type safety than MySQL ENUM
CREATE TYPE otp_purpose AS ENUM ('Registration', 'Password Reset');

-- Table 1: client_accounts
-- Purpose: Store registered user information with secure password storage and verification status
-- Security: Passwords are hashed using bcrypt with cost factor 10+ (never store plain text)
-- Normalization: 3NF compliant - all non-key attributes depend directly on the primary key
-- PostgreSQL Advantage: Native UUID support, JSONB for future flexible metadata
CREATE TABLE client_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- UUID v4 for secure, non-sequential identifiers
    email VARCHAR(255) NOT NULL UNIQUE, -- UNIQUE constraint prevents duplicate registrations
    password_hash VARCHAR(255) NOT NULL, -- bcrypt-hashed password (60+ characters)
    full_name VARCHAR(255) NOT NULL, -- User's full name (2-255 characters as per validation)
    contact_number VARCHAR(50) NULL, -- Optional phone number for future SMS OTP
    is_verified BOOLEAN NOT NULL DEFAULT FALSE, -- Email/OTP verification status
    metadata JSONB DEFAULT '{}'::jsonb, -- PostgreSQL JSONB for flexible future extensions (notifications, preferences)
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Account creation timestamp with timezone
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP -- Last modification timestamp with timezone
);

-- Indexes for client_accounts table
-- PostgreSQL automatically creates indexes for PRIMARY KEY and UNIQUE constraints
CREATE INDEX CONCURRENTLY idx_client_accounts_email_search ON client_accounts(email); -- Fast login queries (redundant but explicit)
CREATE INDEX CONCURRENTLY idx_client_accounts_is_verified ON client_accounts(is_verified); -- Filter verified users
CREATE INDEX CONCURRENTLY idx_client_accounts_created_at ON client_accounts(created_at); -- For user analytics

-- Add table comment for documentation
COMMENT ON TABLE client_accounts IS 'Primary table storing registered user accounts with secure authentication data';
COMMENT ON COLUMN client_accounts.id IS 'UUID v4 primary key for secure, non-enumerable identifiers';
COMMENT ON COLUMN client_accounts.email IS 'Unique email address for user login and verification';
COMMENT ON COLUMN client_accounts.password_hash IS 'bcrypt-hashed password with cost factor 10+';
COMMENT ON COLUMN client_accounts.metadata IS 'JSONB field for flexible future extensions (notifications, preferences, etc.)';

-- Table 2: otp_verification
-- Purpose: Store one-time passwords for email verification and password reset with expiration tracking
-- Security: OTP codes expire after 5 minutes, cannot be reused after verification
-- Relationship: One-to-Many (client_accounts 1:N otp_verification) with CASCADE deletion
-- PostgreSQL Advantage: Better ENUM support, advanced constraints, timezone-aware timestamps
CREATE TABLE otp_verification (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), -- UUID v4 for secure identifiers
    client_id UUID NOT NULL, -- Foreign key to client_accounts.id
    otp_code VARCHAR(6) NOT NULL
        CHECK (otp_code ~ '^[0-9]{6}$'), -- PostgreSQL CHECK constraint for 6-digit validation
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Expiration timestamp with timezone (typically NOW() + 5 minutes)
    is_used BOOLEAN NOT NULL DEFAULT FALSE, -- Prevents OTP reuse after verification
    purpose otp_purpose NOT NULL DEFAULT 'Registration', -- Custom ENUM type for OTP use case
    attempts INTEGER NOT NULL DEFAULT 0
        CHECK (attempts >= 0 AND attempts <= 5), -- Rate limiting: max 5 verification attempts
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- OTP generation timestamp
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Last modification timestamp
    -- Foreign key constraint with CASCADE deletion for referential integrity
    CONSTRAINT fk_otp_verification_client_id
        FOREIGN KEY (client_id) REFERENCES client_accounts(id) ON DELETE CASCADE
);

-- Indexes for otp_verification table
CREATE INDEX CONCURRENTLY idx_otp_verification_client_id ON otp_verification(client_id); -- Foreign key performance
-- Composite index for fast OTP verification queries (most common operation)
CREATE INDEX CONCURRENTLY idx_otp_verification_lookup ON otp_verification(otp_code, is_used, expires_at);
CREATE INDEX CONCURRENTLY idx_otp_verification_expires_at ON otp_verification(expires_at); -- Cleanup expired OTPs
CREATE INDEX CONCURRENTLY idx_otp_verification_purpose ON otp_verification(purpose); -- Filter by OTP type

-- Add table comment for documentation
COMMENT ON TABLE otp_verification IS 'Stores one-time passwords with expiration and reuse prevention';
COMMENT ON COLUMN otp_verification.otp_code IS '6-digit numeric code with CHECK constraint validation';
COMMENT ON COLUMN otp_verification.attempts IS 'Rate limiting counter (max 5 attempts per OTP)';
COMMENT ON COLUMN otp_verification.purpose IS 'Custom ENUM type for Registration or Password Reset';

-- Create a partial index for active OTPs (commonly queried)
CREATE INDEX CONCURRENTLY idx_otp_verification_active ON otp_verification(client_id, created_at DESC)
WHERE is_used = FALSE AND expires_at > CURRENT_TIMESTAMP;

-- PostgreSQL-specific security: Row Level Security (RLS) for future multi-tenant features
-- ALTER TABLE client_accounts ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE otp_verification ENABLE ROW LEVEL SECURITY;

-- Create a function for automatic OTP cleanup (can be called by a scheduled job)
CREATE OR REPLACE FUNCTION cleanup_expired_otps()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM otp_verification
    WHERE expires_at < CURRENT_TIMESTAMP
       OR (is_used = TRUE AND updated_at < CURRENT_TIMESTAMP - INTERVAL '24 hours');

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Comments for academic documentation
-- This schema implements the Database Design phase of the Waterfall SDLC model
-- PostgreSQL advantages: ACID compliance, JSONB for flexible data, better concurrency
-- Security measures align with OWASP recommendations for password storage and OTP handling
-- Normalization to 3NF eliminates redundancy and ensures data integrity
-- UUID primary keys prevent enumeration attacks and provide global uniqueness
-- Foreign key constraints with CASCADE ensure referential integrity when users are deleted
-- CHECK constraints provide data validation at database level
-- Custom ENUM types improve type safety over MySQL ENUM
-- Timezone-aware timestamps prevent timezone-related bugs
-- JSONB fields enable future schema evolution without migrations
