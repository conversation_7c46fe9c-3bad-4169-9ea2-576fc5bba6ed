// Business Service - Business Profile Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import apiClient from './apiClient';

export interface BusinessProfile {
  id: string;
  client_id: string;
  business_name: string;
  business_type: string | null;
  address: string | null;
  contact_number: string | null;
  tin_number: string | null;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface CreateBusinessProfileData {
  business_name: string;
  business_type?: string;
  address?: string;
  contact_number?: string;
  tin_number?: string;
}

export interface UpdateBusinessProfileData {
  business_name?: string;
  business_type?: string;
  address?: string;
  contact_number?: string;
  tin_number?: string;
}

export const businessService = {
  /**
   * Get all business profiles for authenticated user
   */
  getBusinessProfiles: async () => {
    const response = await apiClient.get<{ success: boolean; data: BusinessProfile[] }>(
      '/business-profile'
    );
    return response.data;
  },

  /**
   * Get single business profile by ID
   */
  getBusinessProfileById: async (id: string) => {
    const response = await apiClient.get<{ success: boolean; data: BusinessProfile }>(
      `/business-profile/${id}`
    );
    return response.data;
  },

  /**
   * Create new business profile
   */
  createBusinessProfile: async (data: CreateBusinessProfileData) => {
    const response = await apiClient.post<{ success: boolean; message: string; data: BusinessProfile }>(
      '/business-profile',
      data
    );
    return response.data;
  },

  /**
   * Update business profile
   */
  updateBusinessProfile: async (id: string, data: UpdateBusinessProfileData) => {
    const response = await apiClient.put<{ success: boolean; message: string; data: BusinessProfile }>(
      `/business-profile/${id}`,
      data
    );
    return response.data;
  },
};

