// Sequelize Model for Business Application - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define BusinessApplication model for permit applications
// Security: UUID primary keys, ENUM validation, status tracking
// Standards: Sequelize v6+ conventions with PostgreSQL data types

const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  const BusinessApplication = sequelize.define('BusinessApplication', {
    // Primary key: UUID v4 for secure identifiers
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
      allowNull: false,
      comment: 'Unique application identifier (UUID v4)',
    },
    // Foreign key to business_profiles table
    business_profile_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'business_profiles',
        key: 'id',
      },
      onDelete: 'CASCADE',
      comment: 'Reference to business profile',
    },
    // Application type: new or renewal
    application_type: {
      type: DataTypes.ENUM('new', 'renewal'),
      allowNull: false,
      validate: {
        isIn: {
          args: [['new', 'renewal']],
          msg: 'Application type must be either new or renewal',
        },
      },
      comment: 'Type of application (new permit or renewal)',
    },
    // Application status with workflow states
    status: {
      type: DataTypes.ENUM('draft', 'submitted', 'under_review', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'draft',
      validate: {
        isIn: {
          args: [['draft', 'submitted', 'under_review', 'approved', 'rejected']],
          msg: 'Status must be one of: draft, submitted, under_review, approved, rejected',
        },
      },
      comment: 'Current status of the application',
    },
    // Submission timestamp
    submitted_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Timestamp when application was submitted',
    },
    // Review completion timestamp
    reviewed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Timestamp when application was reviewed',
    },
    // Admin notes or rejection reasons
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Admin notes or rejection reasons',
    },
    // Additional metadata for flexible extensions
    metadata: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
      comment: 'JSONB field for flexible metadata (fees, requirements, etc.)',
    },
  }, {
    // Table configuration
    tableName: 'business_applications',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      // Foreign key index for performance
      {
        fields: ['business_profile_id'],
        name: 'idx_business_applications_business_profile_id',
        using: 'btree',
      },
      // Status filtering index
      {
        fields: ['status'],
        name: 'idx_business_applications_status',
        using: 'btree',
      },
      // Composite index for status tracking queries
      {
        fields: ['business_profile_id', 'status'],
        name: 'idx_business_applications_profile_status',
        using: 'btree',
      },
      // Submission date index for reporting
      {
        fields: ['submitted_at'],
        name: 'idx_business_applications_submitted_at',
        using: 'btree',
      },
      // GIN index for JSONB metadata queries
      {
        fields: ['metadata'],
        name: 'idx_business_applications_metadata',
        using: 'gin',
      },
    ],
  });

  // Instance method to submit application
  BusinessApplication.prototype.submit = async function() {
    if (this.status !== 'draft') {
      throw new Error('Only draft applications can be submitted');
    }
    return await this.update({
      status: 'submitted',
      submitted_at: new Date(),
    });
  };

  // Instance method to approve application
  BusinessApplication.prototype.approve = async function(notes = null) {
    if (this.status !== 'under_review') {
      throw new Error('Only applications under review can be approved');
    }
    return await this.update({
      status: 'approved',
      reviewed_at: new Date(),
      notes: notes,
    });
  };

  // Instance method to reject application
  BusinessApplication.prototype.reject = async function(notes) {
    if (this.status !== 'under_review') {
      throw new Error('Only applications under review can be rejected');
    }
    if (!notes) {
      throw new Error('Rejection reason is required');
    }
    return await this.update({
      status: 'rejected',
      reviewed_at: new Date(),
      notes: notes,
    });
  };

  // Static method to find applications by status
  BusinessApplication.findByStatus = async function(status) {
    return await this.findAll({
      where: { status: status },
      order: [['created_at', 'DESC']],
    });
  };

  // Static method to find applications by business profile
  BusinessApplication.findByBusinessProfile = async function(businessProfileId) {
    return await this.findAll({
      where: { business_profile_id: businessProfileId },
      order: [['created_at', 'DESC']],
    });
  };

  return BusinessApplication;
};

