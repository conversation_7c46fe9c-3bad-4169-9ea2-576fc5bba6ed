const { Sequelize } = require('sequelize');
const bcrypt = require('bcrypt');

// Load database config
const dbConfig = require('./config/database.js');

// Create Sequelize instance
const sequelize = new Sequelize(dbConfig.development);

async function checkPassword() {
  try {
    console.log('🔍 Checking password hash for accounts...\n');

    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful\n');

    // Query accounts
    const accounts = await sequelize.query(`
      SELECT id, email, password_hash, is_verified, created_at
      FROM client_accounts
      ORDER BY created_at DESC
      LIMIT 5;
    `, { type: Sequelize.QueryTypes.SELECT });

    for (const account of accounts) {
      console.log(`\nAccount: ${account.email}`);
      console.log(`ID: ${account.id}`);
      console.log(`Created: ${account.created_at}`);
      console.log(`Verified: ${account.is_verified}`);
      console.log(`Password Hash: ${account.password_hash.substring(0, 20)}...`);

      // Check if password hash is bcrypt format
      const isHashed = account.password_hash.startsWith('$2a$') || account.password_hash.startsWith('$2b$') || account.password_hash.startsWith('$2y$');
      console.log(`Is Password Hashed: ${isHashed ? 'Yes' : 'No'}`);

      if (isHashed) {
        // Test password comparison with common password
        const plainPassword = 'AdminPass123!';
        const isValid = await bcrypt.compare(plainPassword, account.password_hash);
        console.log(`Password 'AdminPass123!' matches: ${isValid ? 'Yes' : 'No'}`);
      } else {
        console.log('❌ Password is not hashed!');
      }
    }

  } catch (error) {
    console.error('❌ Query failed:', error.message);
  } finally {
    await sequelize.close();
  }
}

// Run the check
checkPassword();
