// Migration: Create business_profiles table - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Create business_profiles table with UUID primary key and foreign key to client_accounts

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('business_profiles', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        comment: 'Unique business profile identifier (UUID v4)',
      },
      client_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'client_accounts',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Reference to user account',
      },
      business_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Registered business name',
      },
      business_type: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Type or category of business',
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Complete business address',
      },
      contact_number: {
        type: Sequelize.STRING(50),
        allowNull: true,
        comment: 'Business contact number',
      },
      tin_number: {
        type: Sequelize.STRING(50),
        allowNull: true,
        unique: true,
        comment: 'Tax Identification Number (unique)',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'JSONB field for flexible metadata',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('business_profiles', ['client_id'], {
      name: 'idx_business_profiles_client_id',
      using: 'btree',
    });

    await queryInterface.addIndex('business_profiles', ['business_name'], {
      name: 'idx_business_profiles_business_name',
      using: 'btree',
    });

    await queryInterface.addIndex('business_profiles', ['tin_number'], {
      name: 'idx_business_profiles_tin_number',
      using: 'btree',
    });

    await queryInterface.addIndex('business_profiles', ['metadata'], {
      name: 'idx_business_profiles_metadata',
      using: 'gin',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('business_profiles');
  },
};

