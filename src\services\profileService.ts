// Profile Service - User Profile Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import apiClient from './apiClient';

export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  contact_number: string | null;
  is_verified: boolean;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface UpdateProfileData {
  full_name?: string;
  contact_number?: string;
  current_password?: string;
  new_password?: string;
}

export const profileService = {
  /**
   * Get user profile
   */
  getProfile: async () => {
    const response = await apiClient.get<{ success: boolean; data: UserProfile }>('/profile');
    return response.data;
  },

  /**
   * Update user profile
   */
  updateProfile: async (data: UpdateProfileData) => {
    const response = await apiClient.put<{ success: boolean; message: string; data: UserProfile }>(
      '/profile',
      data
    );
    return response.data;
  },
};

