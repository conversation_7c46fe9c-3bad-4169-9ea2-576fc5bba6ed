# Diagram 5: Application Submission Sequence Diagram

## Overview
This sequence diagram shows the complete application submission workflow: creating an application in draft status, uploading required documents, submitting for review, creating notifications, and updating status.

---

## Process Flow
1. User selects business profile
2. Create application (draft status)
3. Redirect to document upload page
4. Upload required documents
5. Submit application for review
6. Update status to 'submitted'
7. Create notification
8. Return success response

---

## Mermaid Sequence Diagram

```mermaid
sequenceDiagram
    actor User
    participant UI as React Frontend<br/>(ApplicationFormPage)
    participant DocUI as React Frontend<br/>(DocumentUploadPage)
    participant Axios as Axios + JWT<br/>(applicationService)
    participant Express as Express Server
    participant AppRoute as /api/applications
    participant AppCtrl as applicationController
    participant DocRoute as /api/documents
    participant DocCtrl as documentController
    participant NotifCtrl as notificationController
    participant Model as Sequelize Models
    participant DB as PostgreSQL 16
    participant Storage as Local File System<br/>(uploads/documents/)

    %% Step 1: Create Application (Draft)
    User->>UI: Select business profile
    User->>UI: Choose application type<br/>(new or renewal)
    UI->>Axios: POST /api/applications<br/>{business_profile_id, application_type}
    Note over Axios: JWT token in<br/>Authorization header
    
    Axios->>Express: HTTP POST + JWT
    Express->>AppRoute: Route to POST /api/applications
    AppRoute->>AppCtrl: createApplication(req, res)
    
    %% Verify Business Profile Ownership
    AppCtrl->>Model: BusinessProfile.findByPk(business_profile_id)
    Model->>DB: SELECT * FROM business_profiles<br/>WHERE id = ?
    DB-->>Model: Business profile record
    Model-->>AppCtrl: businessProfile object
    
    AppCtrl->>AppCtrl: Verify ownership:<br/>businessProfile.client_account_id === req.user.id
    
    alt Not Owner
        AppCtrl-->>Express: 403 Forbidden
        Express-->>Axios: Error response
        Axios-->>UI: Display error
        UI-->>User: "Access denied"
    end
    
    %% Create Application in Draft Status
    AppCtrl->>Model: BusinessApplication.create({<br/>business_profile_id,<br/>application_type,<br/>status: 'draft'})
    Model->>DB: INSERT INTO business_applications<br/>(id, business_profile_id, application_type,<br/>status, created_at, updated_at)<br/>VALUES (uuid_generate_v4(), ?, ?, 'draft', NOW(), NOW())
    DB-->>Model: New application record
    Model-->>AppCtrl: application object
    
    %% Create Initial Notification
    AppCtrl->>NotifCtrl: createNotification({<br/>client_account_id,<br/>type: 'info',<br/>title: 'Application Created',<br/>message: 'Draft application created'})
    NotifCtrl->>Model: Notification.create(...)
    Model->>DB: INSERT INTO notifications
    DB-->>Model: Notification created
    Model-->>NotifCtrl: notification object
    NotifCtrl-->>AppCtrl: Success
    
    AppCtrl-->>Express: 201 Created<br/>{application}
    Express-->>Axios: Success response
    Axios-->>UI: Application created
    UI-->>User: "Application created successfully!"
    UI->>DocUI: Navigate to /applications/:id/documents
    
    %% Step 2: Upload Documents
    User->>DocUI: Select document type
    User->>DocUI: Choose file (PDF/JPG/PNG)
    DocUI->>Axios: POST /api/documents/upload<br/>FormData: {application_id, document_type, file}
    Note over Axios: multipart/form-data<br/>Content-Type
    
    Axios->>Express: HTTP POST + JWT + File
    Express->>DocRoute: Route to POST /api/documents/upload
    DocRoute->>DocCtrl: uploadDocument(req, res)
    
    %% Validate File
    DocCtrl->>DocCtrl: Check file exists
    DocCtrl->>DocCtrl: Validate MIME type<br/>(PDF, JPG, PNG only)
    DocCtrl->>DocCtrl: Validate file size<br/>(max 5MB)
    
    alt Invalid File
        DocCtrl-->>Express: 400 Bad Request
        Express-->>Axios: Error response
        Axios-->>DocUI: Display error
        DocUI-->>User: "Invalid file type or size"
    end
    
    %% Verify Application Ownership and Status
    DocCtrl->>Model: BusinessApplication.findByPk(application_id,<br/>{include: BusinessProfile})
    Model->>DB: SELECT a.*, bp.client_account_id<br/>FROM business_applications a<br/>JOIN business_profiles bp ON a.business_profile_id = bp.id<br/>WHERE a.id = ?
    DB-->>Model: Application with business profile
    Model-->>DocCtrl: application object
    
    DocCtrl->>DocCtrl: Verify ownership
    DocCtrl->>DocCtrl: Check status === 'draft'
    
    alt Not Draft Status
        DocCtrl-->>Express: 400 Bad Request
        Express-->>Axios: "Cannot upload to submitted applications"
        Axios-->>DocUI: Display error
        DocUI-->>User: "Application already submitted"
    end
    
    %% Save File to Disk (Multer)
    DocCtrl->>Storage: Save file with unique name<br/>{timestamp}-{uuid}-{original_name}
    Storage-->>DocCtrl: File saved, file_path returned
    
    %% Create Document Record
    DocCtrl->>Model: DocumentUpload.create({<br/>application_id, document_type,<br/>file_path, file_name, file_size, mime_type})
    Model->>DB: INSERT INTO document_uploads<br/>(id, application_id, document_type,<br/>file_path, file_name, file_size, mime_type,<br/>created_at, updated_at)<br/>VALUES (uuid_generate_v4(), ?, ?, ?, ?, ?, ?, NOW(), NOW())
    DB-->>Model: New document record
    Model-->>DocCtrl: document object
    
    DocCtrl-->>Express: 201 Created<br/>{document}
    Express-->>Axios: Success response
    Axios-->>DocUI: Document uploaded
    DocUI-->>User: "Document uploaded successfully!"
    
    Note over User,DocUI: User can upload<br/>multiple documents
    
    %% Step 3: Submit Application
    User->>DocUI: Click "Submit Application"
    DocUI->>Axios: POST /api/applications/:id/submit
    
    Axios->>Express: HTTP POST + JWT
    Express->>AppRoute: Route to POST /api/applications/:id/submit
    AppRoute->>AppCtrl: submitApplication(req, res)
    
    %% Verify Documents Uploaded
    AppCtrl->>Model: DocumentUpload.count({<br/>where: {application_id}})
    Model->>DB: SELECT COUNT(*) FROM document_uploads<br/>WHERE application_id = ?
    DB-->>Model: Document count
    Model-->>AppCtrl: count
    
    alt No Documents
        AppCtrl-->>Express: 400 Bad Request
        Express-->>Axios: "Please upload at least one document"
        Axios-->>DocUI: Display error
        DocUI-->>User: "Upload documents first"
    end
    
    %% Update Application Status
    AppCtrl->>Model: BusinessApplication.update({<br/>status: 'submitted',<br/>submitted_at: new Date()},<br/>{where: {id}})
    Model->>DB: UPDATE business_applications<br/>SET status = 'submitted',<br/>submitted_at = NOW(),<br/>updated_at = NOW()<br/>WHERE id = ?
    DB-->>Model: Update successful
    Model-->>AppCtrl: Updated application
    
    %% Create Submission Notification
    AppCtrl->>NotifCtrl: createNotification({<br/>client_account_id,<br/>type: 'success',<br/>title: 'Application Submitted',<br/>message: 'Your application has been submitted for review'})
    NotifCtrl->>Model: Notification.create(...)
    Model->>DB: INSERT INTO notifications
    DB-->>Model: Notification created
    Model-->>NotifCtrl: notification object
    NotifCtrl-->>AppCtrl: Success
    
    AppCtrl-->>Express: 200 OK<br/>{message, application}
    Express-->>Axios: Success response
    Axios-->>DocUI: Application submitted
    DocUI-->>User: "Application submitted successfully!<br/>You will be notified of the review status."
    DocUI->>UI: Navigate to /applications
```

---

## Status Transitions

### Application Status Flow
1. **draft** - Initial creation, documents can be uploaded/deleted
2. **submitted** - User submitted, awaiting admin review (admin feature)
3. **under_review** - Admin is reviewing (admin feature)
4. **approved** - Application approved (admin feature)
5. **rejected** - Application rejected with notes (admin feature)

### User Module Scope
- ✅ Create application (draft)
- ✅ Upload documents (draft only)
- ✅ Submit application (draft → submitted)
- ❌ Review application (admin feature)
- ❌ Approve/reject (admin feature)

---

## Database Tables Involved

### business_applications
- **Operations:** INSERT, SELECT, UPDATE
- **Status Values:** 'draft', 'submitted', 'under_review', 'approved', 'rejected'

### business_profiles
- **Operations:** SELECT (for ownership verification)

### document_uploads
- **Operations:** INSERT, COUNT

### notifications
- **Operations:** INSERT (2 notifications: created, submitted)

---

**Diagram Status:** ✅ Complete  
**Participants:** 11  
**Steps:** 40+  
**Last Updated:** November 20, 2025

