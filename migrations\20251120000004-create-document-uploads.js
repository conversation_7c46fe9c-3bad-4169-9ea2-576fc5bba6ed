// Migration: Create document_uploads table - PostgreSQL
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Create document_uploads table with UUID primary key and file validation

'use strict';

export default {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('document_uploads', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
        allowNull: false,
        comment: 'Unique document identifier (UUID v4)',
      },
      application_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'business_applications',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'Reference to business application',
      },
      document_type: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Type of document',
      },
      file_path: {
        type: Sequelize.STRING(500),
        allowNull: false,
        comment: 'Server file path',
      },
      file_name: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Original file name',
      },
      mime_type: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'MIME type of the file',
      },
      file_size: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'File size in bytes (max 5MB)',
      },
      uploaded_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        comment: 'Timestamp when file was uploaded',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: false,
        defaultValue: {},
        comment: 'JSONB field for flexible metadata',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Create indexes
    await queryInterface.addIndex('document_uploads', ['application_id'], {
      name: 'idx_document_uploads_application_id',
      using: 'btree',
    });

    await queryInterface.addIndex('document_uploads', ['document_type'], {
      name: 'idx_document_uploads_document_type',
      using: 'btree',
    });

    await queryInterface.addIndex('document_uploads', ['uploaded_at'], {
      name: 'idx_document_uploads_uploaded_at',
      using: 'btree',
    });

    await queryInterface.addIndex('document_uploads', ['metadata'], {
      name: 'idx_document_uploads_metadata',
      using: 'gin',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('document_uploads');
  },
};

