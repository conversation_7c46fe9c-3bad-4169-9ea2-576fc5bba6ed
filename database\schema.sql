-- MySQL Database Schema for Smart Governance User Authentication Module
-- BSCS Mini-Thesis Project - Holy Trinity College
-- Created: November 19, 2025
-- Purpose: Complete database schema for secure user authentication with OTP verification
-- Security: Implements bcrypt password hashing, OTP expiration, and referential integrity
-- Standards: MySQL 8.0+ UUID() function, 3NF normalization, 2025 security best practices

-- Drop existing database if it exists (for clean re-creation)
DROP DATABASE IF EXISTS smart_governance_auth;

-- Create database with UTF8MB4 charset for full Unicode support
CREATE DATABASE smart_governance_auth
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE smart_governance_auth;

-- Table 1: client_accounts
-- Purpose: Store registered user information with secure password storage and verification status
-- Security: Passwords are hashed using bcrypt with cost factor 10+ (never store plain text)
-- Normalization: 3NF compliant - all non-key attributes depend directly on the primary key
CREATE TABLE client_accounts (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()), -- UUID v4 for secure, non-sequential identifiers
    email VARCHAR(255) NOT NULL UNIQUE, -- UNIQUE constraint prevents duplicate registrations
    password_hash VARCHAR(255) NOT NULL, -- bcrypt-hashed password (60+ characters)
    full_name VARCHAR(255) NOT NULL, -- User's full name (2-50 characters as per frontend validation)
    contact_number VARCHAR(50) NULL, -- Optional phone number for future SMS OTP
    is_verified BOOLEAN NOT NULL DEFAULT FALSE, -- Email/OTP verification status
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Account creation timestamp
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP -- Last modification timestamp
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Indexes for client_accounts table
-- PRIMARY KEY index on id (automatically created)
CREATE UNIQUE INDEX idx_client_accounts_email ON client_accounts(email); -- UNIQUE constraint index
CREATE INDEX idx_client_accounts_email_search ON client_accounts(email); -- Fast login queries
CREATE INDEX idx_client_accounts_is_verified ON client_accounts(is_verified); -- Filter verified users

-- Table 2: otp_verification
-- Purpose: Store one-time passwords for email verification and password reset with expiration tracking
-- Security: OTP codes expire after 5 minutes, cannot be reused after verification
-- Relationship: One-to-Many (client_accounts 1:N otp_verification) with CASCADE deletion
CREATE TABLE otp_verification (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()), -- UUID v4 for secure identifiers
    client_id CHAR(36) NOT NULL, -- Foreign key to client_accounts.id
    otp_code VARCHAR(6) NOT NULL, -- 6-digit numeric code (000000-999999)
    expires_at TIMESTAMP NOT NULL, -- Expiration timestamp (typically NOW() + 5 minutes)
    is_used BOOLEAN NOT NULL DEFAULT FALSE, -- Prevents OTP reuse after verification
    purpose ENUM('Registration', 'Password Reset') NOT NULL DEFAULT 'Registration', -- OTP use case
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, -- OTP generation timestamp
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Last modification timestamp
    -- Foreign key constraint with CASCADE deletion for referential integrity
    CONSTRAINT fk_otp_verification_client_id
        FOREIGN KEY (client_id) REFERENCES client_accounts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Indexes for otp_verification table
-- PRIMARY KEY index on id (automatically created)
CREATE INDEX idx_otp_verification_client_id ON otp_verification(client_id); -- Foreign key performance
-- Composite index for fast OTP verification queries (most common operation)
CREATE INDEX idx_otp_verification_code_used_expires ON otp_verification(otp_code, is_used, expires_at);
CREATE INDEX idx_otp_verification_expires_at ON otp_verification(expires_at); -- Cleanup expired OTPs

-- Comments for academic documentation
-- This schema implements the Database Design phase of the Waterfall SDLC model
-- Security measures align with OWASP recommendations for password storage and OTP handling
-- Normalization to 3NF eliminates redundancy and ensures data integrity
-- UUID primary keys prevent enumeration attacks and provide global uniqueness
-- Foreign key constraints with CASCADE ensure referential integrity when users are deleted
