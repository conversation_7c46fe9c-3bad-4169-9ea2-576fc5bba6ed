# Diagram 2: System Architecture Diagram

## Overview
This diagram illustrates the 3-tier architecture of the Smart Governance User Module system, showing the Frontend (React + Vite), Backend (Node.js + Express), and Database (PostgreSQL 16) layers with their interactions and technologies.

---

## Architecture Type
**Pattern:** 3-Tier Architecture (Presentation, Application, Data)  
**Communication:** RESTful API with JSON  
**Authentication:** JWT (JSON Web Tokens)  
**File Storage:** Local File System (Multer)

---

## Mermaid Architecture Diagram

```mermaid
graph TB
    subgraph "CLIENT LAYER - Port 5173"
        A[Web Browser]
        subgraph "React Frontend - Vite Dev Server"
            B[React 18.2+ with TypeScript 5.3+]
            C[React Router 6.20.0<br/>Protected Routes]
            D[Axios 1.6.2<br/>HTTP Client + JWT Interceptors]
            E[React Hook Form 7.48.2<br/>+ Yup 1.3.3 Validation]
            F[Tailwind CSS 3.3.6<br/>+ shadcn/ui Components]
            G[Framer Motion 10.16.5<br/>Animations]
            H[AuthContext<br/>Global State Management]
        end
    end

    subgraph "APPLICATION LAYER - Port 3001"
        I[Express 4.18.2 Server]
        subgraph "Middleware"
            J[CORS Configuration]
            K[JWT Authentication<br/>authenticateToken]
            L[Multer File Upload<br/>5MB limit, PDF/JPG/PNG]
            M[Error Handler]
        end
        subgraph "Routes - 6 Route Files"
            N[/api/auth<br/>Register, Login, Verify OTP]
            O[/api/profile<br/>Get, Update Profile]
            P[/api/business-profile<br/>CRUD Operations]
            Q[/api/applications<br/>CRUD + Submit]
            R[/api/documents<br/>Upload, List, Delete]
            S[/api/notifications<br/>List, Mark Read]
            T[/api/feedback<br/>Submit, Stats]
        end
        subgraph "Controllers - 6 Controller Files"
            U[authController.js<br/>bcrypt + JWT + OTP]
            V[profileController.js<br/>Update User Data]
            W[businessProfileController.js<br/>Business CRUD]
            X[applicationController.js<br/>Application Logic]
            Y[documentController.js<br/>File Handling]
            Z[notificationController.js<br/>Notification Logic]
            AA[feedbackController.js<br/>Feedback Logic]
        end
        subgraph "Models - 7 Sequelize Models"
            AB[ClientAccount.js]
            AC[OtpVerification.js]
            AD[BusinessProfile.js]
            AE[BusinessApplication.js]
            AF[DocumentUpload.js]
            AG[Notification.js]
            AH[FeedbackSubmission.js]
        end
    end

    subgraph "DATA LAYER - Port 5432"
        AI[PostgreSQL 16+ Database]
        subgraph "Database: smart_governance_auth"
            AJ[client_accounts<br/>UUID PK, bcrypt passwords]
            AK[otp_verification<br/>6-digit OTP, 10min expiry]
            AL[business_profiles<br/>Business information]
            AM[business_applications<br/>ENUM status, JSONB metadata]
            AN[document_uploads<br/>File paths, MIME types]
            AO[notifications<br/>ENUM type, is_read flag]
            AP[feedback_submissions<br/>Rating 1-5, CHECK constraint]
        end
    end

    subgraph "FILE STORAGE - Local Disk"
        AQ[uploads/documents/<br/>Multer Local Storage<br/>Unique filenames: timestamp-uuid-original]
    end

    subgraph "EXTERNAL SERVICES"
        AR[Nodemailer 6.9.7<br/>Email Service<br/>console.log for demo]
    end

    %% Client to Frontend
    A -->|HTTP Requests| B
    B --> C
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H

    %% Frontend to Backend
    D -->|REST API Calls<br/>JSON + JWT Token<br/>http://localhost:3001| I

    %% Backend Middleware Flow
    I --> J
    J --> K
    K --> L
    L --> M

    %% Routes to Controllers
    N --> U
    O --> V
    P --> W
    Q --> X
    R --> Y
    S --> Z
    T --> AA

    %% Controllers to Models
    U --> AB
    U --> AC
    V --> AB
    W --> AD
    X --> AE
    Y --> AF
    Z --> AG
    AA --> AH

    %% Models to Database
    AB -->|Sequelize ORM<br/>pg 8.11.3| AI
    AC --> AI
    AD --> AI
    AE --> AI
    AF --> AI
    AG --> AI
    AH --> AI

    %% Database Tables
    AI --> AJ
    AI --> AK
    AI --> AL
    AI --> AM
    AI --> AN
    AI --> AO
    AI --> AP

    %% File Upload Flow
    Y -->|Save Files| AQ
    AF -->|Store Metadata| AN

    %% Email Service
    U -->|Send OTP Email| AR

    %% Styling
    classDef frontend fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef backend fill:#10b981,stroke:#059669,color:#fff
    classDef database fill:#f59e0b,stroke:#d97706,color:#fff
    classDef storage fill:#8b5cf6,stroke:#7c3aed,color:#fff
    classDef external fill:#ef4444,stroke:#dc2626,color:#fff

    class A,B,C,D,E,F,G,H frontend
    class I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z,AA,AB,AC,AD,AE,AF,AG,AH backend
    class AI,AJ,AK,AL,AM,AN,AO,AP database
    class AQ storage
    class AR external
```

---

## Layer Details

### 1. CLIENT LAYER (Presentation Tier)
**Technology:** React 18.2+ with TypeScript 5.3+  
**Build Tool:** Vite 5.0.7  
**Port:** 5173  
**URL:** http://localhost:5173

**Key Components:**
- **React Router:** Client-side routing with protected routes
- **Axios:** HTTP client with JWT token interceptors
- **React Hook Form + Yup:** Form validation and error handling
- **Tailwind CSS + shadcn/ui:** Styling and UI components
- **Framer Motion:** Smooth animations and transitions
- **AuthContext:** Global authentication state management

**Responsibilities:**
- User interface rendering
- Form validation (client-side)
- JWT token storage (localStorage)
- API request handling
- Error display (toast notifications)

---

### 2. APPLICATION LAYER (Business Logic Tier)
**Technology:** Node.js 22+ with Express 4.18.2  
**Language:** TypeScript 5.3.3  
**Port:** 3001  
**URL:** http://localhost:3001

**Middleware Stack:**
1. **CORS:** Cross-Origin Resource Sharing configuration
2. **JWT Authentication:** Token verification for protected routes
3. **Multer:** File upload handling (5MB limit, PDF/JPG/PNG only)
4. **Error Handler:** Centralized error handling

**API Routes (6 route files):**
- `/api/auth` - Authentication endpoints
- `/api/profile` - User profile management
- `/api/business-profile` - Business profile CRUD
- `/api/applications` - Application management
- `/api/documents` - Document upload/management
- `/api/notifications` - Notification system
- `/api/feedback` - Feedback submission

**Controllers (6 controller files):**
- Business logic implementation
- Input validation (server-side)
- Database operations via Sequelize
- Response formatting

**Models (7 Sequelize models):**
- ORM mapping to PostgreSQL tables
- Data validation
- Relationships and associations

**Responsibilities:**
- Request routing
- Authentication and authorization
- Business logic execution
- Data validation (server-side)
- Database operations
- File storage management
- Email sending (OTP)

---

### 3. DATA LAYER (Data Persistence Tier)
**Technology:** PostgreSQL 16+  
**ORM:** Sequelize 6.35.2  
**Port:** 5432  
**Database:** smart_governance_auth

**Tables (7 total):**
1. **client_accounts** - User authentication data
2. **otp_verification** - OTP codes for email verification
3. **business_profiles** - Business information
4. **business_applications** - Permit applications
5. **document_uploads** - File metadata
6. **notifications** - User notifications
7. **feedback_submissions** - User feedback

**PostgreSQL Features:**
- **UUID Primary Keys:** UUIDV4 for all tables
- **ENUM Types:** Status fields, notification types
- **JSONB:** Flexible metadata storage
- **Timestamps:** TIMESTAMP WITH TIME ZONE
- **Indexes:** btree and gin indexes for performance
- **Foreign Keys:** CASCADE constraints for data integrity

**Responsibilities:**
- Data persistence
- Data integrity enforcement
- Query optimization
- Transaction management

---

### 4. FILE STORAGE LAYER
**Technology:** Multer 1.4.5-lts.1  
**Storage Type:** Local File System  
**Location:** `uploads/documents/`

**File Naming Convention:**
```
{timestamp}-{uuid}-{original_filename}
Example: *************-a1b2c3d4-e5f6-7890-abcd-ef1234567890-dti-registration.pdf
```

**Validation:**
- **Allowed Types:** PDF, JPG, PNG
- **Max Size:** 5MB (5,242,880 bytes)
- **Ownership:** User can only upload to their own applications

**Responsibilities:**
- File storage
- File validation
- Unique filename generation
- File deletion (draft applications only)

---

### 5. EXTERNAL SERVICES
**Email Service:** Nodemailer 6.9.7  
**Current Mode:** Console.log (demo mode)  
**Production:** SMTP configuration required

**Usage:**
- OTP email sending during registration
- Future: Password reset emails

---

## Data Flow

### Request Flow (Frontend → Backend → Database)
1. User interacts with React UI
2. React Hook Form validates input (client-side)
3. Axios sends HTTP request with JWT token
4. Express receives request
5. CORS middleware validates origin
6. JWT middleware verifies token
7. Route handler calls controller
8. Controller validates input (server-side)
9. Controller calls Sequelize model
10. Sequelize executes SQL query
11. PostgreSQL processes query
12. Results flow back through layers
13. Frontend displays response

### File Upload Flow
1. User selects file in React UI
2. Axios sends multipart/form-data request
3. Multer middleware validates file
4. Multer saves file to disk
5. Controller creates database record
6. Response sent to frontend

---

## Security Layers

### Frontend Security
- JWT token storage in localStorage
- Protected routes (redirect to login)
- Client-side input validation
- HTTPS (production only)

### Backend Security
- JWT token verification
- bcrypt password hashing (10 rounds)
- File type validation
- File size validation
- SQL injection prevention (Sequelize)
- CORS configuration

### Database Security
- UUID primary keys (non-sequential)
- Foreign key constraints
- CHECK constraints (rating 1-5)
- Timestamps for audit trail

---

**Diagram Status:** ✅ Complete  
**Architecture Pattern:** 3-Tier  
**Total Layers:** 5 (Client, Application, Data, File Storage, External)  
**Last Updated:** November 20, 2025

