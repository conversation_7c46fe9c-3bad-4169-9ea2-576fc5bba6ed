import React, { useState, useRef, useEffect, KeyboardEvent, ClipboardEvent } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { authService } from '@/services/authService'
import { useToast } from '@/hooks/useToast'
import { formatTime } from '@/lib/utils'
import { motion } from 'framer-motion'

interface OTPModalProps {
  isOpen: boolean
  onClose: () => void
  email: string
  onSuccess: () => void
}

export const OTPModal: React.FC<OTPModalProps> = ({ isOpen, onClose, email, onSuccess }) => {
  const [otp, setOtp] = useState<string[]>(Array(6).fill(''))
  const [isLoading, setIsLoading] = useState(false)
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)
  const inputRefs = useRef<(HTMLInputElement | null)[]>([])
  const { toast } = useToast()

  useEffect(() => {
    if (isOpen) {
      setOtp(Array(6).fill(''))
      setCountdown(60)
      setCanResend(false)
      inputRefs.current[0]?.focus()
    }
  }, [isOpen])

  useEffect(() => {
    if (countdown > 0 && isOpen) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else if (countdown === 0) {
      setCanResend(true)
    }
  }, [countdown, isOpen])

  const handleChange = (index: number, value: string) => {
    if (!/^\d*$/.test(value)) return

    const newOtp = [...otp]
    newOtp[index] = value.slice(-1)
    setOtp(newOtp)

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handleKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    } else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus()
    } else if (e.key === 'ArrowRight' && index < 5) {
      inputRefs.current[index + 1]?.focus()
    }
  }

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault()
    const pastedData = e.clipboardData.getData('text/plain').slice(0, 6)
    
    if (!/^\d+$/.test(pastedData)) return

    const newOtp = [...otp]
    pastedData.split('').forEach((char, index) => {
      if (index < 6) {
        newOtp[index] = char
      }
    })
    setOtp(newOtp)

    const nextEmptyIndex = newOtp.findIndex(val => !val)
    const focusIndex = nextEmptyIndex === -1 ? 5 : nextEmptyIndex
    inputRefs.current[focusIndex]?.focus()
  }

  const handleSubmit = async () => {
    const otpValue = otp.join('')
    
    if (otpValue.length !== 6) {
      toast({
        variant: 'destructive',
        title: 'Invalid OTP',
        description: 'Please enter all 6 digits',
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await authService.verifyOTP({ email, otp: otpValue })
      
      toast({
        variant: 'success',
        title: 'Success!',
        description: response.message || 'Account verified successfully',
      })
      
      onSuccess()
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Verification Failed',
        description: error instanceof Error ? error.message : 'Invalid OTP. Please try again.',
      })
      setOtp(Array(6).fill(''))
      inputRefs.current[0]?.focus()
    } finally {
      setIsLoading(false)
    }
  }

  const handleResend = async () => {
    if (!canResend) return

    setIsLoading(true)
    try {
      await authService.resendOTP(email)
      
      toast({
        variant: 'success',
        title: 'OTP Sent',
        description: 'A new OTP has been sent to your email',
      })
      
      setCountdown(60)
      setCanResend(false)
      setOtp(Array(6).fill(''))
      inputRefs.current[0]?.focus()
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to resend OTP',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Verify Your Email</DialogTitle>
          <DialogDescription>
            {/* DEMO MODE: OTP displayed on screen instead of email */}
            Please enter the 6-digit code displayed in the previous notification.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex justify-center gap-2" role="group" aria-label="OTP input">
            {otp.map((digit, index) => (
              <motion.input
                key={index}
                ref={(el) => (inputRefs.current[index] = el)}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={index === 0 ? handlePaste : undefined}
                className="w-12 h-12 text-center text-lg font-semibold border-2 border-input rounded-md focus:border-primary focus:ring-2 focus:ring-ring focus:outline-none transition-all duration-200"
                aria-label={`Digit ${index + 1}`}
                disabled={isLoading}
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: index * 0.05 }}
              />
            ))}
          </div>

          <div className="text-center text-sm text-muted-foreground">
            {canResend ? (
              <button
                onClick={handleResend}
                disabled={isLoading}
                className="text-primary hover:underline font-medium disabled:opacity-50"
              >
                Resend OTP
              </button>
            ) : (
              <span>Resend OTP in {formatTime(countdown)}</span>
            )}
          </div>

          <Button
            onClick={handleSubmit}
            isLoading={isLoading}
            disabled={otp.some(digit => !digit) || isLoading}
            className="w-full"
            size="lg"
          >
            Verify Email
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

