# Diagram 4: User Login Sequence Diagram

## Overview
This sequence diagram shows the complete user login flow including email/password validation, bcrypt password comparison, JWT token generation with 24-hour expiration, and response with user data.

---

## Process Flow
1. User submits login credentials
2. Frontend validates input
3. Backend validates credentials
4. Check email exists
5. Verify email is confirmed
6. Compare password with bcrypt
7. Generate JWT token (24h expiration)
8. Return token and user data
9. Store token in localStorage
10. Redirect to dashboard

---

## Mermaid Sequence Diagram

```mermaid
sequenceDiagram
    actor User
    participant U<PERSON> as React Frontend<br/>(LoginPage)
    participant Form as React Hook Form<br/>+ Yup Validation
    participant Axios as Axios HTTP Client<br/>(authService)
    participant Express as Express Server<br/>(Port 3001)
    participant AuthRoute as /api/auth/login<br/>(authRoutes)
    participant AuthCtrl as authController<br/>(login function)
    participant bcrypt as bcrypt Library<br/>(compare)
    participant JW<PERSON> as jsonwebtoken<br/>(sign)
    participant Model as Sequelize Models<br/>(ClientAccount)
    participant DB as PostgreSQL 16<br/>(client_accounts)
    participant Storage as localStorage<br/>(Browser)

    %% User Input
    User->>UI: Enter email and password
    UI->>Form: Submit login form

    %% Client-side Validation
    Form->>Form: Validate email format
    Form->>Form: Validate password required
    
    alt Validation Fails
        Form-->>UI: Display validation errors
        UI-->>User: Show error messages
    end

    %% API Request
    Form->>Axios: POST /api/auth/login<br/>{email, password}
    Axios->>Express: HTTP POST with JSON body

    %% Backend Routing
    Express->>AuthRoute: Route to /api/auth/login
    AuthRoute->>AuthCtrl: Call login(req, res)

    %% Server-side Validation
    AuthCtrl->>AuthCtrl: Validate request body
    AuthCtrl->>AuthCtrl: Check email and password present

    alt Missing Credentials
        AuthCtrl-->>Express: 400 Bad Request
        Express-->>Axios: {message: "Email and password required"}
        Axios-->>UI: Display error
        UI-->>User: "Please enter email and password"
    end

    %% Find User by Email
    AuthCtrl->>Model: ClientAccount.findOne({<br/>where: {email}})
    Model->>DB: SELECT * FROM client_accounts<br/>WHERE email = ?
    DB-->>Model: User record or null
    Model-->>AuthCtrl: user object or null

    alt User Not Found
        AuthCtrl-->>Express: 401 Unauthorized
        Express-->>Axios: {message: "Invalid credentials"}
        Axios-->>UI: Display error
        UI-->>User: "Invalid email or password"
    end

    %% Check Email Verification
    AuthCtrl->>AuthCtrl: Check user.is_verified

    alt Email Not Verified
        AuthCtrl-->>Express: 403 Forbidden
        Express-->>Axios: {message: "Please verify your email first"}
        Axios-->>UI: Display error
        UI-->>User: "Please verify your email"
    end

    %% Verify Password
    AuthCtrl->>bcrypt: compare(password, user.password_hash)
    Note over bcrypt: Timing-safe comparison<br/>~100ms processing time<br/>Prevents timing attacks
    bcrypt-->>AuthCtrl: boolean (true/false)

    alt Password Incorrect
        AuthCtrl-->>Express: 401 Unauthorized
        Express-->>Axios: {message: "Invalid credentials"}
        Axios-->>UI: Display error
        UI-->>User: "Invalid email or password"
    end

    %% Generate JWT Token
    AuthCtrl->>JWT: sign({<br/>id: user.id,<br/>email: user.email},<br/>JWT_SECRET,<br/>{expiresIn: '24h'})
    Note over JWT: JWT Payload:<br/>{id, email, iat, exp}<br/>Algorithm: HS256<br/>Expiration: 24 hours
    JWT-->>AuthCtrl: token (JWT string)

    %% Prepare User Data
    AuthCtrl->>AuthCtrl: Exclude password_hash from response
    AuthCtrl->>AuthCtrl: Prepare user object:<br/>{id, email, full_name, contact_number}

    %% Success Response
    AuthCtrl-->>Express: 200 OK<br/>{token, user}
    Express-->>Axios: Success response
    Axios-->>UI: {token, user}

    %% Store Token
    UI->>Storage: localStorage.setItem('token', token)
    Storage-->>UI: Token stored

    %% Update Auth Context
    UI->>UI: Update AuthContext<br/>(setUser, setToken)
    UI->>UI: Set Axios default header:<br/>Authorization: Bearer {token}

    %% Navigate to Dashboard
    UI-->>User: "Login successful!"
    UI->>UI: Navigate to /dashboard
    User->>UI: View dashboard

    %% Error Handling
    Note over AuthCtrl,DB: Error Handling:<br/>- Database errors: 500 Internal Server Error<br/>- Invalid credentials: 401 Unauthorized<br/>- Unverified email: 403 Forbidden<br/>- Generic message for security
```

---

## Step-by-Step Breakdown

### Step 1: User Input (Frontend)
- User enters email and password
- React Hook Form manages form state
- Password field type="password" (masked)

### Step 2: Client-Side Validation (Yup Schema)
```typescript
const loginSchema = yup.object({
  email: yup.string().email().required(),
  password: yup.string().required()
});
```

### Step 3: API Request (Axios)
```typescript
POST http://localhost:3001/api/auth/login
Headers: { "Content-Type": "application/json" }
Body: { email, password }
```

### Step 4: Find User by Email
```sql
SELECT * FROM client_accounts WHERE email = '<EMAIL>';
```
- If not found: Return 401 error (generic message for security)

### Step 5: Check Email Verification
```javascript
if (!user.is_verified) {
  return res.status(403).json({ message: "Please verify your email first" });
}
```

### Step 6: Password Verification (bcrypt)
```javascript
const isPasswordValid = await bcrypt.compare(password, user.password_hash);
```
- **Timing-safe comparison:** Prevents timing attacks
- **Time:** ~100ms
- **Result:** true or false

### Step 7: JWT Token Generation
```javascript
const token = jwt.sign(
  { id: user.id, email: user.email },
  process.env.JWT_SECRET,
  { expiresIn: '24h' }
);
```

**JWT Structure:**
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "iat": **********,
    "exp": **********
  },
  "signature": "..."
}
```

### Step 8: Success Response
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "contact_number": "+************",
    "is_verified": true
  }
}
```

### Step 9: Token Storage (Frontend)
```javascript
localStorage.setItem('token', token);
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
```

### Step 10: Navigation
```javascript
navigate('/dashboard');
```

---

## Error Scenarios

| Error Type | HTTP Status | Response Message | Security Note |
|------------|-------------|------------------|---------------|
| Missing credentials | 400 | "Email and password required" | Clear error |
| User not found | 401 | "Invalid credentials" | Generic message |
| Wrong password | 401 | "Invalid credentials" | Generic message |
| Email not verified | 403 | "Please verify your email first" | Specific error |
| Database error | 500 | "Login failed. Please try again." | Generic error |

**Security Note:** User not found and wrong password return the same generic message to prevent user enumeration attacks.

---

## Security Considerations

1. **Timing-Safe Password Comparison:** bcrypt.compare() prevents timing attacks
2. **Generic Error Messages:** "Invalid credentials" for both wrong email and wrong password
3. **JWT Expiration:** 24-hour token lifetime
4. **No Password in Response:** password_hash never sent to client
5. **Email Verification Required:** is_verified must be TRUE
6. **HTTPS Required:** In production (prevents token interception)
7. **Token Storage:** localStorage (consider httpOnly cookies for production)

---

## JWT Token Details

### Token Lifetime
- **Expiration:** 24 hours (86,400 seconds)
- **Issued At (iat):** Current timestamp
- **Expires At (exp):** iat + 24 hours

### Token Usage
```javascript
// Frontend: Include in all API requests
axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

// Backend: Verify on protected routes
const decoded = jwt.verify(token, JWT_SECRET);
```

### Token Refresh
- **Current:** No refresh token (user must re-login after 24h)
- **Future Enhancement:** Implement refresh token mechanism

---

## Database Tables Involved

### client_accounts
- **Operation:** SELECT
- **Fields:** id, email, password_hash, full_name, contact_number, is_verified
- **WHERE Clause:** email = ?

---

## Performance Metrics

- **Client-side validation:** < 10ms
- **Database SELECT:** ~20ms
- **bcrypt comparison:** ~100ms
- **JWT generation:** ~5ms
- **Total time:** ~135-150ms

---

## Authentication Flow After Login

### Protected Route Access
1. User navigates to protected route
2. Frontend checks if token exists in localStorage
3. If no token: Redirect to /login
4. If token exists: Include in Authorization header
5. Backend verifies token with JWT middleware
6. If valid: Allow access
7. If invalid/expired: Return 401, redirect to /login

### Axios Interceptor
```javascript
axios.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

---

**Diagram Status:** ✅ Complete  
**Participants:** 10  
**Steps:** 25+  
**Error Paths:** 5  
**Last Updated:** November 20, 2025

