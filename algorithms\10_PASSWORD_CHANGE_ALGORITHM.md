# Algorithm 10: Password Change with Validation

## Overview
This algorithm handles secure password changes for authenticated users, including current password verification, new password validation, and bcrypt hashing for the Smart Governance system.

---

## Algorithm Complexity
- **Time Complexity:** O(1) - Constant time for database operations and bcrypt
- **Space Complexity:** O(1) - Fixed size data structures
- **Database Operations:** 2 queries (1 SELECT, 1 UPDATE)

---

## Input Parameters
```typescript
interface PasswordChangeInput {
  client_account_id: UUID;       // From JWT token
  current_password: string;      // User's current password
  new_password: string;          // New password (min 8 characters)
  confirm_password: string;      // Password confirmation
}
```

## Output
```typescript
interface PasswordChangeOutput {
  success: boolean;
  message: string;
}
```

---

## Pseudocode

```
ALGORITHM ChangePassword(client_account_id, current_password, new_password, confirm_password)
BEGIN
  // Step 1: Input Validation
  IF current_password is empty THEN
    RETURN error("Current password is required")
  END IF
  
  IF new_password is empty THEN
    RETURN error("New password is required")
  END IF
  
  IF confirm_password is empty THEN
    RETURN error("Password confirmation is required")
  END IF
  
  // Step 2: Validate New Password Length
  IF new_password.length < 8 THEN
    RETURN error("New password must be at least 8 characters")
  END IF
  
  // Step 3: Check Password Confirmation Match
  IF new_password ≠ confirm_password THEN
    RETURN error("New password and confirmation do not match")
  END IF
  
  // Step 4: Check if New Password is Same as Current
  IF new_password = current_password THEN
    RETURN error("New password must be different from current password")
  END IF
  
  // Step 5: Find User Account
  user ← DATABASE.query(
    "SELECT * FROM client_accounts WHERE id = ?",
    client_account_id
  )
  
  IF user NOT EXISTS THEN
    RETURN error(404, "User not found")
  END IF
  
  // Step 6: Verify Current Password
  password_match ← bcrypt.compare(current_password, user.password_hash)
  
  IF NOT password_match THEN
    RETURN error(401, "Current password is incorrect")
  END IF
  
  // Step 7: Hash New Password
  salt ← bcrypt.genSalt(10)
  new_password_hash ← bcrypt.hash(new_password, salt)
  
  // Step 8: Update Password in Database
  DATABASE.update("client_accounts", {
    password_hash: new_password_hash,
    updated_at: CURRENT_TIMESTAMP
  }, WHERE id = client_account_id)
  
  // Step 9: Create Notification (Optional)
  notification_id ← UUID.generate_v4()
  
  DATABASE.insert("notifications", {
    id: notification_id,
    client_account_id: client_account_id,
    type: 'success',
    title: 'Password Changed',
    message: 'Your password has been successfully changed.',
    is_read: FALSE,
    metadata: {},
    created_at: CURRENT_TIMESTAMP
  })
  
  // Step 10: Return Success Response
  RETURN success({
    message: "Password changed successfully"
  })
END
```

---

## Flowchart

```mermaid
flowchart TD
    Start([Start: Change Password]) --> Auth{User<br/>Authenticated?}
    Auth -->|No| ErrorAuth[Return Error 401]
    Auth -->|Yes| Input[Receive: current_password,<br/>new_password, confirm_password]
    Input --> ValidateInput{All Fields<br/>Provided?}
    ValidateInput -->|No| ErrorInput[Return Error:<br/>All fields required]
    ValidateInput -->|Yes| ValidateLength{New Password<br/>≥ 8 chars?}
    ValidateLength -->|No| ErrorLength[Return Error:<br/>Password too short]
    ValidateLength -->|Yes| CheckMatch{New Password =<br/>Confirm Password?}
    CheckMatch -->|No| ErrorMatch[Return Error:<br/>Passwords don't match]
    CheckMatch -->|Yes| CheckSame{New Password ≠<br/>Current Password?}
    CheckSame -->|No| ErrorSame[Return Error:<br/>Must be different]
    CheckSame -->|Yes| FindUser[Query: Find user<br/>by client_account_id]
    FindUser --> UserExists{User<br/>Found?}
    UserExists -->|No| ErrorNotFound[Return Error 404]
    UserExists -->|Yes| VerifyPassword[bcrypt.compare:<br/>Verify current password]
    VerifyPassword --> PasswordCorrect{Current Password<br/>Correct?}
    PasswordCorrect -->|No| ErrorWrong[Return Error 401:<br/>Incorrect password]
    PasswordCorrect -->|Yes| HashNew[Hash new password:<br/>bcrypt with 10 rounds]
    HashNew --> UpdateDB[Update password_hash<br/>in database]
    UpdateDB --> CreateNotif[Create notification:<br/>Password changed]
    CreateNotif --> Success[Return Success:<br/>Password changed]
    Success --> End([End])
    ErrorAuth --> End
    ErrorInput --> End
    ErrorLength --> End
    ErrorMatch --> End
    ErrorSame --> End
    ErrorNotFound --> End
    ErrorWrong --> End
```

---

## Password Requirements

### Minimum Requirements
- **Length:** At least 8 characters
- **Confirmation:** Must match confirm_password field
- **Uniqueness:** Must be different from current password

### Recommended Requirements (Future Enhancement)
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character
- Maximum length: 128 characters

---

## Security Considerations

1. **Current Password Verification:** Prevents unauthorized password changes
2. **bcrypt Hashing:** 10 salt rounds for strong password protection
3. **Password Confirmation:** Prevents typos in new password
4. **Different Password Required:** Prevents reusing current password
5. **No Password in Response:** Never return password in API response
6. **Notification Created:** User alerted of password change
7. **Session Invalidation:** Consider invalidating all sessions (not implemented)

---

## Error Handling

| Error Type | HTTP Status | Message |
|------------|-------------|---------|
| Missing Current Password | 400 | "Current password is required" |
| Missing New Password | 400 | "New password is required" |
| Missing Confirmation | 400 | "Password confirmation is required" |
| Password Too Short | 400 | "New password must be at least 8 characters" |
| Passwords Don't Match | 400 | "New password and confirmation do not match" |
| Same as Current | 400 | "New password must be different from current password" |
| User Not Found | 404 | "User not found" |
| Incorrect Current Password | 401 | "Current password is incorrect" |
| Database Error | 500 | "Failed to change password. Please try again." |

---

## Database Tables Involved

### client_accounts
- **Operations:** SELECT, UPDATE
- **Fields Read:** id, password_hash
- **Fields Updated:** password_hash, updated_at

### notifications
- **Operations:** INSERT
- **Fields:** id, client_account_id, type, title, message, is_read, metadata

---

## Frontend Validation (Yup Schema)

```typescript
const passwordChangeSchema = yup.object({
  current_password: yup.string()
    .required('Current password is required'),
  new_password: yup.string()
    .required('New password is required')
    .min(8, 'Password must be at least 8 characters')
    .notOneOf([yup.ref('current_password')], 'New password must be different'),
  confirm_password: yup.string()
    .required('Password confirmation is required')
    .oneOf([yup.ref('new_password')], 'Passwords must match'),
});
```

---

## Frontend Implementation

### Password Change Form
```typescript
const PasswordChangeForm = () => {
  const { register, handleSubmit, formState: { errors } } = useForm({
    resolver: yupResolver(passwordChangeSchema)
  });
  
  const onSubmit = async (data) => {
    try {
      await profileService.changePassword(data);
      toast.success('Password changed successfully');
      reset();
    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to change password');
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Input
        type="password"
        label="Current Password"
        {...register('current_password')}
        error={errors.current_password?.message}
      />
      <Input
        type="password"
        label="New Password"
        {...register('new_password')}
        error={errors.new_password?.message}
      />
      <Input
        type="password"
        label="Confirm New Password"
        {...register('confirm_password')}
        error={errors.confirm_password?.message}
      />
      <Button type="submit">Change Password</Button>
    </form>
  );
};
```

---

## Implementation Files

- **Controller:** `controllers/profileController.js` - `updateProfile()` function
- **Model:** `models/ClientAccount.js`
- **Route:** `routes/profile.js` - `PUT /api/profile`
- **Middleware:** `middleware/auth.js` - `authenticateToken()`
- **Frontend:** `src/pages/ProfilePage.tsx`
- **Service:** `src/services/profileService.ts`

---

## Future Enhancements

1. **Password Strength Meter:** Visual indicator of password strength
2. **Password History:** Prevent reusing last N passwords
3. **Session Invalidation:** Logout all devices after password change
4. **Email Notification:** Send email alert about password change
5. **Two-Factor Authentication:** Require 2FA for password changes
6. **Password Expiry:** Force password change after X days
7. **Breach Detection:** Check against known breached passwords (Have I Been Pwned API)

---

## Password Strength Validation (Future)

```
FUNCTION CheckPasswordStrength(password)
BEGIN
  score ← 0
  
  IF password.length >= 8 THEN score ← score + 1
  IF password.length >= 12 THEN score ← score + 1
  IF password matches /[a-z]/ THEN score ← score + 1
  IF password matches /[A-Z]/ THEN score ← score + 1
  IF password matches /[0-9]/ THEN score ← score + 1
  IF password matches /[^a-zA-Z0-9]/ THEN score ← score + 1
  
  IF score <= 2 THEN RETURN "Weak"
  ELSE IF score <= 4 THEN RETURN "Medium"
  ELSE RETURN "Strong"
END
```

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

