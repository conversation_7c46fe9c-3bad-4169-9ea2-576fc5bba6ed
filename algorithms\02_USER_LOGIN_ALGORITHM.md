# Algorithm 2: User Login with JWT Authentication

## Overview
This algorithm handles user authentication by verifying credentials, checking email verification status, and generating JWT tokens for session management in the Smart Governance system.

---

## Algorithm Complexity
- **Time Complexity:** O(1) - Constant time for database lookup and bcrypt comparison
- **Space Complexity:** O(1) - Fixed size token and user data
- **Database Operations:** 1 SELECT query (client_accounts)

---

## Input Parameters
```typescript
interface LoginInput {
  email: string;     // User's registered email
  password: string;  // Plain text password
}
```

## Output
```typescript
interface LoginOutput {
  success: boolean;
  message: string;
  token?: string;           // JWT token (24h expiration)
  user?: {
    id: UUID;
    email: string;
    full_name: string;
    is_verified: boolean;
  };
}
```

---

## Pseudocode

```
ALGORITHM UserLogin(email, password)
BEGIN
  // Step 1: Input Validation
  IF email is empty OR password is empty THEN
    RETURN error("Email and password are required")
  END IF
  
  // Step 2: Find User by Email
  user ← DATABASE.query("SELECT * FROM client_accounts WHERE email = ?", email)
  
  IF user NOT EXISTS THEN
    RETURN error("Invalid email or password")
  END IF
  
  // Step 3: Verify Password
  password_match ← bcrypt.compare(password, user.password_hash)
  
  IF NOT password_match THEN
    RETURN error("Invalid email or password")
  END IF
  
  // Step 4: Check Email Verification Status
  IF user.is_verified = FALSE THEN
    RETURN error("Please verify your email before logging in")
  END IF
  
  // Step 5: Generate JWT Token
  token_payload ← {
    id: user.id,
    email: user.email,
    iat: CURRENT_TIMESTAMP
  }
  
  jwt_token ← JWT.sign(token_payload, JWT_SECRET, {
    expiresIn: '24h'
  })
  
  // Step 6: Prepare User Data (exclude sensitive fields)
  user_data ← {
    id: user.id,
    email: user.email,
    full_name: user.full_name,
    contact_number: user.contact_number,
    is_verified: user.is_verified
  }
  
  // Step 7: Return Success Response
  RETURN success({
    message: "Login successful",
    token: jwt_token,
    user: user_data
  })
END
```

---

## Flowchart

```mermaid
flowchart TD
    Start([Start: User Login]) --> Input[Receive: email, password]
    Input --> ValidateInput{Email and<br/>Password<br/>Provided?}
    ValidateInput -->|No| ErrorInput[Return Error:<br/>Email and password required]
    ValidateInput -->|Yes| QueryUser[Query Database:<br/>SELECT user by email]
    QueryUser --> UserFound{User<br/>Exists?}
    UserFound -->|No| ErrorNotFound[Return Error:<br/>Invalid email or password]
    UserFound -->|Yes| ComparePassword[Compare Password:<br/>bcrypt.compare]
    ComparePassword --> PasswordMatch{Password<br/>Matches?}
    PasswordMatch -->|No| ErrorPassword[Return Error:<br/>Invalid email or password]
    PasswordMatch -->|Yes| CheckVerified{Email<br/>Verified?}
    CheckVerified -->|No| ErrorNotVerified[Return Error:<br/>Please verify your email]
    CheckVerified -->|Yes| GenerateToken[Generate JWT Token:<br/>24h expiration]
    GenerateToken --> PrepareUserData[Prepare User Data:<br/>Exclude password_hash]
    PrepareUserData --> SuccessResponse[Return Success:<br/>token + user data]
    SuccessResponse --> End([End])
    ErrorInput --> End
    ErrorNotFound --> End
    ErrorPassword --> End
    ErrorNotVerified --> End
```

---

## Step-by-Step Explanation

### Step 1: Input Validation
- Check if email and password are provided
- Return error if either field is missing
- Prevents unnecessary database queries

### Step 2: Find User by Email
- Query `client_accounts` table using email
- Email is unique, so returns 0 or 1 record
- Use parameterized query to prevent SQL injection

### Step 3: Verify Password
- Use bcrypt.compare() to verify password
- Compares plain text password with stored hash
- Returns same error message as "user not found" for security

### Step 4: Check Email Verification Status
- Verify that `is_verified` field is TRUE
- Prevents unverified users from accessing system
- Prompts user to complete OTP verification

### Step 5: Generate JWT Token
- Create payload with user ID and email
- Sign token with JWT_SECRET from environment
- Set expiration to 24 hours
- Token used for all subsequent API requests

### Step 6: Prepare User Data
- Extract non-sensitive user information
- Exclude password_hash from response
- Include: id, email, full_name, contact_number, is_verified

### Step 7: Return Success Response
- Return JWT token for client storage
- Return user data for UI display
- Client stores token in localStorage

---

## Security Considerations

1. **Password Comparison:** bcrypt.compare() is timing-safe
2. **Generic Error Messages:** Don't reveal if email exists
3. **Email Verification Required:** Prevents unauthorized access
4. **JWT Expiration:** 24-hour limit reduces token theft risk
5. **No Password in Response:** Never return password_hash
6. **HTTPS Required:** Tokens should only be sent over HTTPS in production

---

## Error Handling

| Error Type | HTTP Status | Message |
|------------|-------------|---------|
| Missing Credentials | 400 | "Email and password are required" |
| Invalid Credentials | 401 | "Invalid email or password" |
| Email Not Verified | 403 | "Please verify your email before logging in" |
| Database Error | 500 | "Login failed. Please try again." |

---

## JWT Token Structure

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "id": "550e8400-e29b-41d4-a716-************",
    "email": "<EMAIL>",
    "iat": **********,
    "exp": **********
  },
  "signature": "HMACSHA256(base64UrlEncode(header) + '.' + base64UrlEncode(payload), secret)"
}
```

---

## Database Tables Involved

### client_accounts
- **Operation:** SELECT
- **Fields:** id, email, password_hash, full_name, contact_number, is_verified
- **Index Used:** btree on email (unique)

---

## Frontend Integration

### Token Storage
```typescript
// Store token in localStorage
localStorage.setItem('token', response.token);
localStorage.setItem('user', JSON.stringify(response.user));
```

### API Request Headers
```typescript
// Add token to all subsequent requests
headers: {
  'Authorization': `Bearer ${token}`
}
```

---

## Implementation Files

- **Controller:** `controllers/authController.js` - `login()` function
- **Model:** `models/ClientAccount.js`
- **Route:** `routes/auth.js` - `POST /api/auth/login`
- **Middleware:** `middleware/auth.js` - `authenticateToken()`
- **Frontend:** `src/pages/LoginPage.tsx`
- **Service:** `src/services/authService.ts` - `login()`

---

## Session Management

### Token Validation (Middleware)
```
ALGORITHM AuthenticateToken(request)
BEGIN
  token ← request.headers['authorization'].split(' ')[1]
  
  IF token is empty THEN
    RETURN 401 Unauthorized
  END IF
  
  TRY
    decoded ← JWT.verify(token, JWT_SECRET)
    request.user ← decoded
    CONTINUE to next middleware
  CATCH error
    RETURN 403 Forbidden
  END TRY
END
```

### Token Expiration Handling
- Frontend checks token expiration before requests
- Expired tokens trigger automatic logout
- User redirected to login page
- Token refresh not implemented (user must re-login)

---

**Algorithm Status:** ✅ Implemented and Tested  
**Last Updated:** November 20, 2025  
**Author:** BSCS Mini-Thesis Project - Holy Trinity College

