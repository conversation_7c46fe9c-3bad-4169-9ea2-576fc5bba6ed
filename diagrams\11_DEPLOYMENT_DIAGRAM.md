# Diagram 11: Deployment Diagram

## Overview
This deployment diagram shows the current local development environment setup, including localhost ports, file storage locations, component interactions, and deployment architecture.

---

## Environment
- **Development:** Local machine (localhost)
- **Operating System:** Windows/Linux/macOS
- **Node.js Version:** 22.19.0
- **PostgreSQL Version:** 18 (16+ required)

---

## Mermaid Deployment Diagram

```mermaid
graph TB
    subgraph "Developer Machine (localhost)"
        subgraph "Client Tier - Port 5173"
            Browser[Web Browser<br/>Chrome/Firefox/Edge]
            
            subgraph "React Application"
                Vite[Vite Dev Server<br/>Port: 5173<br/>Hot Module Replacement]
                ReactApp[React App<br/>TypeScript + Tailwind CSS<br/>shadcn/ui Components]
                StaticAssets[Static Assets<br/>Images, CSS, JS]
            end
        end
        
        subgraph "Application Tier - Port 3001"
            subgraph "Node.js Backend"
                Express[Express Server<br/>Port: 3001<br/>Node.js 22.19.0]
                
                subgraph "Middleware"
                    CORS[CORS Middleware<br/>Allow: http://localhost:5173]
                    JWT[JWT Middleware<br/>Verify Bearer Token]
                    MulterMW[Multer Middleware<br/>File Upload Handler]
                    ErrorHandler[Error Handler<br/>Global Error Handling]
                end
                
                subgraph "Routes"
                    AuthRoutes[/api/auth/*<br/>Register, Login, OTP]
                    ProfileRoutes[/api/profile/*<br/>View, Update, Password]
                    BusinessRoutes[/api/business-profiles/*<br/>CRUD Operations]
                    AppRoutes[/api/applications/*<br/>Create, Submit, View]
                    DocRoutes[/api/documents/*<br/>Upload, View, Delete]
                    NotifRoutes[/api/notifications/*<br/>View, Mark Read]
                    FeedbackRoutes[/api/feedback/*<br/>Submit, View Stats]
                end
                
                subgraph "Controllers"
                    Controllers[Controllers<br/>Business Logic<br/>Request/Response Handling]
                end
                
                subgraph "Models (Sequelize ORM)"
                    Models[Sequelize Models<br/>7 Tables<br/>UUID Primary Keys]
                end
            end
        end
        
        subgraph "Data Tier - Port 5432"
            subgraph "PostgreSQL 18"
                PG[PostgreSQL Server<br/>Port: 5432<br/>Database: smart_governance]
                
                subgraph "Database Schema"
                    Tables[7 Tables:<br/>client_accounts<br/>otp_verification<br/>business_profiles<br/>business_applications<br/>document_uploads<br/>notifications<br/>feedback_submissions]
                    
                    Indexes[Indexes:<br/>btree on standard fields<br/>gin on JSONB fields]
                    
                    Constraints[Constraints:<br/>UUID Primary Keys<br/>Foreign Keys with CASCADE<br/>ENUM Types<br/>NOT NULL Constraints]
                end
            end
        end
        
        subgraph "File Storage Tier"
            FileSystem[Local File System<br/>uploads/documents/]
            
            subgraph "Uploaded Files"
                PDFs[PDF Documents<br/>application/pdf]
                Images[Images<br/>image/jpeg, image/png]
                Metadata[File Metadata<br/>Stored in database]
            end
        end
        
        subgraph "External Services"
            Email[Email Service<br/>Nodemailer<br/>Mode: console.log demo]
        end
    end
    
    %% Client to Application Tier
    Browser -->|HTTP Requests<br/>http://localhost:5173| Vite
    Vite --> ReactApp
    ReactApp --> StaticAssets
    
    ReactApp -->|Axios HTTP Requests<br/>http://localhost:3001/api/*<br/>JWT in Authorization header| Express
    
    %% Application Tier Internal
    Express --> CORS
    CORS --> JWT
    JWT --> MulterMW
    MulterMW --> AuthRoutes
    MulterMW --> ProfileRoutes
    MulterMW --> BusinessRoutes
    MulterMW --> AppRoutes
    MulterMW --> DocRoutes
    MulterMW --> NotifRoutes
    MulterMW --> FeedbackRoutes
    
    AuthRoutes --> Controllers
    ProfileRoutes --> Controllers
    BusinessRoutes --> Controllers
    AppRoutes --> Controllers
    DocRoutes --> Controllers
    NotifRoutes --> Controllers
    FeedbackRoutes --> Controllers
    
    Controllers --> Models
    Controllers --> ErrorHandler
    
    %% Application to Data Tier
    Models -->|Sequelize Queries<br/>TCP Connection<br/>Port 5432| PG
    PG --> Tables
    Tables --> Indexes
    Tables --> Constraints
    
    %% Application to File Storage
    MulterMW -->|Save Files<br/>Unique Filenames| FileSystem
    FileSystem --> PDFs
    FileSystem --> Images
    Controllers -->|Store Metadata| Metadata
    
    %% Application to External Services
    Controllers -->|Send OTP Emails<br/>console.log mode| Email
    
    %% Response Flow
    Controllers -->|JSON Response| Express
    Express -->|HTTP Response| ReactApp
    ReactApp -->|Update UI| Browser
    
    %% Styling
    classDef client fill:#3b82f6,stroke:#1e40af,color:#fff
    classDef app fill:#10b981,stroke:#059669,color:#fff
    classDef data fill:#8b5cf6,stroke:#7c3aed,color:#fff
    classDef storage fill:#f59e0b,stroke:#d97706,color:#fff
    classDef external fill:#ef4444,stroke:#dc2626,color:#fff
    
    class Browser,Vite,ReactApp,StaticAssets client
    class Express,CORS,JWT,MulterMW,ErrorHandler,AuthRoutes,ProfileRoutes,BusinessRoutes,AppRoutes,DocRoutes,NotifRoutes,FeedbackRoutes,Controllers,Models app
    class PG,Tables,Indexes,Constraints data
    class FileSystem,PDFs,Images,Metadata storage
    class Email external
```

---

## Port Configuration

### Frontend (Client Tier)
- **Port:** 5173
- **URL:** http://localhost:5173
- **Server:** Vite Dev Server
- **Hot Reload:** Enabled
- **CORS:** Configured to allow requests to port 3001

### Backend (Application Tier)
- **Port:** 3001
- **URL:** http://localhost:3001
- **Server:** Express.js
- **CORS:** Configured to allow requests from port 5173
- **Environment:** Development

### Database (Data Tier)
- **Port:** 5432
- **Host:** localhost
- **Database:** smart_governance
- **User:** postgres (or configured user)
- **Connection:** TCP/IP

---

## File Storage Configuration

### Upload Directory
```
project-root/
└── uploads/
    └── documents/
        ├── 1700500000000-uuid-dti-registration.pdf
        ├── 1700500001000-uuid-barangay-clearance.jpg
        └── ...
```

### File Naming Convention
```
{timestamp}-{uuid}-{original_filename}
```

### Storage Limits
- **Max File Size:** 5MB per file
- **Allowed Types:** PDF, JPG, PNG
- **Storage Location:** Local disk (uploads/documents/)

---

## Network Communication

### Frontend → Backend
```
Protocol: HTTP
Method: GET, POST, PUT, DELETE
URL: http://localhost:3001/api/*
Headers:
  - Content-Type: application/json
  - Authorization: Bearer {JWT_TOKEN}
```

### Backend → Database
```
Protocol: TCP/IP
Port: 5432
Connection Pool: Sequelize managed
Max Connections: 10 (default)
```

### Backend → File System
```
Protocol: File I/O
Path: ./uploads/documents/
Permissions: Read/Write
```

---

## Environment Variables

### Frontend (.env)
```env
VITE_API_URL=http://localhost:3001
```

### Backend (.env)
```env
PORT=3001
DATABASE_URL=postgresql://postgres:password@localhost:5432/smart_governance
JWT_SECRET=your-secret-key-here
JWT_EXPIRES_IN=24h
NODE_ENV=development
```

---

## Deployment Architecture

### Current: Local Development
```
Developer Machine
├── Frontend (Vite) - Port 5173
├── Backend (Express) - Port 3001
├── Database (PostgreSQL) - Port 5432
└── File Storage (Local Disk)
```

### Future: Production Deployment (Recommended)

#### Option 1: Single Server
```
Production Server
├── Frontend (Nginx) - Port 80/443
├── Backend (PM2 + Express) - Port 3001
├── Database (PostgreSQL) - Port 5432
└── File Storage (Local Disk or Cloud)
```

#### Option 2: Separate Servers
```
Frontend Server (Nginx)
└── Static Files - Port 80/443

Backend Server (PM2 + Express)
├── API Server - Port 3001
└── File Storage

Database Server (PostgreSQL)
└── Database - Port 5432
```

#### Option 3: Cloud Deployment
```
Frontend: Vercel/Netlify
Backend: Heroku/Railway/Render
Database: Supabase/Neon/AWS RDS
File Storage: AWS S3/Cloudinary (future)
```

---

## Security Considerations

### Development Environment
- ✅ CORS configured for localhost only
- ✅ JWT authentication required for protected routes
- ✅ File type and size validation
- ✅ SQL injection prevention (Sequelize ORM)
- ⚠️ HTTP only (no HTTPS in development)
- ⚠️ Weak JWT secret (use strong secret in production)

### Production Requirements
- 🔒 HTTPS/TLS encryption
- 🔒 Strong JWT secret (256-bit minimum)
- 🔒 Environment variables in secure vault
- 🔒 Database connection encryption
- 🔒 Rate limiting on API endpoints
- 🔒 File upload virus scanning
- 🔒 CORS restricted to production domain
- 🔒 Helmet.js security headers

---

## Startup Commands

### Start All Services

#### 1. Start PostgreSQL
```bash
# Windows
pg_ctl start -D "C:\Program Files\PostgreSQL\18\data"

# Linux/macOS
sudo service postgresql start
```

#### 2. Start Backend
```bash
cd backend
npm install
npm run dev
# Server running on http://localhost:3001
```

#### 3. Start Frontend
```bash
cd frontend
npm install
npm run dev
# Server running on http://localhost:5173
```

---

## Health Check Endpoints

### Backend Health Check
```
GET http://localhost:3001/api/health
Response: { status: "ok", timestamp: "..." }
```

### Database Connection Check
```
GET http://localhost:3001/api/health/db
Response: { status: "connected", database: "smart_governance" }
```

---

## Monitoring and Logging

### Development Logging
- **Frontend:** Browser console (console.log)
- **Backend:** Terminal console (console.log, console.error)
- **Database:** PostgreSQL logs

### Production Logging (Recommended)
- **Frontend:** Sentry, LogRocket
- **Backend:** Winston, Morgan, PM2 logs
- **Database:** PostgreSQL logs with rotation

---

**Diagram Status:** ✅ Complete  
**Tiers:** 4 (Client, Application, Data, Storage)  
**Ports:** 3 (5173, 3001, 5432)  
**Services:** 7 (Vite, Express, PostgreSQL, Multer, Nodemailer, File System, Browser)  
**Last Updated:** November 20, 2025

