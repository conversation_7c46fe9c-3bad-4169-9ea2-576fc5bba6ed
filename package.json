{"name": "smart-governance-auth-backend", "version": "1.0.0", "description": "MySQL Database Backend for Smart Governance User Authentication Module - BSCS Mini-Thesis Project", "type": "module", "main": "server.cjs", "scripts": {"start": "node server.js", "backend": "node server.js", "backend:dev": "nodemon server.js", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "db:create": "mysql -u root -p < database/schema.sql", "db:test": "mysql -u root -p smart_governance_auth < database/test.sql", "db:seed": "mysql -u root -p smart_governance_auth < database/seeds/sample_users.sql", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "migrate:undo:all": "sequelize-cli db:migrate:undo:all", "seed": "sequelize-cli db:seed:all", "seed:undo": "sequelize-cli db:seed:undo:all"}, "keywords": ["mysql", "authentication", "otp", "sequelize", "bcrypt", "smart-governance", "bscs-thesis"], "author": "BSCS Student - Holy Trinity College", "license": "MIT", "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.2.15", "axios": "^1.6.2", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.3.1", "framer-motion": "^10.16.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "multer": "^2.0.2", "mysql2": "^3.15.3", "nodemailer": "^6.9.7", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.0", "sequelize": "^6.35.2", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "winston": "^3.11.0", "yup": "^1.3.3"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jest": "^29.7.0", "nodemon": "^3.0.2", "postcss": "^8.4.32", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "vite": "^5.0.7"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/smart-governance-auth-backend.git"}, "bugs": {"url": "https://github.com/your-username/smart-governance-auth-backend/issues"}, "homepage": "https://github.com/your-username/smart-governance-auth-backend#readme"}