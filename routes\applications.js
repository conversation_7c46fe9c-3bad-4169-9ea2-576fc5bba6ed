// Application Routes - Business Application Management API
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025
// Purpose: Define API routes for business application operations

const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const {
  getApplications,
  getApplicationById,
  createApplication,
  submitApplication,
  updateApplication,
} = require('../controllers/applicationController');

/**
 * @route   GET /api/applications
 * @desc    Get all applications for authenticated user
 * @access  Private
 */
router.get('/', authenticateToken, getApplications);

/**
 * @route   GET /api/applications/:id
 * @desc    Get single application by ID
 * @access  Private
 */
router.get('/:id', authenticateToken, getApplicationById);

/**
 * @route   POST /api/applications
 * @desc    Create new application
 * @access  Private
 */
router.post('/', authenticateToken, createApplication);

/**
 * @route   PUT /api/applications/:id
 * @desc    Update application (draft only)
 * @access  Private
 */
router.put('/:id', authenticateToken, updateApplication);

/**
 * @route   PUT /api/applications/:id/submit
 * @desc    Submit application for review
 * @access  Private
 */
router.put('/:id/submit', authenticateToken, submitApplication);

module.exports = router;

