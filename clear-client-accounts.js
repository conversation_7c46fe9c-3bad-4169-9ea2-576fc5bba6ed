#!/usr/bin/env node

// Clear Client Accounts Script
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Purpose: Clear all client <NAME_EMAIL>
// Usage: node clear-client-accounts.js

const { Sequelize } = require('sequelize');
const config = require('./config/database');

const environment = process.env.NODE_ENV || 'development';
const dbConfig = config[environment];

console.log(`🗑️ Clearing client accounts in ${environment} environment...`);
console.log(`📊 Database: ${dbConfig.database}`);
console.log(`🌐 Host: ${dbConfig.host}:${dbConfig.port}`);

const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: false,
    pool: dbConfig.pool,
    timezone: dbConfig.timezone,
    dialectOptions: dbConfig.dialectOptions,
  }
);

async function clearClientAccounts() {
  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Get count before deletion
    const [beforeCount] = await sequelize.query(
      'SELECT COUNT(*) as count FROM client_accounts',
      { type: Sequelize.QueryTypes.SELECT }
    );

    console.log(`📊 Found ${beforeCount.count} client accounts before deletion`);

    // Delete all <NAME_EMAIL>
    const [deleteResult] = await sequelize.query(
      'DELETE FROM client_accounts WHERE email != $1',
      {
        bind: ['<EMAIL>'],
        type: Sequelize.QueryTypes.DELETE
      }
    );

    console.log(`🗑️ Deleted ${deleteResult} client accounts`);

    // Get count after deletion
    const [afterCount] = await sequelize.query(
      'SELECT COUNT(*) as count FROM client_accounts',
      { type: Sequelize.QueryTypes.SELECT }
    );

    console.log(`📊 Remaining client accounts: ${afterCount.count}`);

    // Show remaining account if any
    if (afterCount.count > 0) {
      const [remainingAccounts] = await sequelize.query(
        'SELECT id, email, full_name, is_verified, created_at FROM client_accounts',
        { type: Sequelize.QueryTypes.SELECT }
      );

      console.log('👤 Remaining account:');
      remainingAccounts.forEach(account => {
        console.log(`   - ${account.email} (${account.full_name}) - Verified: ${account.is_verified}`);
      });
    }

    console.log('✅ Client accounts cleared successfully');

  } catch (error) {
    console.error('❌ Error clearing client accounts:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the script
clearClientAccounts();
