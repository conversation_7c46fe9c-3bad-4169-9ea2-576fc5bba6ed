import axios, { AxiosError } from 'axios'

const API_URL = import.meta.env.VITE_API_URL || '/api'

console.log('[authService] API_URL configured as:', API_URL)

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // Increased from 10000 to 30000ms
})

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterCredentials {
  email: string
  password: string
  fullName: string
  contactNumber: string
}

export interface VerifyOTPCredentials {
  email: string
  otp: string
}

export interface AuthResponse {
  success: boolean
  message: string
  otpCode?: string // DEMO MODE: OTP code returned for display
  data: {
    token: string
    user: {
      id: string
      email: string
      fullName: string
    }
  }
}

export interface ErrorResponse {
  message: string
  errors?: Record<string, string[]>
}

const handleError = (error: unknown): never => {
  console.error('[authService] handleError called with:', error)
  
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ErrorResponse>
    console.error('[authService] Axios error details:', {
      status: axiosError.response?.status,
      message: axiosError.message,
      code: axiosError.code,
      url: axiosError.config?.url,
    })
    
    if (axiosError.response?.data?.message) {
      console.error('[authService] Backend error message:', axiosError.response.data.message)
      throw new Error(axiosError.response.data.message)
    }
    if (axiosError.message === 'Network Error') {
      throw new Error('Unable to connect to server. Please check your internet connection and that the backend is running on port 3001.')
    }
    if (axiosError.code === 'ECONNABORTED') {
      throw new Error('Request timeout. The server took too long to respond. Please try again.')
    }
    if (axiosError.code === 'ERR_NETWORK') {
      throw new Error(`Cannot reach server at ${API_URL}. Is the backend running?`)
    }
  }
  
  console.error('[authService] Non-axios error:', error)
  throw new Error('An unexpected error occurred. Please try again.')
}

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>('/auth/login', credentials)

      // response.data is the full AuthResponse object: { success, message, data: { token, user } }
      if (response.data?.data?.token && response.data?.data?.user) {
        try {
          const storage = credentials.rememberMe ? localStorage : sessionStorage
          storage.setItem('authToken', response.data.data.token)
          // Always try localStorage for user, fallback to sessionStorage
          try {
            localStorage.setItem('user', JSON.stringify(response.data.data.user))
          } catch (e) {
            sessionStorage.setItem('user', JSON.stringify(response.data.data.user))
          }
        } catch (e) {
          // If primary storage fails, use sessionStorage for both
          try {
            sessionStorage.setItem('authToken', response.data.data.token)
            sessionStorage.setItem('user', JSON.stringify(response.data.data.user))
          } catch (e2) {
            console.error('Storage not available')
          }
        }
      } else {
        console.error('Login response missing required fields', response.data)
        throw new Error('Login response missing token or user data')
      }

      return response.data
    } catch (error) {
      return handleError(error)
    }
  },

  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    try {
      console.log('[authService] Attempting registration to:', `${api.defaults.baseURL}/auth/register`)
      const response = await api.post<AuthResponse>('/auth/register', credentials)
      console.log('[authService] Registration response:', response.data)

      // Store token and user if registration successful
      if (response.data?.data?.token && response.data?.data?.user) {
        try {
          const storage = localStorage // Registration always uses localStorage
          storage.setItem('authToken', response.data.data.token)
          storage.setItem('user', JSON.stringify(response.data.data.user))
          console.log('[authService] Token and user stored successfully')
        } catch (e) {
          console.error('[authService] Storage not available:', e)
        }
      }

      return response.data
    } catch (error) {
      console.error('[authService] Registration error:', error)
      return handleError(error)
    }
  },



  logout(): void {
    localStorage.removeItem('authToken')
    localStorage.removeItem('user')
    sessionStorage.removeItem('authToken')
  },

  getToken(): string | null {
    return localStorage.getItem('authToken') || sessionStorage.getItem('authToken')
  },

  getUser(): { id: string; email: string; fullName: string } | null {
    const userStr = localStorage.getItem('user') || sessionStorage.getItem('user')
    if (!userStr) return null
    try {
      return JSON.parse(userStr)
    } catch {
      return null
    }
  },

  isAuthenticated(): boolean {
    return !!this.getToken()
  },
}

