// Document Service - Document Upload Management
// BSCS Mini-Thesis Project - Holy Trinity College PCMRC
// Created: November 20, 2025

import apiClient from './apiClient';

export interface DocumentUpload {
  id: string;
  application_id: string;
  document_type: string;
  file_path: string;
  file_name: string;
  mime_type: string;
  file_size: number;
  uploaded_at: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export const documentService = {
  /**
   * Upload document for application
   */
  uploadDocument: async (applicationId: string, documentType: string, file: File) => {
    const formData = new FormData();
    formData.append('document', file);
    formData.append('application_id', applicationId);
    formData.append('document_type', documentType);

    const response = await apiClient.post<{ success: boolean; message: string; data: DocumentUpload }>(
      '/documents/upload',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  },

  /**
   * Get documents for application
   */
  getDocumentsByApplication: async (applicationId: string) => {
    const response = await apiClient.get<{ success: boolean; data: DocumentUpload[] }>(
      `/documents/application/${applicationId}`
    );
    return response.data;
  },

  /**
   * Delete document
   */
  deleteDocument: async (id: string) => {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `/documents/${id}`
    );
    return response.data;
  },
};

