// Sequelize Database Models Initialization
// BSCS Mini-Thesis Project - Holy Trinity College
// Created: November 19, 2025
// Purpose: Initialize Sequelize ORM with database configuration and model associations
// Framework: Sequelize v6+ for Node.js backend integration
// Security: Handles database connections and model relationships securely

import { Sequelize } from 'sequelize';
import path from 'path';

// Import database configuration
import dbConfig from '../config/database.js';

// Initialize Sequelize instance based on environment
const env = process.env.NODE_ENV || 'development';
const config = dbConfig[env];

const sequelize = new Sequelize(
  config.database,
  config.username,
  config.password,
  {
    host: config.host,
    port: config.port,
    dialect: config.dialect,
    pool: config.pool,
    logging: config.logging,
    timezone: config.timezone,
  }
);

// Initialize models using ES modules
const ClientAccountFactory = require('./ClientAccount.js');
const OtpVerificationFactory = require('./OtpVerification.js');
const BusinessProfileFactory = require('./BusinessProfile.js');
const BusinessApplicationFactory = require('./BusinessApplication.js');
const DocumentUploadFactory = require('./DocumentUpload.js');
const NotificationFactory = require('./Notification.js');
const FeedbackSubmissionFactory = require('./FeedbackSubmission.js');

const ClientAccount = ClientAccountFactory(sequelize, Sequelize.DataTypes);
const OtpVerification = OtpVerificationFactory(sequelize, Sequelize.DataTypes);
const BusinessProfile = BusinessProfileFactory(sequelize, Sequelize.DataTypes);
const BusinessApplication = BusinessApplicationFactory(sequelize, Sequelize.DataTypes);
const DocumentUpload = DocumentUploadFactory(sequelize, Sequelize.DataTypes);
const Notification = NotificationFactory(sequelize, Sequelize.DataTypes);
const FeedbackSubmission = FeedbackSubmissionFactory(sequelize, Sequelize.DataTypes);

// Define associations (relationships between models)

// OtpVerification associations
// Many-to-One: OtpVerification belongs to ClientAccount
OtpVerification.belongsTo(ClientAccount, {
  foreignKey: 'client_id',
  as: 'clientAccount',
  targetKey: 'id',
});

// BusinessProfile associations
// Many-to-One: BusinessProfile belongs to ClientAccount
BusinessProfile.belongsTo(ClientAccount, {
  foreignKey: 'client_id',
  as: 'clientAccount',
  targetKey: 'id',
});

// One-to-Many: BusinessProfile has many BusinessApplications
BusinessProfile.hasMany(BusinessApplication, {
  foreignKey: 'business_profile_id',
  as: 'applications',
  onDelete: 'CASCADE',
});

// BusinessApplication associations
// Many-to-One: BusinessApplication belongs to BusinessProfile
BusinessApplication.belongsTo(BusinessProfile, {
  foreignKey: 'business_profile_id',
  as: 'businessProfile',
});

// One-to-Many: BusinessApplication has many DocumentUploads
BusinessApplication.hasMany(DocumentUpload, {
  foreignKey: 'application_id',
  as: 'documents',
  onDelete: 'CASCADE',
});

// One-to-Many: BusinessApplication has many FeedbackSubmissions
BusinessApplication.hasMany(FeedbackSubmission, {
  foreignKey: 'application_id',
  as: 'feedbackSubmissions',
  onDelete: 'SET NULL',
});

// DocumentUpload associations
// Many-to-One: DocumentUpload belongs to BusinessApplication
DocumentUpload.belongsTo(BusinessApplication, {
  foreignKey: 'application_id',
  as: 'application',
});

// Notification associations
// Many-to-One: Notification belongs to ClientAccount
Notification.belongsTo(ClientAccount, {
  foreignKey: 'client_id',
  as: 'clientAccount',
});

// FeedbackSubmission associations
// Many-to-One: FeedbackSubmission belongs to ClientAccount
FeedbackSubmission.belongsTo(ClientAccount, {
  foreignKey: 'client_id',
  as: 'clientAccount',
});

// Many-to-One: FeedbackSubmission belongs to BusinessApplication (optional)
FeedbackSubmission.belongsTo(BusinessApplication, {
  foreignKey: 'application_id',
  as: 'application',
});

// Test database connection
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
};

// Export models and sequelize instance for use in application
module.exports = {
  sequelize,
  Sequelize,
  ClientAccount,
  OtpVerification,
  BusinessProfile,
  BusinessApplication,
  DocumentUpload,
  Notification,
  FeedbackSubmission,
  testConnection,
};
