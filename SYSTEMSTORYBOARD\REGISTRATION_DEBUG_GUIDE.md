# Registration Issue - Complete Debug & Fix Guide

## 🔴 Issues Found & Fixed

### **Issue 1: Contact Number Field Name Mismatch** ✅ FIXED
**Problem:** Frontend sends `contactNumber` (camelCase) but backend validation expected specific format handling.
**Solution:** Updated backend to support both `contactNumber` and `contact_number` formats.
**File:** `routes/auth.js` - Lines 35-53

### **Issue 2: API URL Configuration** ✅ VERIFIED
**Status:** Correctly configured in `.env`
```env
VITE_API_URL=http://localhost:3001/api
```
- Frontend will use this URL to connect to backend
- Backend runs on port 3001 ✅

### **Issue 3: Backend Server Not Running** ⚠️ ACTION NEEDED
**Problem:** Exit code 1 errors show backend is not starting
**Solution:** Follow startup instructions below

---

## 🚀 **STEP-BY-STEP STARTUP GUIDE**

### **PREREQUISITE CHECK:**
```bash
# Verify PostgreSQL is running
# Open Task Manager → Services → PostgreSQL is running ✓
# Or open pgAdmin: http://localhost:5050

# Verify Node.js is installed
node --version  # Should be v18 or higher

# Verify npm is installed
npm --version   # Should be v9 or higher
```

---

## **📋 STEP 1: Install Dependencies** (Only if not done)
```bash
cd "C:\Users\<USER>\OneDrive\Desktop\MINI THESIS\TEST 2"
npm install
```

---

## **▶️ STEP 2: Start Backend Server**

### **Option A: Direct Node (Recommended)**
```bash
# Open Terminal 1 (PowerShell or CMD)
cd "C:\Users\<USER>\OneDrive\Desktop\MINI THESIS\TEST 2"
node server.js
```

**Expected Output:**
```
Database connection established successfully.
Database synchronized successfully.
🚀 Smart Governance Auth API Server running on port 3001
📊 Health check available at: http://localhost:3001/health
🔐 Environment: development
```

**If you get errors:**
- ❌ "Cannot find module" → Run `npm install` first
- ❌ "EADDRINUSE: port 3001 already in use" → Kill existing process:
  ```bash
  netstat -ano | findstr :3001
  taskkill /PID <PID> /F
  ```
- ❌ "Database connection error" → Check PostgreSQL is running and `.env` database credentials are correct

### **Option B: Using npm script**
```bash
npm start
```

---

## **▶️ STEP 3: Start Frontend Server**

```bash
# Open Terminal 2 (New Window)
cd "C:\Users\<USER>\OneDrive\Desktop\MINI THESIS\TEST 2"
npm run dev
```

**Expected Output:**
```
VITE v5.0.7  ready in XXX ms

➜  Local:   http://localhost:5173/
➜  Network: use --host to expose
```

---

## **🌐 STEP 4: Access Application**

1. **Frontend:** `http://localhost:5173`
2. **Backend Health:** `http://localhost:3001/health`
3. **API Base:** `http://localhost:3001/api`

---

## **✅ TESTING REGISTRATION FLOW**

### **Test Case 1: Register New Account**

1. Open `http://localhost:5173/register`
2. Fill in the form:
   ```
   Email: <EMAIL>
   Full Name: John Doe
   Contact Number: +************ (or ***********)
   Password: TestPass123!
   Confirm Password: TestPass123!
   ```

3. Click "Create Account"

### **Expected Behavior:**
- ✅ Form validates all fields
- ✅ Loading spinner shows during submission
- ✅ Success toast appears: "Registration Successful!"
- ✅ User auto-redirected to dashboard
- ✅ User details displayed on dashboard

### **If Registration Fails:**

#### **Check 1: Browser Console**
Press `F12` → Open DevTools → Console tab
- Look for JavaScript errors
- Check Network tab → POST /api/auth/register
  - Should see `Status: 201`
  - Response should have `data.token` and `data.user`

#### **Check 2: Backend Console**
Look at Terminal 1 (backend) for error logs:
```
Registration error: [error message here]
```

#### **Check 3: Test API Directly**

Using Postman or curl:
```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123!",
    "fullName": "Test User",
    "contactNumber": "+************"
  }'
```

Expected Response:
```json
{
  "success": true,
  "message": "User registered and verified successfully.",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid-here",
      "email": "<EMAIL>",
      "fullName": "Test User"
    }
  }
}
```

---

## **🔍 DETAILED REGISTRATION FLOW DIAGRAM**

```
USER SUBMITS FORM
        ↓
FRONTEND: RegisterPage.onSubmit()
        ↓
VALIDATION (Yup Schema)
  - Email format ✓
  - Password strength (8+ chars, uppercase, lowercase, number, special) ✓
  - Contact format (Philippines format) ✓
  - Passwords match ✓
        ↓
authService.register() sends POST request
        ↓
HTTP POST: http://localhost:3001/api/auth/register
{
  "email": "<EMAIL>",
  "password": "hashed-by-frontend: no, plain text sent",
  "fullName": "John Doe",
  "contactNumber": "+************"
}
        ↓
BACKEND: routes/auth.js - /register endpoint
        ↓
EXPRESS-VALIDATOR checks:
  - email is valid format ✓
  - password.length >= 8 ✓
  - contactNumber format ✓
        ↓
CHECK DATABASE:
  - User doesn't already exist ✓
        ↓
BCRYPT:
  - Hash password (cost factor: 12)
        ↓
CREATE USER in client_accounts table:
  - id: UUID v4
  - email: <EMAIL>
  - password_hash: $2b$12$...
  - full_name: John Doe
  - contact_number: +************
  - is_verified: true (AUTO-VERIFY)
  - created_at: now
        ↓
JWT TOKEN GENERATION:
  {
    id: user.id,
    email: user.email,
    fullName: user.full_name,
    expiresIn: 24h
  }
        ↓
RESPONSE (201 Created):
{
  "success": true,
  "message": "User registered and verified successfully.",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "fullName": "John Doe"
    }
  }
}
        ↓
FRONTEND receives response
        ↓
STORE IN LOCALSTORAGE:
  - authToken: token
  - user: { id, email, fullName }
        ↓
UPDATE AUTH CONTEXT:
  - isAuthenticated: true
  - user: { id, email, fullName }
        ↓
SHOW SUCCESS TOAST
        ↓
REDIRECT TO /dashboard
        ↓
DASHBOARD displays welcome message
with user's fullName and email
```

---

## **📝 COMMON ERRORS & SOLUTIONS**

### **Error: "Email already exists"**
- **Cause:** User with this email already registered
- **Solution:** Use a different email or delete the user from database:
  ```sql
  DELETE FROM client_accounts WHERE email = '<EMAIL>';
  ```

### **Error: "Contact number must be a valid Philippine mobile number"**
- **Valid formats:**
  - ✅ `+************` (with +63 prefix)
  - ✅ `***********` (with 0 prefix)
  - ❌ `************` (missing prefix)
  - ❌ `+*********` (wrong country code)

### **Error: "Password must include..."**
- **Requirements:**
  - ✅ Minimum 8 characters
  - ✅ At least one uppercase letter (A-Z)
  - ✅ At least one lowercase letter (a-z)
  - ✅ At least one number (0-9)
  - ✅ At least one special character (!@#$%^&*)

### **Error: "Unable to connect to server"**
- **Check:** Backend is running on port 3001
- **Check:** `.env` has `VITE_API_URL=http://localhost:3001/api`
- **Check:** CORS is enabled in server.js

### **Error: "Network Error" or timeout**
- **Check:** Backend server is responding:
  ```bash
  curl http://localhost:3001/health
  ```
- **Check:** Port 3001 is not blocked by firewall
- **Check:** No other service using port 3001

---

## **🛠️ DATABASE VERIFICATION**

### **Connect to PostgreSQL:**
```bash
psql -U postgres -d smart_governance_auth
```

### **Check if user was created:**
```sql
SELECT id, email, full_name, contact_number, is_verified, created_at 
FROM client_accounts 
WHERE email = '<EMAIL>';
```

### **Delete test user (if needed):**
```sql
DELETE FROM client_accounts 
WHERE email = '<EMAIL>';
```

---

## **📊 SUMMARY OF FIXES**

| Issue | Status | File | Line |
|-------|--------|------|------|
| Contact number field name | ✅ FIXED | routes/auth.js | 35-53 |
| API URL configuration | ✅ VERIFIED | .env | 1 |
| Backend not running | ⚠️ ACTION NEEDED | - | Follow startup |
| Token response format | ✅ CORRECT | routes/auth.js | 96 |

---

## **✨ NEXT STEPS**

1. ✅ Backend code is fixed
2. ⏭️ Start backend server with `node server.js`
3. ⏭️ Start frontend with `npm run dev`
4. ⏭️ Test registration at `http://localhost:5173/register`
5. ⏭️ Check browser console and backend logs for any errors

---

**Good luck! 🚀 Your registration system is ready to go!**
